<script setup>
import * as echarts from "echarts"
import { ElMessage } from "element-plus"
import { onMounted, onUnmounted, reactive, ref } from "vue"

// 统计数据
const statisticsData = reactive([
  {
    title: "教师总数",
    value: "156",
    type: "",
    label1: "在职教师",
    value1: "142",
    label2: "兼职教师",
    value2: "14"
  },
  {
    title: "平均课时",
    value: "12.5",
    type: "success",
    label1: "最高课时",
    value1: "18",
    label2: "最低课时",
    value2: "8"
  },
  {
    title: "超负荷教师",
    value: "12",
    type: "warning",
    label1: "严重超负荷",
    value1: "5",
    label2: "轻度超负荷",
    value2: "7"
  },
  {
    title: "课程分布",
    value: "485",
    type: "info",
    label1: "理论课程",
    value1: "325",
    label2: "实践课程",
    value2: "160"
  }
])

// 搜索表单
const searchForm = reactive({
  department: "",
  teacherType: "",
  semester: "",
  loadStatus: ""
})

// 选项数据
const departmentOptions = [
  { value: "cs", label: "计算机科学系" },
  { value: "math", label: "数学系" },
  { value: "physics", label: "物理系" }
]

const teacherTypeOptions = [
  { value: "full", label: "专职教师" },
  { value: "part", label: "兼职教师" }
]

const semesterOptions = [
  { value: "2023-1", label: "2023-2024学年第一学期" },
  { value: "2023-2", label: "2023-2024学年第二学期" }
]

const loadStatusOptions = [
  { value: "high", label: "超负荷" },
  { value: "normal", label: "正常" },
  { value: "low", label: "偏低" }
]

// 图表相关
const chartViewType = ref("week")
let workloadChart = null
let courseTypeChart = null
let teacherAnalysisChart = null

// 表格数据
const teacherLoadData = ref([
  {
    name: "张三",
    department: "计算机科学系",
    title: "教授",
    totalHours: 180,
    weeklyHours: 12,
    courseCount: 3,
    classCount: 4,
    studentCount: 120,
    workloadRate: 85,
    loadStatus: "正常",
    courses: [
      {
        name: "数据结构",
        type: "理论课",
        hours: 64,
        students: 45,
        classroom: "教学楼101",
        schedule: "周一 1-2节"
      }
    ]
  }
  // 更多数据...
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(156)
const tableLoading = ref(false)

// 详情弹窗
const detailVisible = ref(false)
const selectedTeacher = ref(null)
const activeTab = ref("courses")

// 方法：获取课时标签类型
function getHoursTagType(hours) {
  if (hours > 200) return "danger"
  if (hours > 150) return "warning"
  return "success"
}

// 方法：获取工作量状态
function getWorkloadStatus(rate) {
  if (rate > 90) return "exception"
  if (rate > 70) return "warning"
  return "success"
}

// 方法：获取负荷状态类型
function getLoadStatusType(status) {
  const statusMap = {
    超负荷: "danger",
    正常: "success",
    偏低: "info"
  }
  return statusMap[status] || ""
}

// 方法：搜索
function handleSearch() {
  tableLoading.value = true
  // 模拟API请求
  setTimeout(() => {
    tableLoading.value = false
    ElMessage.success("查询成功")
  }, 500)
}

// 方法：重置搜索
function resetSearch() {
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = ""
  })
  handleSearch()
}

// 方法：显示教师详情
function showTeacherDetail(teacher) {
  selectedTeacher.value = teacher
  detailVisible.value = true
  // 在下一个tick更新图表
  setTimeout(() => {
    initTeacherAnalysisChart()
  }, 100)
}

// 方法：导出数据
function exportData() {
  ElMessage.success("数据导出成功")
}

// 方法：分页大小改变
function handleSizeChange(val) {
  pageSize.value = val
  handleSearch()
}

// 方法：当前页改变
function handleCurrentChange(val) {
  currentPage.value = val
  handleSearch()
}

// 方法：初始化工作量图表
function initWorkloadChart() {
  workloadChart = echarts.init(document.querySelector(".chart"))
  workloadChart.setOption({
    tooltip: { trigger: "axis" },
    xAxis: { type: "category", data: ["周一", "周二", "周三", "周四", "周五"] },
    yAxis: { type: "value" },
    series: [{
      data: [12, 8, 10, 6, 8],
      type: "bar"
    }]
  })
}

// 方法：初始化课程类型图表
function initCourseTypeChart() {
  courseTypeChart = echarts.init(document.querySelectorAll(".chart")[1])
  courseTypeChart.setOption({
    tooltip: { trigger: "item" },
    legend: { orient: "vertical", left: "left" },
    series: [{
      type: "pie",
      radius: "50%",
      data: [
        { value: 325, name: "理论课程" },
        { value: 160, name: "实践课程" }
      ]
    }]
  })
}

// 方法：初始化教师分析图表
function initTeacherAnalysisChart() {
  if (!selectedTeacher.value) return

  teacherAnalysisChart = echarts.init(document.querySelector(".detail-chart"))
  teacherAnalysisChart.setOption({
    title: { text: "教师工作量分析" },
    tooltip: { trigger: "axis" },
    legend: { data: ["课时数", "学生数"] },
    xAxis: { type: "category", data: ["周一", "周二", "周三", "周四", "周五"] },
    yAxis: [
      { type: "value", name: "课时数" },
      { type: "value", name: "学生数" }
    ],
    series: [
      {
        name: "课时数",
        type: "bar",
        data: [4, 6, 4, 8, 6]
      },
      {
        name: "学生数",
        type: "line",
        yAxisIndex: 1,
        data: [120, 160, 120, 180, 140]
      }
    ]
  })
}

// 生命周期钩子
onMounted(() => {
  handleSearch()
  initWorkloadChart()
  initCourseTypeChart()

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize)
})

onUnmounted(() => {
  window.removeEventListener("resize", handleResize)
  workloadChart?.dispose()
  courseTypeChart?.dispose()
  teacherAnalysisChart?.dispose()
})

// 方法：处理窗口大小变化
function handleResize() {
  workloadChart?.resize()
  courseTypeChart?.resize()
  teacherAnalysisChart?.resize()
}
</script>

<template>
  <div class="teacher-load-analysis">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="6" v-for="(item, index) in statisticsData" :key="index">
        <el-card shadow="hover" class="statistic-card">
          <template #header>
            <div class="card-header">
              <span>{{ item.title }}</span>
              <el-tag :type="item.type">
                {{ item.value }}
              </el-tag>
            </div>
          </template>
          <div class="card-body">
            <div class="data-item">
              <span>{{ item.label1 }}</span>
              <span class="value">{{ item.value1 }}</span>
            </div>
            <div class="data-item">
              <span>{{ item.label2 }}</span>
              <span class="value">{{ item.value2 }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索条件 -->
    <el-card class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="院系" style="width: 200px;">
          <el-select v-model="searchForm.department" placeholder="选择院系" clearable>
            <el-option
              v-for="dept in departmentOptions"
              :key="dept.value"
              :label="dept.label"
              :value="dept.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="教师类型" style="width: 200px;">
          <el-select v-model="searchForm.teacherType" placeholder="选择教师类型" clearable>
            <el-option
              v-for="type in teacherTypeOptions"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学期" style="width: 200px;">
          <el-select v-model="searchForm.semester" placeholder="选择学期" clearable>
            <el-option
              v-for="sem in semesterOptions"
              :key="sem.value"
              :label="sem.label"
              :value="sem.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="负荷状态" style="width: 200px;">
          <el-select v-model="searchForm.loadStatus" placeholder="选择负荷状态" clearable>
            <el-option
              v-for="status in loadStatusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin-left: 20px;">
          <el-button type="primary" @click="handleSearch">
            查询
          </el-button>
          <el-button @click="resetSearch">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图表分析区域 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>教师课时负荷分布</span>
              <el-radio-group v-model="chartViewType" size="small">
                <el-radio-button label="week">
                  周视图
                </el-radio-button>
                <el-radio-button label="month">
                  月视图
                </el-radio-button>
                <el-radio-button label="semester">
                  学期视图
                </el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="workloadChart" class="chart" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>课程类型分布</span>
            </div>
          </template>
          <div ref="courseTypeChart" class="chart" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 教师负荷详情表格 -->
    <el-card class="table-section">
      <template #header>
        <div class="card-header">
          <span>教师负荷详情</span>
          <div class="header-actions">
            <el-button type="success" @click="exportData">
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="teacherLoadData"
        border
        v-loading="tableLoading"
        style="width: 100%"
      >
        <el-table-column prop="name" label="教师姓名" width="120" />
        <el-table-column prop="department" label="所属院系" width="150" />
        <el-table-column prop="title" label="职称" width="100" />
        <el-table-column prop="totalHours" label="总课时" width="100">
          <template #default="scope">
            <el-tag :type="getHoursTagType(scope.row.totalHours)">
              {{ scope.row.totalHours }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="weeklyHours" label="周课时" width="100" />
        <el-table-column prop="courseCount" label="课程数" width="100" />
        <el-table-column prop="classCount" label="班级数" width="100" />
        <el-table-column prop="studentCount" label="学生总数" width="120" />
        <el-table-column prop="workloadRate" label="工作量比例" width="180">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.workloadRate"
              :status="getWorkloadStatus(scope.row.workloadRate)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="loadStatus" label="负荷状态" width="100">
          <template #default="scope">
            <el-tag :type="getLoadStatusType(scope.row.loadStatus)">
              {{ scope.row.loadStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="showTeacherDetail(scope.row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 教师详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="教师负荷详情"
      width="70%"
      destroy-on-close
    >
      <template v-if="selectedTeacher">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="教师姓名">
            {{ selectedTeacher.name }}
          </el-descriptions-item>
          <el-descriptions-item label="所属院系">
            {{ selectedTeacher.department }}
          </el-descriptions-item>
          <el-descriptions-item label="职称">
            {{ selectedTeacher.title }}
          </el-descriptions-item>
          <el-descriptions-item label="总课时">
            {{ selectedTeacher.totalHours }}
          </el-descriptions-item>
          <el-descriptions-item label="周课时">
            {{ selectedTeacher.weeklyHours }}
          </el-descriptions-item>
          <el-descriptions-item label="负荷状态">
            <el-tag :type="getLoadStatusType(selectedTeacher.loadStatus)">
              {{ selectedTeacher.loadStatus }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="课程信息" name="courses">
            <el-table :data="selectedTeacher.courses" border>
              <el-table-column prop="name" label="课程名称" />
              <el-table-column prop="type" label="课程类型" />
              <el-table-column prop="hours" label="课时" />
              <el-table-column prop="students" label="学生数" />
              <el-table-column prop="classroom" label="教室" />
              <el-table-column prop="schedule" label="上课时间" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="负荷分析" name="analysis">
            <div ref="teacherAnalysisChart" class="detail-chart" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.teacher-load-analysis {
  padding: 20px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.statistic-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-body {
  padding: 10px 0;
}

.data-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.value {
  font-weight: bold;
}

.search-section {
  margin-bottom: 20px;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart {
  height: 300px;
}

.table-section {
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.detail-tabs {
  margin-top: 20px;
}

.detail-chart {
  height: 400px;
  margin-top: 20px;
}

:deep(.el-card__body) {
  padding: 15px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
}
</style>
