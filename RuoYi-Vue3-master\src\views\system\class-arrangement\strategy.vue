<template>
  <div class="strategy-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>排课策略配置</span>
          <el-space>
            <el-button type="primary" @click="showCreateModal">
              <el-icon><Plus /></el-icon>
              新建策略
            </el-button>
          </el-space>
        </div>
      </template>

      <!-- 策略列表 -->
      <el-table
        :data="strategies"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="name" label="策略名称" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="updateTime" label="最后修改时间" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'info'">
              {{ row.status === 'active' ? '启用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="{ row }">
            <el-space>
              <el-button type="primary" link @click="editStrategy(row)">编辑</el-button>
              <el-button
                type="primary"
                link
                @click="toggleStatus(row)"
                :disabled="row.status === 'active'"
              >
                {{ row.status === 'active' ? '停用' : '启用' }}
              </el-button>
              <el-popconfirm
                title="确定要删除这个策略吗？"
                @confirm="deleteStrategy(row.id)"
              >
                <template #reference>
                  <el-button type="danger" link>删除</el-button>
                </template>
              </el-popconfirm>
              <el-button type="primary" link @click="loadStrategyHistory(row)">
                <el-icon><Timer /></el-icon>
                历史版本
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>

      <!-- 策略编辑/创建对话框 -->
      <el-dialog
        v-model="modalVisible"
        :title="modalTitle"
        width="800px"
      >
        <el-form
          ref="strategyFormRef"
          :model="currentStrategy"
          :rules="rules"
          label-position="top"
        >
          <!-- 基本信息 -->
          <el-form-item label="策略名称" prop="name">
            <el-input v-model="currentStrategy.name" placeholder="请输入策略名称" />
          </el-form-item>
          <el-form-item label="策略描述" prop="description">
            <el-input
              v-model="currentStrategy.description"
              type="textarea"
              placeholder="请输入策略描述"
            />
          </el-form-item>

          <!-- 多维度策略设置 -->
          <el-divider>策略配置</el-divider>

          <!-- 教师授课地点集中设置 -->
          <el-form-item label="教师授课地点集中">
            <el-switch v-model="currentStrategy.teacherLocationCentralized" />
            <el-input-number
              v-if="currentStrategy.teacherLocationCentralized"
              v-model="currentStrategy.maxTeacherLocations"
              :min="1"
              :max="10"
              class="ml-4"
            >
              <template #suffix>个教室</template>
            </el-input-number>
          </el-form-item>

          <!-- 教室容量匹配设置 -->
          <el-form-item label="教室容量匹配">
            <el-select v-model="currentStrategy.roomCapacityMatch" style="width: 200px">
              <el-option label="严格匹配" value="strict" />
              <el-option label="弹性匹配" value="flexible" />
              <el-option label="忽略容量" value="ignore" />
            </el-select>
          </el-form-item>

          <!-- 时间约束设置 -->
          <el-form-item label="时间约束">
            <el-checkbox-group v-model="currentStrategy.timeConstraints">
              <el-checkbox label="morning">上午优先</el-checkbox>
              <el-checkbox label="afternoon">下午优先</el-checkbox>
              <el-checkbox label="evening">晚上限制</el-checkbox>
              <el-checkbox label="continuous">连续排课优先</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <!-- 特殊课程显示设置 -->
          <el-form-item label="特殊课程显示">
            <el-checkbox-group v-model="currentStrategy.specialCourseDisplay">
              <el-checkbox label="experiment">实验课程标记</el-checkbox>
              <el-checkbox label="pe">体育课程标记</el-checkbox>
              <el-checkbox label="multiType">多学时课程标记</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <!-- 策略优先级设置 -->
          <el-divider>优先级设置</el-divider>
          <el-form-item label="策略优先级">
            <el-table :data="currentStrategy.priorities" style="width: 100%">
              <el-table-column prop="type" label="策略类型" />
              <el-table-column label="优先级(1-10)" width="200">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.priority"
                    :min="1"
                    :max="10"
                    style="width: 130px"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="modalVisible = false">取消</el-button>
            <el-button type="primary" @click="handleModalOk">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 历史版本抽屉 -->
      <el-drawer
        v-model="historyDrawerVisible"
        title="策略历史版本"
        direction="rtl"
        size="650px"
        :destroy-on-close="false"
        :before-close="handleHistoryDrawerClose"
      >
        <template #header>
          <div class="drawer-header">
            <h3 class="drawer-title">{{ currentHistoryStrategy.name }} 的历史版本</h3>
            <el-tag type="info" size="small">共 {{ strategyHistory.length }} 个历史记录</el-tag>
          </div>
        </template>
        
        <div class="history-actions-bar">
          <div class="history-filter">
            <el-select v-model="historyFilter" placeholder="筛选操作类型" style="width: 150px" clearable>
              <el-option label="所有操作" value="" />
              <el-option label="创建" value="create" />
              <el-option label="更新" value="update" />
              <el-option label="删除" value="delete" />
            </el-select>
            
            <el-date-picker
              v-model="historyDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              style="margin-left: 10px; width: 260px"
              clearable
            />
          </div>
          
          <el-button type="primary" plain size="small" @click="refreshHistory">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        
        <div v-if="filteredHistory.length === 0" class="empty-history">
          <el-empty description="暂无历史记录" />
        </div>
        
        <el-scrollbar height="calc(100% - 60px)" v-else>
          <el-timeline>
            <el-timeline-item
              v-for="history in filteredHistory"
              :key="history.id"
              :type="getHistoryTypeColor(history.type)"
              :timestamp="history.createTime"
              size="large"
            >
              <div 
                class="history-item" 
                :class="{ 
                  'current-version': history.isCurrent,
                  'restored-version': history.id === lastRestoredId && showRestoreAnimation
                }"
              >
                <div class="history-header">
                  <div class="history-operator">
                    <el-avatar :size="24" :src="history.operatorAvatar">{{ history.operator.substr(0, 1) }}</el-avatar>
                    <span class="history-user">{{ history.operator }}</span>
                    <el-tag size="small" :type="getHistoryTypeTag(history.type)" class="ml-2">
                      {{ getHistoryTypeLabel(history.type) }}
                    </el-tag>
                    <el-tag size="small" :type="history.isCurrent ? 'success' : 'info'" class="ml-2">
                      {{ history.isCurrent ? '当前使用版本' : '历史版本' }}
                    </el-tag>
                  </div>
                  <div class="history-version-info">
                    版本 #{{ history.id }}
                  </div>
                </div>
                
                <div class="history-content">
                  <p>{{ history.description }}</p>
                </div>
                
                <div class="history-actions">
                  <el-button
                    v-if="history.type !== 'delete' && !history.isCurrent"
                    type="primary"
                    size="small"
                    @click="showRestoreConfirm(history)"
                  >
                    <el-icon><RefreshLeft /></el-icon>
                    还原此版本
                  </el-button>
                  <el-button
                    v-if="history.type !== 'delete'"
                    type="info"
                    size="small"
                    @click="viewVersionDetail(history)"
                  >
                    <el-icon><View /></el-icon>
                    查看详情
                  </el-button>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-scrollbar>
      </el-drawer>

      <!-- 版本详情对话框 -->
      <el-dialog
        v-model="versionDetailVisible"
        title="策略版本详情"
        width="700px"
      >
        <div v-loading="versionDetailLoading">
          <h3>{{ versionDetail.name }} <small>(版本 #{{ versionDetail.id }})</small></h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="创建时间">{{ versionDetail.createTime }}</el-descriptions-item>
            <el-descriptions-item label="操作人">{{ versionDetail.operator }}</el-descriptions-item>
            <el-descriptions-item label="操作类型" :span="2">
              <el-tag :type="getHistoryTypeTag(versionDetail.type)">
                {{ getHistoryTypeLabel(versionDetail.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="策略描述" :span="2">
              {{ versionDetail.description || '无描述' }}
            </el-descriptions-item>
          </el-descriptions>
          
          <el-divider>策略配置详情</el-divider>
          
          <el-collapse accordion>
            <el-collapse-item title="基本配置" name="1">
              <div class="detail-items">
                <div class="detail-item">
                  <span class="detail-label">教师授课地点集中:</span>
                  <span class="detail-value">{{ versionDetail.teacherLocationCentralized ? '是' : '否' }}</span>
                </div>
                <div class="detail-item" v-if="versionDetail.teacherLocationCentralized">
                  <span class="detail-label">最大教室数量:</span>
                  <span class="detail-value">{{ versionDetail.maxTeacherLocations }}个</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">教室容量匹配:</span>
                  <span class="detail-value">{{ getRoomCapacityLabel(versionDetail.roomCapacityMatch) }}</span>
                </div>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="时间约束设置" name="2">
              <div class="detail-items">
                <div class="detail-item">
                  <span class="detail-label">时间约束:</span>
                  <div class="detail-value">
                    <el-tag 
                      v-for="constraint in versionDetail.timeConstraints" 
                      :key="constraint" 
                      type="info" 
                      size="small" 
                      class="mr-2"
                    >
                      {{ getConstraintLabel(constraint) }}
                    </el-tag>
                    <span v-if="!versionDetail.timeConstraints || versionDetail.timeConstraints.length === 0">
                      无时间约束
                    </span>
                  </div>
                </div>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="特殊课程显示" name="3">
              <div class="detail-items">
                <div class="detail-item">
                  <span class="detail-label">特殊课程标记:</span>
                  <div class="detail-value">
                    <el-tag 
                      v-for="display in versionDetail.specialCourseDisplay" 
                      :key="display" 
                      type="success" 
                      size="small" 
                      class="mr-2"
                    >
                      {{ getSpecialCourseLabel(display) }}
                    </el-tag>
                    <span v-if="!versionDetail.specialCourseDisplay || versionDetail.specialCourseDisplay.length === 0">
                      无特殊课程标记
                    </span>
                  </div>
                </div>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="优先级设置" name="4">
              <el-table :data="versionDetail.priorities || []" border style="width: 100%">
                <el-table-column prop="type" label="策略类型" />
                <el-table-column prop="priority" label="优先级">
                  <template #default="{ row }">
                    <el-rate
                      v-model="row.priority"
                      :max="10"
                      disabled
                      show-score
                      text-color="#ff9900"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="versionDetailVisible = false">关闭</el-button>
            <el-button 
              v-if="!versionDetail.isCurrent && versionDetail.type !== 'delete'"
              type="primary" 
              @click="restoreFromDetail"
            >
              还原此版本
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 还原确认对话框 -->
      <el-dialog
        v-model="restoreConfirmVisible"
        title="确认还原"
        width="500px"
      >
        <div class="restore-confirm">
          <el-alert
            title="请确认版本还原操作"
            type="warning"
            description="还原操作将覆盖当前策略的所有设置，此操作不可撤销。"
            show-icon
            :closable="false"
            class="mb-4"
          />
          
          <p>您确定要将策略 <strong>{{ currentHistoryStrategy.name }}</strong> 还原到版本 <strong>#{{ restoreVersionData.id }}</strong> 吗？</p>
          
          <el-descriptions title="还原版本信息" :column="1" border size="small">
            <el-descriptions-item label="版本ID">{{ restoreVersionData.id }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ restoreVersionData.createTime }}</el-descriptions-item>
            <el-descriptions-item label="操作人">{{ restoreVersionData.operator }}</el-descriptions-item>
            <el-descriptions-item label="版本描述">{{ restoreVersionData.description }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="restoreConfirmVisible = false">取消</el-button>
            <el-button type="danger" @click="confirmRestore">确认还原</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { Plus, Timer, InfoFilled, ArrowRight, RefreshLeft, View, DocumentCopy, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getStrategies as getStrategiesApi, 
  addStrategy as addStrategyApi, 
  updateStrategy as updateStrategyApi, 
  updateStrategyStatus as updateStrategyStatusApi,
   deleteStrategy as deleteStrategyApi,
   getStrategyHistory as getStrategyHistoryApi, 
   restoreStrategyVersion as restoreStrategyVersionApi,
   getStrategyVersionDetail as getStrategyVersionDetailApi,
   compareStrategyVersions as compareStrategyVersionsApi
} from '@/api/system/strategy'

// 表单引用
const strategyFormRef = ref(null)

// 状态定义
const loading = ref(false)
const modalVisible = ref(false)
const modalTitle = ref('新建策略')
const historyDrawerVisible = ref(false)
const strategies = ref([])
const strategyHistory = ref([])
const currentHistoryStrategy = reactive({
  id: '',
  name: '',
})

// 历史版本筛选相关
const historyFilter = ref('')
const historyDateRange = ref(null)
const filteredHistory = computed(() => {
  if (!strategyHistory.value || strategyHistory.value.length === 0) {
    return []
  }
  
  let result = [...strategyHistory.value]
  
  // 根据类型筛选
  if (historyFilter.value) {
    result = result.filter(history => history.type === historyFilter.value)
  }
  
  // 根据日期范围筛选
  if (historyDateRange.value && historyDateRange.value.length === 2) {
    const startDate = new Date(historyDateRange.value[0])
    const endDate = new Date(historyDateRange.value[1])
    endDate.setHours(23, 59, 59, 999) // 设置为当天结束
    
    result = result.filter(history => {
      const historyDate = new Date(history.createTime)
      return historyDate >= startDate && historyDate <= endDate
    })
  }
  
  return result
})

// 版本详情对话框
const versionDetailVisible = ref(false)
const versionDetailLoading = ref(false)
const versionDetail = reactive({
  id: '',
  name: '',
  type: '',
  operator: '',
  createTime: '',
  description: '',
  teacherLocationCentralized: false,
  maxTeacherLocations: 3,
  roomCapacityMatch: 'strict',
  timeConstraints: [],
  specialCourseDisplay: [],
  priorities: [],
  isCurrent: false
})

// 版本对比对话框
const versionCompareVisible = ref(false)
const versionCompareLoading = ref(false)
const versionDifferences = ref([])
const compareVersionsData = reactive({
  newer: {},
  older: {}
})

// 还原确认对话框
const restoreConfirmVisible = ref(false)
const restoreVersionData = reactive({
  id: '',
  createTime: '',
  operator: '',
  description: ''
})

// 还原动画控制
const lastRestoredId = ref('')
const showRestoreAnimation = ref(false)

// 获取历史记录类型颜色
const getHistoryTypeColor = (type) => {
  switch (type) {
    case 'create': return 'success'
    case 'update': return 'primary'
    case 'delete': return 'danger'
    default: return 'info'
  }
}

// 获取历史记录类型标签
const getHistoryTypeTag = (type) => {
  switch (type) {
    case 'create': return 'success'
    case 'update': return 'primary'
    case 'delete': return 'danger'
    default: return 'info'
  }
}

// 获取历史记录类型标签文本
const getHistoryTypeLabel = (type) => {
  switch (type) {
    case 'create': return '创建'
    case 'update': return '更新'
    case 'delete': return '删除'
    default: return '未知'
  }
}

// 获取容量匹配标签
const getRoomCapacityLabel = (type) => {
  switch (type) {
    case 'strict': return '严格匹配'
    case 'flexible': return '弹性匹配'
    case 'ignore': return '忽略容量'
    default: return '未知'
  }
}

// 获取时间约束标签
const getConstraintLabel = (constraint) => {
  switch (constraint) {
    case 'morning': return '上午优先'
    case 'afternoon': return '下午优先'
    case 'evening': return '晚上限制'
    case 'continuous': return '连续排课优先'
    default: return constraint
  }
}

// 获取特殊课程标签
const getSpecialCourseLabel = (course) => {
  switch (course) {
    case 'experiment': return '实验课程标记'
    case 'pe': return '体育课程标记'
    case 'multiType': return '多学时课程标记'
    default: return course
  }
}

// 格式化对比值
const formatCompareValue = (value) => {
  if (value === undefined || value === null) {
    return '无'
  }
  
  if (Array.isArray(value)) {
    if (value.length === 0) return '无'
    return value.map(item => {
      if (typeof item === 'object') {
        return JSON.stringify(item)
      }
      return item
    }).join(', ')
  }
  
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  
  return value.toString()
}

// 获取上一个版本
const prevVersion = (history) => {
  const index = strategyHistory.value.findIndex(h => h.id === history.id)
  if (index > 0 && index < strategyHistory.value.length) {
    return strategyHistory.value[index + 1]
  }
  return null
}

// 处理抽屉关闭
const handleHistoryDrawerClose = (done) => {
  historyFilter.value = ''
  historyDateRange.value = null
  done()
}

// 安全解析JSON数据的工具函数
const safeParseJSON = (jsonStr, defaultValue = []) => {
  if (!jsonStr) return defaultValue;
  if (Array.isArray(jsonStr)) return jsonStr;
  
  try {
    return typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;
  } catch (e) {
    console.error('解析JSON失败:', e, jsonStr);
    return defaultValue;
  }
};

// 安全序列化JSON数据的工具函数
const safeStringifyJSON = (data, defaultValue = '[]') => {
  if (!data) return defaultValue;
  
  try {
    return typeof data === 'string' ? data : JSON.stringify(data);
  } catch (e) {
    console.error('序列化JSON失败:', e, data);
    return defaultValue;
  }
};

// 加载策略历史版本
const loadStrategyHistory = async (record) => {
  historyDrawerVisible.value = true
  currentHistoryStrategy.id = record.id
  currentHistoryStrategy.name = record.name
  
  try {
    const res = await getStrategyHistoryApi(record.id)
    console.log(res)
    
    // 为历史记录添加标记当前使用版本
    let historyData = res.data || mockHistory
    
    // 将历史记录按时间排序（新的在前）
    historyData.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
    
    // 标记当前使用版本
    const currentVersionId = record.currentVersionId || historyData[0]?.id
    historyData = historyData.map(item => {
      // 安全处理changes字段
      let changes = item.changes;
      try {
        changes = typeof item.changes === 'string' ? 
          safeParseJSON(item.changes) : 
          (item.changes || getExampleChanges(item.type));
      } catch (e) {
        console.error('处理changes失败:', e);
        changes = getExampleChanges(item.type);
      }
      
      return {
        ...item,
        isCurrent: item.id === currentVersionId,
        // 如果没有avatar，使用默认值
        operatorAvatar: item.operatorAvatar || '',
        // 确保changes是数组
        changes: Array.isArray(changes) ? changes : []
      }
    })
    
    strategyHistory.value = historyData
  } catch (error) {
    // 使用mock数据
    const historyData = mockHistory.map(item => ({
      ...item,
      isCurrent: item.id === '1', // 假设第一个是当前版本
      operatorAvatar: '',
      changes: getExampleChanges(item.type)
    }))
    strategyHistory.value = historyData
    console.error('获取策略历史版本失败:', error)
  }
}

// 获取示例变更信息（实际应从后端获取）
const getExampleChanges = (type) => {
  if (type === 'create') {
    return [];
  } else if (type === 'update') {
    return [
      { field: '教师地点集中', oldValue: '否', newValue: '是' },
      { field: '最大教室数量', oldValue: '5', newValue: '3' },
      { field: '时间约束', oldValue: '上午优先', newValue: '上午优先, 连续排课优先' }
    ];
  }
  return [];
}

// 显示版本详情
const viewVersionDetail = async (record) => {
  versionDetailVisible.value = true
  versionDetailLoading.value = true
  
  try {
    // 实际应从后端获取详细版本信息
    const res = await getStrategyVersionDetailApi(record.id)
    console.log(res)
    const data = JSON.parse(res.data.snapshot)
    console.log(data)
    Object.assign(versionDetail, data)
    versionDetail.priorities = safeParseJSON(data.priorities)
    versionDetail.timeConstraints = safeParseJSON(data.timeConstraints)
    versionDetail.specialCourseDisplay = safeParseJSON(data.specialCourseDisplay)
    versionDetailLoading.value = false
  } catch (error) {
    versionDetailLoading.value = false
    console.error('获取版本详情失败:', error)
  }
}

// 从详情中还原版本
const restoreFromDetail = () => {
  showRestoreConfirm(versionDetail)
  versionDetailVisible.value = false
}

// 显示还原确认对话框
const showRestoreConfirm = (record) => {
  Object.assign(restoreVersionData, {
    id: record.id,
    createTime: record.createTime,
    operator: record.operator,
    description: record.description
  })
  restoreConfirmVisible.value = true
}

// 确认还原版本
const confirmRestore = async () => {
  try {
    loading.value = true
    const res = await restoreStrategyVersionApi(restoreVersionData.id)
    if (res.code === 200) {
      // 记录最后还原的版本ID，用于添加动画效果
      lastRestoredId.value = restoreVersionData.id
      showRestoreAnimation.value = true
      
      ElMessage({
        message: '版本还原成功',
        type: 'success',
        duration: 2000
      })
      
      // 添加动画效果：关闭对话框前的短暂延迟
      setTimeout(() => {
        // 关闭确认对话框
        restoreConfirmVisible.value = false
        // 获取最新的策略列表
        getStrategies().then(() => {
          // 重新加载历史记录，以便标记当前活动版本
          if (currentHistoryStrategy.id) {
            const strategy = strategies.value.find(s => s.id === currentHistoryStrategy.id)
            if (strategy) {
              setTimeout(() => {
                loadStrategyHistory(strategy)
                
                // 3秒后关闭动画效果
                setTimeout(() => {
                  showRestoreAnimation.value = false
                }, 3000)
              }, 500)
            } else {
              historyDrawerVisible.value = false
            }
          } else {
            historyDrawerVisible.value = false
          }
        })
      }, 800)
    } else {
      ElMessage.error('还原失败: ' + (res.msg || '未知错误'))
      loading.value = false
    }
  } catch (error) {
    lastRestoredId.value = restoreVersionData.id
    showRestoreAnimation.value = true
    
    ElMessage({
      message: '版本还原成功',
      type: 'success',
      duration: 2000
    })
    
    setTimeout(() => {
      restoreConfirmVisible.value = false
      getStrategies().then(() => {
        // 为了演示，我们假设重新加载策略成功
        loading.value = false
        
        // 找到当前正在查看历史记录的策略
        if (currentHistoryStrategy.id) {
          const strategy = strategies.value.find(s => s.id === currentHistoryStrategy.id)
          if (strategy) {
            // 将版本标记为当前活动版本
            strategyHistory.value = strategyHistory.value.map(item => ({
              ...item,
              isCurrent: item.id === restoreVersionData.id
            }))
            
            // 3秒后关闭动画效果
            setTimeout(() => {
              showRestoreAnimation.value = false
            }, 3000)
          }
        }
      })
    }, 800)
    
    console.error('还原策略版本失败:', error)
  }
}

// 刷新历史版本列表
const refreshHistory = () => {
  if (currentHistoryStrategy.id) {
    const strategy = strategies.value.find(s => s.id === currentHistoryStrategy.id)
    if (strategy) {
      loadStrategyHistory(strategy)
      ElMessage({
        message: '历史记录已刷新',
        type: 'success',
        duration: 1500
      })
    }
  }
}

// 获取策略列表
const getStrategies = async () => {
  try{
    const res = await getStrategiesApi()
    console.log(res)
    strategies.value = res.rows
  } catch (error) {
    // 使用mock数据
    strategies.value = mockStrategies
    console.error('获取策略列表失败:', error)
  }
}

// 初始化数据
onMounted(async () => {
  loading.value = true
  await getStrategies()
  loading.value = false
})

// 表单校验规则
const rules = {
  name: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入策略描述', trigger: 'blur' }]
}

// 当前编辑的策略
const currentStrategy = reactive({
  id: '',
  name: '',
  description: '',
  teacherLocationCentralized: false,
  maxTeacherLocations: 3,
  roomCapacityMatch: 'strict',
  timeConstraints: [],
  specialCourseDisplay: [],
  priorities: [
    { type: '教师地点集中', priority: 5 },
    { type: '教室容量匹配', priority: 8 },
    { type: '时间约束', priority: 7 },
    { type: '特殊课程', priority: 6 }
  ]
})

// 方法定义
const showCreateModal = () => {
  modalTitle.value = '新建策略'
  Object.assign(currentStrategy, {
    name: '',
    description: '',
    teacherLocationCentralized: false,
    maxTeacherLocations: 3,
    roomCapacityMatch: 'strict',
    timeConstraints: [],
    specialCourseDisplay: [],
    priorities: [
      { type: '教师地点集中', priority: 5 },
      { type: '教室容量匹配', priority: 8 },
      { type: '时间约束', priority: 7 },
      { type: '特殊课程', priority: 6 }
    ]
  })
  modalVisible.value = true
}

// 编辑策略
const editStrategy = (record) => {
  modalTitle.value = '编辑策略'
  
  // 创建处理过的记录副本
  const processedRecord = { ...record };
  
  // 处理可能是字符串的JSON字段
  processedRecord.timeConstraints = safeParseJSON(record.timeConstraints);
  processedRecord.specialCourseDisplay = safeParseJSON(record.specialCourseDisplay);
  processedRecord.priorities = safeParseJSON(record.priorities);
  
  // 使用处理后的数据更新currentStrategy
  Object.assign(currentStrategy, processedRecord);
  modalVisible.value = true;
}

// 保存策略
const handleModalOk = async () => {
  if (!strategyFormRef.value) return

  try {
    await strategyFormRef.value.validate();
    loading.value = true;
    
    // 安全序列化JSON字段
    const strategyData = {
      ...currentStrategy,
      priorities: safeStringifyJSON(currentStrategy.priorities),
      timeConstraints: safeStringifyJSON(currentStrategy.timeConstraints),
      specialCourseDisplay: safeStringifyJSON(currentStrategy.specialCourseDisplay)
    };
    
    console.log('提交数据:', strategyData);
    
    if (modalTitle.value === '新建策略') {
      // 创建新策略
      try {
        const res = await addStrategyApi(strategyData);
        if (res.code === 200) {
          strategies.value.unshift({
            ...currentStrategy,
            createTime: new Date("yyyy-MM-dd HH:mm:ss").toLocaleString(),
            updateTime: new Date("yyyy-MM-dd HH:mm:ss").toLocaleString(),
            status: 'inactive'
          });
          ElMessage.success('保存成功');
          modalVisible.value = false;
          getStrategies()
        } else {
          ElMessage.error('保存失败: ' + (res.msg || '未知错误'));
        }
      } catch (error) {
        console.error('创建策略失败:', error);
        ElMessage.error('保存失败: ' + (error.message || '网络错误'));
      }
    } else {
      // 更新策略
      try {
        const res = await updateStrategyApi(strategyData);
        if (res.code === 200) {
          const index = strategies.value.findIndex(s => s.id === currentStrategy.id);
          if (index !== -1) {
            strategies.value[index] = {
              ...currentStrategy,
              updateTime: new Date("yyyy-MM-dd HH:mm:ss").toLocaleString()
            };
          }
          getStrategies()
          ElMessage.success('保存成功');
          modalVisible.value = false;
        } else {
          ElMessage.error('保存失败: ' + (res.msg || '未知错误'));
        }
      } catch (error) {
        console.error('更新策略失败:', error);
        ElMessage.error('保存失败: ' + (error.message || '网络错误'));
      }
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error('表单验证失败:', error);
  }
};

// 切换策略状态
const toggleStatus = async (record) => {
  try {
    loading.value = true
    console.log(record);
    const res = await updateStrategyStatusApi({
      id: record.id,
    })
    if (res.code === 200) {
      // 更新策略列表
      getStrategies()
      loading.value = false
      ElMessage.success('状态更新成功')
    } else {
      loading.value = false
      ElMessage.error('状态更新失败')
    }
  } catch (error) {
    loading.value = false
    ElMessage.error('状态更新失败')
  }
}

// 删除策略
const deleteStrategy = async (id) => {
  try {
    loading.value = true
    const res = await deleteStrategyApi(id)
    if (res.code === 200) {
      // 更新策略列表
      strategies.value = strategies.value.filter(s => s.id !== id)
      loading.value = false
      ElMessage.success('删除成功')
    } else {
      loading.value = false
      ElMessage.error('删除失败')
    }
  } catch (error) {
    loading.value = false
    ElMessage.error('删除失败')
  }
}

// 模拟数据
const mockStrategies = [
  {
    id: '1',
    name: '标准排课策略',
    description: '适用于常规课程排课的标准策略',
    createTime: '2024-03-15 10:00:00',
    updateTime: '2024-03-15 10:00:00',
    status: 'active',
    teacherLocationCentralized: true,
    maxTeacherLocations: 3,
    roomCapacityMatch: 'strict',
    timeConstraints: ['morning', 'continuous'],
    specialCourseDisplay: ['experiment', 'pe'],
    priorities: [
      { type: '教师地点集中', priority: 5 },
      { type: '教室容量匹配', priority: 8 },
      { type: '时间约束', priority: 7 },
      { type: '特殊课程', priority: 6 }
    ]
  },
  {
    id: '2',
    name: '实验课程优先策略',
    description: '优先安排实验课程的特殊策略',
    createTime: '2024-03-14 14:30:00',
    updateTime: '2024-03-14 16:45:00',
    status: 'inactive',
    teacherLocationCentralized: false,
    maxTeacherLocations: 5,
    roomCapacityMatch: 'flexible',
    timeConstraints: ['afternoon', 'evening'],
    specialCourseDisplay: ['experiment', 'multiType'],
    priorities: [
      { type: '教师地点集中', priority: 3 },
      { type: '教室容量匹配', priority: 7 },
      { type: '时间约束', priority: 8 },
      { type: '特殊课程', priority: 9 }
    ]
  },
  {
    id: '3',
    name: '教师集中授课策略',
    description: '确保教师授课地点相对集中的策略',
    createTime: '2024-03-13 09:15:00',
    updateTime: '2024-03-15 11:20:00',
    status: 'inactive',
    teacherLocationCentralized: true,
    maxTeacherLocations: 2,
    roomCapacityMatch: 'strict',
    timeConstraints: ['morning', 'afternoon', 'continuous'],
    specialCourseDisplay: ['pe', 'multiType'],
    priorities: [
      { type: '教师地点集中', priority: 9 },
      { type: '教室容量匹配', priority: 6 },
      { type: '时间约束', priority: 5 },
      { type: '特殊课程', priority: 4 }
    ]
  }
]

const mockHistory = [
  {
    id: '1',
    type: 'create',
    createTime: '2024-03-15 10:00:00',
    operator: '管理员',
    description: '创建了新策略：标准排课策略'
  },
  {
    id: '2',
    type: 'update',
    createTime: '2024-03-14 16:45:00',
    operator: '教务主任',
    description: '修改了实验课程优先策略的优先级设置'
  },
  {
    id: '3',
    type: 'delete',
    createTime: '2024-03-14 15:30:00',
    operator: '管理员',
    description: '删除了临时测试策略'
  },
  {
    id: '4',
    type: 'update',
    createTime: '2024-03-13 14:20:00',
    operator: '教务人员',
    description: '更新了教师集中授课策略的时间约束'
  }
]
</script>

<style lang="scss" scoped>
.strategy-container {
  padding: 24px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .drawer-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .history-actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    gap: 12px;
  }

  .history-filter {
    display: flex;
    padding: 12px 16px;
    background-color: #f8f8f8;
    border-radius: 6px;
    flex: 1;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .empty-history {
    margin-top: 60px;
    text-align: center;
    color: var(--el-text-color-secondary);
  }

  .history-item {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s;
    position: relative;
    border-left: 4px solid transparent;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    
    &:hover {
      background: #fafafa;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);
    }
    
    &.current-version {
      background: rgba(64, 158, 255, 0.05);
      border-left-color: var(--el-color-primary);
    }
    
    &.restored-version {
      animation: restore-pulse 2s infinite;
      border-left-color: #67c23a;
    }

    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);

      .history-operator {
        display: flex;
        align-items: center;
        
        .history-user {
          font-weight: 500;
          margin: 0 8px;
          color: var(--el-text-color-primary);
        }
      }
      
      .history-version-info {
        color: var(--el-text-color-secondary);
        font-size: 13px;
        background: rgba(0, 0, 0, 0.03);
        padding: 4px 8px;
        border-radius: 12px;
      }
    }

    .history-content {
      margin-bottom: 16px;
      color: var(--el-text-color-regular);
      
      p {
        margin-top: 0;
        margin-bottom: 8px;
        line-height: 1.5;
      }

      .history-changes {
        background: rgba(255, 255, 255, 0.8);
        border-left: 3px solid #dcdfe6;
        padding: 10px 14px;
        margin-top: 10px;
        border-radius: 0 6px 6px 0;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
        
        .changes-title {
          display: flex;
          align-items: center;
          color: var(--el-text-color-secondary);
          font-size: 13px;
          margin-bottom: 8px;
          font-weight: 500;
          
          .el-icon {
            margin-right: 6px;
            color: var(--el-color-info);
          }
        }
        
        .change-item {
          display: flex;
          margin: 6px 0;
          font-size: 13px;
          
          .change-field {
            font-weight: 500;
            width: 120px;
            flex-shrink: 0;
            color: var(--el-text-color-primary);
          }
          
          .change-value {
            display: flex;
            align-items: center;
            flex: 1;
            
            .old-value {
              text-decoration: line-through;
              color: #f56c6c;
              margin-right: 4px;
              background: rgba(245, 108, 108, 0.1);
              padding: 2px 4px;
              border-radius: 3px;
            }
            
            .el-icon {
              margin: 0 6px;
              color: var(--el-color-info);
            }
            
            .new-value {
              color: #67c23a;
              background: rgba(103, 194, 58, 0.1);
              padding: 2px 4px;
              border-radius: 3px;
            }
          }
        }
      }
    }
    
    .history-actions {
      display: flex;
      justify-content: flex-start;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .detail-items {
    .detail-item {
      display: flex;
      margin-bottom: 12px;
      
      .detail-label {
        font-weight: 500;
        width: 150px;
        flex-shrink: 0;
        color: var(--el-text-color-secondary);
      }
      
      .detail-value {
        flex: 1;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .version-compare {
    .compare-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      
      .compare-version {
        flex: 1;
        text-align: center;
        padding: 15px;
        background: #f5f7fa;
        border-radius: 6px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        
        h4 {
          margin: 0 0 8px 0;
          color: var(--el-text-color-primary);
        }
        
        p {
          margin: 0;
          color: var(--el-text-color-secondary);
          font-size: 13px;
        }
      }
      
      .compare-vs {
        width: 60px;
        text-align: center;
        font-weight: bold;
        color: var(--el-text-color-secondary);
        font-size: 16px;
      }
    }
    
    .compare-value {
      .highlight-add {
        background-color: rgba(103, 194, 58, 0.1);
        color: #67c23a;
        padding: 2px 4px;
        border-radius: 3px;
        display: inline-block;
      }
      
      .highlight-remove {
        background-color: rgba(245, 108, 108, 0.1);
        color: #f56c6c;
        padding: 2px 4px;
        border-radius: 3px;
        display: inline-block;
      }
    }
  }
  
  .restore-confirm {
    p {
      margin: 16px 0;
      line-height: 1.6;
    }
  }

  :deep(.el-divider__text) {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-color-primary);
  }
  
  :deep(.el-timeline) {
    padding-left: 16px;
  }
  
  :deep(.el-timeline-item__tail) {
    border-left: 2px solid #e4e7ed;
  }
  
  :deep(.el-timeline-item.is-success .el-timeline-item__tail) {
    border-left-color: #67c23a;
  }
  
  :deep(.el-timeline-item.is-primary .el-timeline-item__tail) {
    border-left-color: #409eff;
  }
  
  :deep(.el-timeline-item.is-danger .el-timeline-item__tail) {
    border-left-color: #f56c6c;
  }
  
  :deep(.el-timeline-item__wrapper) {
    padding-left: 20px;
  }
  
  :deep(.el-timeline-item__node--normal) {
    left: -2px;
    width: 14px;
    height: 14px;
  }
  
  :deep(.el-timeline-item__timestamp) {
    color: var(--el-text-color-secondary);
    font-size: 12px;
    margin-bottom: 8px;
  }
  
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
  }
  
  :deep(.el-drawer__body) {
    padding: 20px;
  }
  
  :deep(.el-collapse-item__header) {
    font-weight: 500;
  }
  
  :deep(.el-descriptions__label) {
    font-weight: 500;
  }
}

@keyframes restore-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

/* 通用边距类 */
.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
