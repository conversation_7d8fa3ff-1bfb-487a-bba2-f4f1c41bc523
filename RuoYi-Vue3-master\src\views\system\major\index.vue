<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="专业编号" prop="majorCode">
        <el-input
          v-model="queryParams.majorCode"
          placeholder="请输入专业编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专业名称" prop="majorName">
        <el-input
          v-model="queryParams.majorName"
          placeholder="请输入专业名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专业简称" prop="shortName">
        <el-input
          v-model="queryParams.shortName"
          placeholder="请输入专业简称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="英文名称" prop="enName">
        <el-input
          v-model="queryParams.enName"
          placeholder="请输入英文名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:major:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:major:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:major:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:major:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="majorList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="专业编号" align="center" prop="majorCode" />
      <el-table-column label="专业名称" align="center" prop="majorName" />
      <el-table-column label="专业简称" align="center" prop="shortName" />
      <el-table-column label="学制" align="center" prop="duration" />
      <el-table-column label="英文名称" align="center" prop="enName" />
      <el-table-column label="开办状态" align="center" prop="status" />
      <el-table-column label="所属院系" align="center" prop="departmentCode" />
      <el-table-column label="培养层次" align="center" prop="educationLevel" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:major:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:major:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改专业信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="majorRef" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="专业编号" prop="majorCode">
          <el-input v-model="form.majorCode" placeholder="请输入专业编号" />
        </el-form-item> -->
        <el-form-item label="专业名称" prop="majorName">
          <el-input v-model="form.majorName" placeholder="请输入专业名称" />
        </el-form-item>
        <el-form-item label="专业简称" prop="shortName">
          <el-input v-model="form.shortName" placeholder="请输入专业简称" />
        </el-form-item>
        <el-form-item label="英文名称" prop="enName">
          <el-input v-model="form.enName" placeholder="请输入英文名称" />
        </el-form-item>
        <el-form-item label="学制" prop="duration">
          <el-input v-model="form.duration" placeholder="请输入学制" />
        </el-form-item>
        <el-form-item label="开办状态" prop="status">
          <el-input v-model="form.status" placeholder="请输入开办状态" />
        </el-form-item>
        <el-form-item label="所属院系" prop="departmentCode">
          <el-input v-model="form.departmentCode" placeholder="请输入所属院系" />
        </el-form-item>
        <el-form-item label="培养层次" prop="educationLevel">
          <el-input v-model="form.educationLevel" placeholder="请输入培养层次" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Major">
import { listMajor, getMajor, delMajor, addMajor, updateMajor } from "@/api/system/major";

const { proxy } = getCurrentInstance();

const majorList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    majorCode: null,
    majorName: null,
    shortName: null,
    duration: null,
    enName: null,
    status: null,
    departmentCode: null,
    educationLevel: null
  },
  rules: {
    majorCode: [
      { required: true, message: "专业编号不能为空", trigger: "blur" }
    ],
    majorName: [
      { required: true, message: "专业名称不能为空", trigger: "blur" }
    ],
    duration: [
      { required: true, message: "学制不能为空", trigger: "change" }
    ],
    status: [
      { required: true, message: "开办状态不能为空", trigger: "change" }
    ],
    departmentCode: [
      { required: true, message: "所属院系不能为空", trigger: "change" }
    ],
    educationLevel: [
      { required: true, message: "培养层次不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询专业信息列表 */
function getList() {
  loading.value = true;
  listMajor(queryParams.value).then(response => {
    majorList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    majorCode: null,
    majorName: null,
    shortName: null,
    duration: null,
    enName: null,
    status: null,
    departmentCode: null,
    educationLevel: null
  };
  proxy.resetForm("majorRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.majorCode);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加专业信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _majorCode = row.majorCode || ids.value
  getMajor(_majorCode).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改专业信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["majorRef"].validate(valid => {
    if (valid) {
      if (form.value.majorCode != null) {
        updateMajor(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMajor(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _majorCodes = row.majorCode || ids.value;
  proxy.$modal.confirm('是否确认删除专业信息编号为"' + _majorCodes + '"的数据项？').then(function() {
    return delMajor(_majorCodes);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/major/export', {
    ...queryParams.value
  }, `major_${new Date().getTime()}.xlsx`)
}

getList();
</script>
