<template>
  <!-- 引入Font Awesome图标库 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <!-- 引入和风天气图标库 -->
  <link href="https://cdn.jsdelivr.net/npm/qweather-icons@1.6.0/font/qweather-icons.css" rel="stylesheet">

  <div class="home-container">
    <!-- 主要内容区 -->
    <div class="main-container">
      <!-- 内容区 -->
      <div class="content">
        <!-- 天气信息按钮和卡片 -->
        <div
          class="weather-button"
          @click="toggleWeather"
          :class="{'weather-expanded': showWeatherCard}"
          draggable="true"
          @dragstart="handleWeatherDragStart"
          @dragend="handleWeatherDragEnd"
          :style="{
            left: weatherPosition.x,
            top: weatherPosition.y,
            right: 'auto',
            bottom: 'auto',
            position: 'absolute',
            opacity: isWeatherDragging ? '0.6' : '1'
          }"
        >
        <i class="fas fa-cloud-sun-rain"></i>
          <div class="weather-button-pulse"></div>
        </div>

        <!-- 天气卡片 -->
        <div
          class="weather-card"
          v-show="showWeatherCard"
          :class="{'weather-card-dragging': isWeatherDragging}"
          :style="{
            left: weatherCardPosition.x,
            top: weatherCardPosition.y,
            right: 'auto',
            bottom: 'auto',
            position: 'absolute'
          }"
        >
          <div class="weather-card-header">
            <h3><i class="qi-weather"></i> AI天气预报</h3>
            <div>
              <i class="fas fa-sync-alt refresh-icon" @click="refreshWeather" :class="{'fa-spin': weatherLoading}" title="刷新天气"></i>
              <i class="fas fa-times" @click="toggleWeather" style="cursor: pointer; margin-left: 10px;"></i>
            </div>
          </div>
          <p class="weather-subtitle">
            <i class="fas fa-map-marker-alt location-icon"></i> {{ cityName }}
            <span class="update-time">未来三天天气（用于智能调课参考）</span>
          </p>
          <div class="weather-content">
            <div v-if="weatherLoading && weatherData.length === 0" class="weather-loading">
              <i class="fas fa-spinner fa-spin"></i> 正在获取天气数据...
            </div>
            <div class="weather-day" v-for="(day, index) in weatherData" :key="index">
              <div class="weather-date">{{ day.date }}</div>
              <div class="weather-icon">
                <!-- 使用和风天气图标库 -->
                <i :class="`qi-${day.iconDay}`" v-if="day.iconDay"></i>
                <!-- 备用图标 -->
                <template v-else>
                  <i class="fas fa-sun" v-if="day.weather === '晴'"></i>
                  <i class="fas fa-cloud-sun" v-if="day.weather === '多云' || day.weather === '阴'"></i>
                  <i class="fas fa-bolt" v-if="day.weather.includes('雷')"></i>
                  <i class="fas fa-snowflake" v-if="day.weather.includes('雪')"></i>
                  <i class="fas fa-cloud-rain" v-if="day.weather.includes('雨') && !day.weather.includes('雷')"></i>
                  <i class="fas fa-smog" v-if="day.weather.includes('雾') || day.weather.includes('霾')"></i>
                  <i class="fas fa-wind" v-if="day.weather.includes('风')"></i>
                </template>
              </div>
              <div class="weather-info">
                <div class="weather-text">{{ day.weather }}</div>
                <div class="weather-temp">{{ day.tempRange }}</div>
                <div class="weather-wind">{{ day.windDir }} {{ day.windScale }}</div>
              </div>
            </div>
          </div>
          <div class="weather-ai-note" v-if="weatherData[1].weather.includes('雨') || weatherData[2].weather.includes('雨')">
            <i class="fas fa-robot"></i> AI提示: 明后两天有雨，建议调整户外课程安排
          </div>
          <div class="weather-ai-note" v-else-if="weatherData[1].weather.includes('雪') || weatherData[2].weather.includes('雪')">
            <i class="fas fa-robot"></i> AI提示: 明后两天有雪，建议调整户外活动并注意安全
          </div>
          <div class="weather-ai-note" v-else-if="weatherData[0].weather.includes('晴') && weatherData[1].weather.includes('晴') && weatherData[2].weather.includes('晴')">
            <i class="fas fa-robot"></i> AI提示: 未来三天天气晴好，适合安排户外教学活动
          </div>
        </div>

        <!-- 错落有致的功能卡片 -->
        <div class="dashboard-grid">
          <!-- 第一行 -->
          <div class="dashboard-row">
            <!-- 快速操作区 -->
            <div class="card quick-actions grid-item large-width">
              <div class="card-title">
                <i class="fas fa-bolt"></i> 快速操作
              </div>
              <div class="action-buttons">
                <div class="action-button">
                  <i class="fas fa-plus-circle"></i>
                  <span>创建课表</span>
                </div>
                <div class="action-button">
                  <i class="fas fa-edit"></i>
                  <span>编辑课表</span>
                </div>
                <div class="action-button">
                  <i class="fas fa-calendar-alt"></i>
                  <span>查看课表</span>
                </div>
                <div class="action-button">
                  <i class="fas fa-magic"></i>
                  <span>AI优化</span>
                </div>
                <div class="action-button">
                  <i class="fas fa-print"></i>
                  <span>打印课表</span>
                </div>
                <div class="action-button">
                  <i class="fas fa-cog"></i>
                  <span>系统设置</span>
                </div>
              </div>
            </div>

            <!-- 系统公告 -->
            <div class="card announcement-card grid-item medium-width">
              <div class="card-title">
                <i class="fas fa-bullhorn"></i> 系统公告
                <el-tag size="small" effect="plain" type="danger" v-if="hasNewAnnouncement" style="margin-left: 8px;">新</el-tag>
              </div>
              <div class="notices">
                <div v-for="(item, index) in announcements" :key="index" class="notice-item" @click="openAnnouncementDetail(item)">
                  <div class="notice-title">{{ item.title }}</div>
                  <div class="notice-content">{{ item.brief }}</div>
                  <div class="notice-time">{{ item.date }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第二行 -->
          <div class="dashboard-row">
            <!-- 待办事项 -->
            <div class="card todo-card grid-item medium-width">
              <div class="card-title">
                <i class="fas fa-tasks"></i> 待办事项
                <div class="todo-badge">3</div>
              </div>
              <div class="todo-list">
                <div class="todo-item">
                  <div class="todo-checkbox">
                    <i class="far fa-square"></i>
                  </div>
                  <div class="todo-content">
                    <div class="todo-title">完成计算机学院排课</div>
                    <div class="todo-deadline">截止日期: 2023-06-15</div>
                  </div>
                  <div class="todo-priority high">高</div>
                </div>
                <div class="todo-item">
                  <div class="todo-checkbox">
                    <i class="far fa-square"></i>
                  </div>
                  <div class="todo-content">
                    <div class="todo-title">审核教师排课偏好</div>
                    <div class="todo-deadline">截止日期: 2023-06-20</div>
                  </div>
                  <div class="todo-priority medium">中</div>
                </div>
                <div class="todo-item">
                  <div class="todo-checkbox">
                    <i class="far fa-square"></i>
                  </div>
                  <div class="todo-content">
                    <div class="todo-title">分配实验室资源</div>
                    <div class="todo-deadline">截止日期: 2023-06-25</div>
                  </div>
                  <div class="todo-priority low">低</div>
                </div>
              </div>
              <div class="todo-footer">
                <button class="add-todo-btn">
                  <i class="fas fa-plus"></i> 添加待办
                </button>
                <button class="view-all-btn">
                  查看全部 <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>

            <!-- 排课进度概览 -->
            <div class="card progress-card grid-item medium-width">
              <div class="card-title">
                <i class="fas fa-chart-line"></i> 排课进度概览
              </div>
              <div class="progress-overview">
                <div class="progress-item">
                  <div class="progress-label">
                    <span>文学院</span>
                    <span>85%</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 85%;"></div>
                  </div>
                </div>
                <div class="progress-item">
                  <div class="progress-label">
                    <span>理学院</span>
                    <span>76%</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 76%;"></div>
                  </div>
                </div>
                <div class="progress-item">
                  <div class="progress-label">
                    <span>工学院</span>
                    <span>62%</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 62%;"></div>
                  </div>
                </div>
                <div class="progress-item">
                  <div class="progress-label">
                    <span>医学院</span>
                    <span>45%</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 45%;"></div>
                  </div>
                </div>
                <div class="progress-item">
                  <div class="progress-label">
                    <span>综合进度</span>
                    <span>68%</span>
                  </div>
                  <div class="progress-bar total">
                    <div class="progress-fill total" style="width: 68%;"></div>
                  </div>
                </div>
              </div>
              <div class="completion-date">
                预计完成日期: <span>2023-07-05</span>
              </div>
            </div>
          </div>

          <!-- 第三行 -->
          <div class="dashboard-row">
            <!-- 新手排课人员智能培训 -->
            <div class="card training-card grid-item large-width">
              <div class="card-title">
                <i class="fas fa-graduation-cap"></i> 新手排课人员智能培训
              </div>
              <div class="tutorial-container">
                <div class="tutorial-item" @click="showTrainingGuide">
                  <div class="tutorial-icon">
                    <i class="fas fa-play"></i>
                  </div>
                  <div class="tutorial-info">
                    <div class="tutorial-title">排课系统基础入门</div>
                    <div class="tutorial-desc">了解系统各模块功能和基本操作流程</div>
                  </div>
                </div>
                <div class="tutorial-item">
                  <div class="tutorial-icon">
                    <i class="fas fa-book"></i>
                  </div>
                  <div class="tutorial-info">
                    <div class="tutorial-title">排课规则与约束条件</div>
                    <div class="tutorial-desc">学习排课中需要注意的各项规则和限制</div>
                  </div>
                </div>
                <div class="tutorial-item">
                  <div class="tutorial-icon">
                    <i class="fas fa-robot"></i>
                  </div>
                  <div class="tutorial-info">
                    <div class="tutorial-title">AI辅助排课指南</div>
                    <div class="tutorial-desc">掌握如何利用AI进行高效智能排课</div>
                  </div>
                </div>
                <div class="tutorial-item">
                  <div class="tutorial-icon">
                    <i class="fas fa-tasks"></i>
                  </div>
                  <div class="tutorial-info">
                    <div class="tutorial-title">实战案例演练</div>
                    <div class="tutorial-desc">通过实际案例学习排课技巧</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右下角AI助手 -->
    <div
      class="ai-assistant"
      @click="toggleAssistant"
      draggable="true"
      @dragstart="handleDragStart"
      @dragend="handleDragEnd"
      :style="{
        left: assistantPosition.x,
        top: assistantPosition.y,
        right: assistantPosition.right,
        bottom: assistantPosition.bottom,
        opacity: isDragging ? '0.6' : '1'
      }"
    >
      <div class="assistant-tip" v-if="showTip">
        <div class="tip-content">{{ currentTip }}</div>
        <div class="tip-arrow"></div>
      </div>
      <i class="fas fa-robot"></i>
    </div>

    <!-- AI助手聊天框 -->
    <div
      class="chat-container"
      v-show="showAssistantChat"
      :class="{'dragging': isDragging}"
    >
        <div class="chat-connector"></div>
        <div class="chat-header">
            <div>智能排课助手 (AI Assistant)</div>
            <div @click="toggleAssistant" style="cursor: pointer;">
                <i class="fas fa-times"></i>
            </div>
        </div>
        <div class="chat-body" ref="chatMessagesRef">
            <div
                v-for="(msg, index) in chatMessages"
                :key="index"
                class="chat-message"
                :class="msg.isUser ? 'user' : 'bot'"
            >
                <div class="message-avatar">
                    <i class="fas" :class="msg.isUser ? 'fa-user' : 'fa-robot'"></i>
                </div>
                <div class="message-content">
                    {{ msg.content }}
                </div>
            </div>
        </div>
        <div class="chat-footer">
            <input
                type="text"
                class="chat-input"
                v-model="userInput"
                placeholder="请输入您的问题..."
                @keyup.enter="sendMessage"
            >
            <button class="send-button" @click="sendMessage">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <!-- 新手培训指南弹窗 -->
    <el-dialog
      v-model="showTrainingDialog"
      title="新手排课指南"
      width="80%"
      class="training-dialog"
      destroy-on-close
      :show-close="false"
    >
      <div class="training-steps">
        <el-steps :active="activeStep" finish-status="success" simple style="margin-bottom: 25px">
          <el-step title="系统介绍" icon="Guide">
            <template #icon>
              <div class="custom-step-icon"><i class="fas fa-info-circle"></i></div>
            </template>
          </el-step>
          <el-step title="基础排课" icon="OfficeBuilding">
            <template #icon>
              <div class="custom-step-icon"><i class="fas fa-tasks"></i></div>
            </template>
          </el-step>
          <el-step title="高级功能" icon="Setting">
            <template #icon>
              <div class="custom-step-icon"><i class="fas fa-cogs"></i></div>
            </template>
          </el-step>
          <el-step title="AI辅助" icon="Cpu">
            <template #icon>
              <div class="custom-step-icon"><i class="fas fa-robot"></i></div>
            </template>
          </el-step>
        </el-steps>

        <!-- 新手培训内容 -->
        <div class="step-content">
          <div v-if="activeStep === 0">
            <h3><i class="fas fa-graduation-cap"></i> 欢迎使用AI大学智能排课系统</h3>
            <p class="intro-text">本系统利用人工智能技术，帮助您高效完成课程安排工作。下面是系统的主要功能模块：</p>
            <div class="features-list">
              <div class="feature-item animated-card">
                <div class="feature-icon-container">
                  <i class="fas fa-calendar-alt"></i>
                </div>
                <div>
                  <h4>课表管理</h4>
                  <p>创建、编辑和管理课表，支持多种视图模式</p>
                  <el-button size="small" type="primary" class="view-demo-btn" @click="showDemoVideo('课表管理')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
              <div class="feature-item animated-card">
                <div class="feature-icon-container">
                  <i class="fas fa-building"></i>
                </div>
                <div>
                  <h4>教室管理</h4>
                  <p>管理教室资源，查看使用情况和冲突检测</p>
                  <el-button size="small" type="primary" class="view-demo-btn" @click="showDemoVideo('教室管理')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
              <div class="feature-item animated-card">
                <div class="feature-icon-container">
                  <i class="fas fa-user-tie"></i>
                </div>
                <div>
                  <h4>教师管理</h4>
                  <p>教师信息和课程偏好管理，工作量统计</p>
                  <el-button size="small" type="primary" class="view-demo-btn" @click="showDemoVideo('教师管理')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
              <div class="feature-item animated-card">
                <div class="feature-icon-container">
                  <i class="fas fa-magic"></i>
                </div>
                <div>
                  <h4>AI排课</h4>
                  <p>基于约束条件的自动排课，优化课程安排</p>
                  <el-button size="small" type="primary" class="view-demo-btn" @click="showDemoVideo('AI排课')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="activeStep === 1">
            <h3><i class="fas fa-route"></i> 基础排课操作</h3>
            <p class="intro-text">通过以下步骤快速开始您的排课工作：</p>
            <div class="steps-container">
              <div class="step-item">
                <div class="step-number pulse-animation">1</div>
                <div class="step-content-box">
                  <div class="step-header">
                    <div class="step-icon"><i class="fas fa-calendar-plus"></i></div>
                    <h4>创建学期</h4>
                  </div>
                  <div class="step-description">
                    <p>在系统中创建新学期，设置开始和结束日期，以及教学周数。</p>
                    <div class="step-tips">
                      <div class="tip-item">
                        <i class="fas fa-check-circle"></i>
                        <span>指定学期名称（如：2023-2024学年第一学期）</span>
                      </div>
                      <div class="tip-item">
                        <i class="fas fa-check-circle"></i>
                        <span>设置开始日期和结束日期</span>
                      </div>
                      <div class="tip-item">
                        <i class="fas fa-check-circle"></i>
                        <span>配置教学周数和节假日安排</span>
                      </div>
                    </div>
                    <div class="step-image">
                      <div class="image-container">
                        <i class="fas fa-calendar-alt main-icon"></i>
                        <div class="image-overlay">
                          <div class="date-range">
                            <div class="date">2023.09.01</div>
                            <div class="date-divider"></div>
                            <div class="date">2024.01.15</div>
                          </div>
                          <div class="weeks-info">教学周：18周</div>
                        </div>
                      </div>
                    </div>
                    <el-button class="step-demo-btn" type="success" @click="showDemoVideo('创建学期')">
                      <i class="fas fa-play-circle"></i> 查看演示视频
                    </el-button>
                  </div>
                </div>
              </div>
              <div class="step-connector"></div>
              <div class="step-item">
                <div class="step-number pulse-animation">2</div>
                <div class="step-content-box">
                  <div class="step-header">
                    <div class="step-icon"><i class="fas fa-file-import"></i></div>
                    <h4>导入课程</h4>
                  </div>
                  <div class="step-description">
                    <p>导入课程信息，包括课程名称、学时、授课教师等信息。</p>
                    <div class="step-tips">
                      <div class="tip-item">
                        <i class="fas fa-check-circle"></i>
                        <span>支持Excel批量导入课程信息</span>
                      </div>
                      <div class="tip-item">
                        <i class="fas fa-check-circle"></i>
                        <span>手动创建单个课程信息</span>
                      </div>
                      <div class="tip-item">
                        <i class="fas fa-check-circle"></i>
                        <span>从历史学期复制课程数据</span>
                      </div>
                    </div>
                    <div class="step-actions">
                      <div class="action-button mini">
                        <i class="fas fa-file-excel"></i>
                        <span>Excel导入</span>
                      </div>
                      <div class="action-button mini">
                        <i class="fas fa-plus"></i>
                        <span>手动添加</span>
                      </div>
                      <div class="action-button mini">
                        <i class="fas fa-copy"></i>
                        <span>复制历史</span>
                      </div>
                    </div>
                    <el-button class="step-demo-btn" type="success" @click="showDemoVideo('导入课程')">
                      <i class="fas fa-play-circle"></i> 查看演示视频
                    </el-button>
                  </div>
                </div>
              </div>

              <div class="step-connector"></div>

              <div class="step-item">
                <div class="step-number pulse-animation">3</div>
                <div class="step-content-box">
                  <div class="step-header">
                    <div class="step-icon"><i class="fas fa-th"></i></div>
                    <h4>手动排课</h4>
                  </div>
                  <div class="step-description">
                    <p>在课表界面，通过拖拽方式为课程安排时间和教室。</p>
                    <div class="step-tips">
                      <div class="tip-item">
                        <i class="fas fa-check-circle"></i>
                        <span>拖拽课程至对应时间格</span>
                      </div>
                      <div class="tip-item">
                        <i class="fas fa-check-circle"></i>
                        <span>自动检测时间、教室、教师冲突</span>
                      </div>
                      <div class="tip-item">
                        <i class="fas fa-check-circle"></i>
                        <span>支持批量修改和调整</span>
                      </div>
                    </div>
                    <div class="schedule-preview">
                      <div class="preview-header">
                        <div class="preview-cell time-header">时间/地点</div>
                        <div class="preview-cell">周一</div>
                        <div class="preview-cell">周二</div>
                        <div class="preview-cell">周三</div>
                      </div>
                      <div class="preview-row">
                        <div class="preview-cell time-cell">上午<br/>8:00-9:40</div>
                        <div class="preview-cell course-cell">高等数学<br/><small>A101</small></div>
                        <div class="preview-cell"></div>
                        <div class="preview-cell course-cell">大学物理<br/><small>B203</small></div>
                      </div>
                      <div class="preview-row">
                        <div class="preview-cell time-cell">下午<br/>14:00-15:40</div>
                        <div class="preview-cell"></div>
                        <div class="preview-cell course-cell">程序设计<br/><small>机房C202</small></div>
                        <div class="preview-cell"></div>
                      </div>
                      <div class="drag-indicator">
                        <i class="fas fa-arrows-alt"></i>
                        <span>拖拽排课</span>
                      </div>
                    </div>
                    <el-button class="step-demo-btn" type="success" @click="showDemoVideo('手动排课')">
                      <i class="fas fa-play-circle"></i> 查看演示视频
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <div class="guide-actions">
              <el-button type="success" @click="showDemoVideo('全部基础操作')">
                <i class="fas fa-play-circle"></i> 观看完整操作演示
              </el-button>
              <el-button type="primary">
                <i class="fas fa-book"></i> 查看详细教程
              </el-button>
            </div>
          </div>
          <div v-else-if="activeStep === 2">
            <h3><i class="fas fa-star"></i> 高级功能使用</h3>
            <p class="intro-text">掌握这些高级功能，让排课更加高效：</p>
            <div class="advanced-features">
              <div class="advanced-feature animated-card">
                <div class="feature-icon-container small">
                  <i class="fas fa-layer-group"></i>
                </div>
                <div>
                  <h4>批量操作</h4>
                  <p>同时选择多个课程进行批量调整，提高工作效率。</p>
                  <el-button size="small" type="primary" @click="showDemoVideo('批量操作')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
              <div class="advanced-feature animated-card">
                <div class="feature-icon-container small">
                  <i class="fas fa-sliders-h"></i>
                </div>
                <div>
                  <h4>约束条件设置</h4>
                  <p>为教师、课程、教室设置特定的约束条件，确保排课满足各种需求。</p>
                  <el-button size="small" type="primary" @click="showDemoVideo('约束条件设置')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
              <div class="advanced-feature animated-card">
                <div class="feature-icon-container small">
                  <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div>
                  <h4>冲突检测与解决</h4>
                  <p>自动识别排课冲突，并提供智能解决方案。</p>
                  <el-button size="small" type="primary" @click="showDemoVideo('冲突检测与解决')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
              <div class="advanced-feature animated-card">
                <div class="feature-icon-container small">
                  <i class="fas fa-file-export"></i>
                </div>
                <div>
                  <h4>课表导出</h4>
                  <p>将排好的课表导出为Excel、PDF或图片格式，方便分享和打印。</p>
                  <el-button size="small" type="primary" @click="showDemoVideo('课表导出')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="activeStep === 3">
            <h3><i class="fas fa-robot"></i> AI辅助功能</h3>
            <p class="intro-text">利用AI技术，让排课更智能：</p>
            <div class="ai-features">
              <div class="ai-feature animated-card">
                <div class="feature-icon-container ai">
                  <i class="fas fa-magic"></i>
                </div>
                <div>
                  <h4>自动排课</h4>
                  <p>一键自动排课，AI会根据设定的约束条件生成最优排课方案。</p>
                  <el-button size="small" type="primary" @click="showDemoVideo('自动排课')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
              <div class="ai-feature animated-card">
                <div class="feature-icon-container ai">
                  <i class="fas fa-lightbulb"></i>
                </div>
                <div>
                  <h4>优化建议</h4>
                  <p>系统会分析当前排课方案，提供优化建议，如教室利用率提升、师生通勤时间减少等。</p>
                  <el-button size="small" type="primary" @click="showDemoVideo('优化建议')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
              <div class="ai-feature animated-card">
                <div class="feature-icon-container ai">
                  <i class="fas fa-cloud-sun-rain"></i>
                </div>
                <div>
                  <h4>天气智能调课</h4>
                  <p>根据天气预报，系统会自动提示可能需要调整的户外课程。</p>
                  <el-button size="small" type="primary" @click="showDemoVideo('天气智能调课')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
              <div class="ai-feature animated-card">
                <div class="feature-icon-container ai">
                  <i class="fas fa-comments"></i>
                </div>
                <div>
                  <h4>AI助手</h4>
                  <p>随时提问，获取排课相关的帮助和建议。</p>
                  <el-button size="small" type="primary" @click="showDemoVideo('AI助手')">
                    <i class="fas fa-play-circle"></i> 查看演示
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <el-button @click="prevStep" :disabled="activeStep === 0" round>
            <i class="fas fa-chevron-left"></i> 上一步
          </el-button>
          <el-button type="primary" @click="nextStep" v-if="activeStep < 3" round>
            下一步 <i class="fas fa-chevron-right"></i>
          </el-button>
          <el-button type="success" @click="finishTraining" v-else round>
            <i class="fas fa-check"></i> 完成培训
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 视频演示弹窗 -->
    <el-dialog
      v-model="showVideoDialog"
      :title="currentVideo.title"
      width="70%"
      class="video-dialog"
      destroy-on-close
    >
      <div class="video-container">
        <div class="video-player">
          <video
            ref="videoRef"
            controls
            autoplay
            class="demo-video"
            :src="currentVideo.url"
            :poster="currentVideo.poster"
            @ended="videoEnded"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
        <div class="video-info">
          <h3 class="video-title"><i class="fas fa-film"></i> {{ currentVideo.title }}</h3>
          <div class="video-description">
            <i class="fas fa-info-circle"></i> {{ currentVideo.description }}
          </div>
          <div class="video-tags">
            <el-tag
              v-for="tag in currentVideo.tags"
              :key="tag"
              type="info"
              effect="light"
              class="video-tag"
              size="small"
              style="background-color: #409EFF; color: white;"
            >
              <i class="fas fa-tag"></i> {{ tag }}
            </el-tag>
          </div>
          <div class="video-controls">
            <el-button type="primary" @click="closeVideoDialog" round>
              <i class="fas fa-check"></i> 我已了解
            </el-button>
            <el-button @click="replayVideo" type="info" round>
              <i class="fas fa-redo"></i> 重新播放
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 公告详情弹窗 -->
    <el-dialog
      v-model="showAnnouncementDialog"
      :title="currentAnnouncement.title"
      width="60%"
      class="announcement-dialog"
      destroy-on-close
    >
      <div class="announcement-detail animated fadeIn">
        <div class="announcement-header">
          <div class="announcement-info">
            <span class="announcement-date"><i class="fas fa-calendar-alt pulse"></i> 发布时间：{{ currentAnnouncement.date }}</span>
            <span class="announcement-source"><i class="fas fa-building bounce"></i> 发布部门：{{ currentAnnouncement.source }}</span>
          </div>
          <el-tag type="warning" effect="dark" class="importance-tag" v-if="currentAnnouncement.importance === 'high'">
            <i class="fas fa-exclamation-circle fa-spin"></i> 重要
          </el-tag>
        </div>
        <el-divider content-position="center"><i class="fas fa-newspaper"></i> 公告内容</el-divider>
        <div class="announcement-content highlight-box" v-html="currentAnnouncement.content"></div>
        <div class="announcement-attachments" v-if="currentAnnouncement.attachments && currentAnnouncement.attachments.length">
          <h4><i class="fas fa-paperclip"></i> 附件</h4>
          <div class="attachment-list">
            <div v-for="(attachment, index) in currentAnnouncement.attachments" :key="index" class="attachment-item">
              <i class="fas fa-file-alt"></i> {{ attachment.name }}
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAnnouncementDialog = false" round>
            <i class="fas fa-times"></i> 关闭
          </el-button>
          <el-button type="primary" @click="markAsRead" round class="pulse-button">
            <i class="fas fa-check"></i> 标记为已读
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
const latitude = ref("")
const longitude = ref("")

// 天气API返回数据类型定义
interface WeatherDaily {
  fxDate: string;      // 预报日期
  tempMax: string;     // 最高温度
  tempMin: string;     // 最低温度
  textDay: string;     // 白天天气状况文字描述
  textNight: string;   // 夜间天气状况文字描述
  windDirDay: string;  // 白天风向
  windScaleDay: string;// 白天风力等级
  iconDay: string;     // 白天天气状况图标代码
  iconNight: string;   // 夜间天气状况图标代码
}

interface WeatherResponse {
  code: string;
  daily: WeatherDaily[];
  updateTime: string;
}

// 加载状态
const weatherLoading = ref(false)
// 城市名称
const cityName = ref("未知位置")

onMounted(async () => {
  // 获取当前位置 根据浏览器获取
  try {
    weatherLoading.value = true
    const position = await new Promise<GeolocationPosition>((resolve, reject) => {
      if (!navigator.geolocation) {
        ElMessage.warning('您的浏览器不支持地理位置功能');
        reject(new Error('Geolocation is not supported'));
        return;
      }
      
      navigator.geolocation.getCurrentPosition(
        resolve, 
        (error) => {
          let errorMessage = '获取位置失败';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = '用户拒绝了位置请求';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = '位置信息不可用';
              break;
            case error.TIMEOUT:
              errorMessage = '获取位置超时';
              break;
          }
          console.warn(`地理位置错误(${error.code}): ${errorMessage}`);
          reject(error);
        },
        { 
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0 
        }
      );
    });
    
    latitude.value = position.coords.latitude ? position.coords.latitude.toString() : "";
    longitude.value = position.coords.longitude ? position.coords.longitude.toString() : "";

    // 获取城市信息
    await fetchCityName()

    // 获取天气数据
    await fetchWeatherData()
  } catch (error) {
    console.error('获取位置信息失败:', error);
    // 使用默认位置
    latitude.value = "34.403339";  // 默认纬度
    longitude.value = "113.763183"; // 默认经度
    
    // 仍然尝试获取天气数据，使用默认位置
    try {
      await fetchCityName();
      await fetchWeatherData();
    } catch (weatherError) {
      console.error('获取天气数据失败:', weatherError);
      ElMessage.warning('无法获取天气信息，请检查网络连接');
    }
  } finally {
    weatherLoading.value = false;
  }
  // 初始化天气卡片位置
  updateWeatherCardPosition()
})

// 获取城市名称
const fetchCityName = async () => {
  try {
    const key = '2ec79cf3390d40098dedee6b9e8dcb5f'
    const geoResponse = await fetch(`https://geoapi.qweather.com/v2/city/lookup?location=${longitude.value},${latitude.value}&key=${key}`)
    const geoData = await geoResponse.json()

    if (geoData.code === '200' && geoData.location && geoData.location.length > 0) {
      cityName.value = `${geoData.location[0].adm2} ${geoData.location[0].name}`
    }
  } catch (error) {
    console.error("获取城市信息失败:", error)
  }
}

// 获取天气数据
const fetchWeatherData = async () => {
  try {
    const key = '2ec79cf3390d40098dedee6b9e8dcb5f'
    const weatherResponse = await fetch(`https://devapi.qweather.com/v7/weather/3d?location=${longitude.value},${latitude.value}&key=${key}`)
    const data: WeatherResponse = await weatherResponse.json()
    console.log(data)

    if (data.code === '200' && data.daily && data.daily.length > 0) {
      // 更新天气数据
      weatherData.value = data.daily.slice(0, 3).map((day, index) => {
        return {
          date: index === 0 ? '今天' : index === 1 ? '明天' : '后天',
          weather: day.textDay,
          tempRange: `${day.tempMin}°C-${day.tempMax}°C`,
          windDir: day.windDirDay,
          windScale: day.windScaleDay,
          iconDay: day.iconDay
        }
      })
    } else {
      weatherData.value = getDefaultWeatherData()
    }
  } catch (error) {
    console.error("获取天气数据失败:", error)
    weatherData.value = getDefaultWeatherData()
  }
}

// 默认天气数据
const getDefaultWeatherData = () => {
  return [
    {
      date: '今天',
      weather: '晴',
      tempRange: '18°C-25°C',
      windDir: '东南风',
      windScale: '1-2级',
      iconDay: '100'  // 使用和风天气图标代码
    },
    {
      date: '明天',
      weather: '多云',
      tempRange: '16°C-24°C',
      windDir: '东南风',
      windScale: '1-2级',
      iconDay: '101'  // 使用和风天气图标代码
    },
    {
      date: '后天',
      weather: '小雨',
      tempRange: '15°C-22°C',
      windDir: '东风',
      windScale: '2-3级',
      iconDay: '305'  // 使用和风天气图标代码
    }
  ]
}

// 刷新天气数据
const refreshWeather = async () => {
  weatherLoading.value = true
  try {
    await fetchWeatherData()
  } catch (error) {
    console.error("刷新天气数据失败:", error)
  } finally {
    weatherLoading.value = false
  }
}

// 天气数据
const weatherData = ref([
  {
    date: '今天',
    weather: '晴',
    tempRange: '18°C-25°C',
    windDir: '东南风',
    windScale: '1-2级',
    iconDay: '100'  // 使用和风天气图标代码
  },
  {
    date: '明天',
    weather: '多云',
    tempRange: '16°C-24°C',
    windDir: '东南风',
    windScale: '1-2级',
    iconDay: '101'  // 使用和风天气图标代码
  },
  {
    date: '后天',
    weather: '小雨',
    tempRange: '15°C-22°C',
    windDir: '东风',
    windScale: '2-3级',
    iconDay: '305'  // 使用和风天气图标代码
  }
])

// 天气按钮相关
const showWeatherCard = ref(false)
const isWeatherDragging = ref(false)
const weatherPosition = ref({ x: '20px', y: '20px', right: 'auto', bottom: 'auto' })
const weatherCardPosition = ref({ x: '90px', y: '20px', right: 'auto', bottom: 'auto' })

// 切换天气卡片显示状态
const toggleWeather = () => {
  showWeatherCard.value = !showWeatherCard.value

  if (showWeatherCard.value) {
    // 如果是显示卡片，在下一个DOM更新周期更新位置
    nextTick(() => {
      updateWeatherCardPosition()
    })
  }
}

// 拖拽天气按钮开始
const handleWeatherDragStart = (event: DragEvent) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', '天气按钮')
    isWeatherDragging.value = true

    // 保存鼠标在元素内的偏移量，用于精确定位
    const target = event.target as HTMLElement
    const rect = target.getBoundingClientRect()
    const offsetX = event.clientX - rect.left
    const offsetY = event.clientY - rect.top

    event.dataTransfer.setData('offset', JSON.stringify({ x: offsetX, y: offsetY }))
  }
}

// 拖拽天气按钮结束
const handleWeatherDragEnd = (event: DragEvent) => {
  isWeatherDragging.value = false

  // 获取鼠标在元素内的偏移量
  let offsetX = 30, offsetY = 30
  if (event.dataTransfer) {
    try {
      const offset = JSON.parse(event.dataTransfer.getData('offset'))
      offsetX = offset.x
      offsetY = offset.y
    } catch (e) {
      console.error('Error parsing offset data', e)
    }
  }

  // 计算新位置
  const x = event.clientX - offsetX
  const y = event.clientY - offsetY

  // 确保不会拖到窗口外
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  const maxX = windowWidth - 10
  const maxY = windowHeight - 10

  const newX = Math.min(Math.max(0, x), maxX)
  const newY = Math.min(Math.max(0, y), maxY)

  // 更新位置
  weatherPosition.value = {
    x: `${newX}px`,
    y: `${newY}px`,
    right: 'auto',
    bottom: 'auto'
  }

  // 更新天气卡片位置
  if (showWeatherCard.value) {
    updateWeatherCardPosition()
  }
}

// 更新天气卡片位置
const updateWeatherCardPosition = () => {
  nextTick(() => {
    const buttonElement = document.querySelector('.weather-button') as HTMLElement
    const cardElement = document.querySelector('.weather-card') as HTMLElement

    if (buttonElement && cardElement) {
      const rect = buttonElement.getBoundingClientRect()
      const buttonX = rect.left
      const buttonY = rect.top
      const buttonWidth = rect.width
      const buttonHeight = rect.height

      // 默认天气卡片在按钮右侧显示
      let cardLeft = buttonX + buttonWidth - 200
      let cardTop = buttonY

      // 天气卡片宽度
      const cardWidth = 420

      // 获取视窗宽度
      const windowWidth = window.innerWidth

      // 如果天气卡片超出窗口右侧，则显示在按钮左侧
      if (cardLeft + cardWidth > windowWidth ) {
        cardLeft = buttonX - cardWidth - 10
        cardElement.classList.add('weather-card-left')
      } else {
        cardElement.classList.remove('weather-card-left')
      }

      // 更新卡片位置
      weatherCardPosition.value = {
        x: `${cardLeft}px`,
        y: `${cardTop}px`,
        right: 'auto',
        bottom: 'auto'
      }
    }
  })
}

// AI助手相关
const showAssistantChat = ref(false)
const userInput = ref('')
const chatMessages = ref([
  {
    content: '您好！我是智能排课助手，有任何关于排课系统的问题，都可以问我哦！',
    isUser: false
  }
])
const chatMessagesRef = ref(null)

// AI助手位置
const assistantPosition = ref({ x: 'auto', y: 'auto', right: '30px', bottom: '30px' })
const isDragging = ref(false)

// AI助手提示气泡
const showTip = ref(false)
const currentTip = ref('')
const lastTipIndex = ref(-1)
const tipShowCount = ref(0)
const tipMessages = [
  '点击我，有惊喜！',
  '需要帮助排课吗？',
  '有问题问我吧！',
  '智能助手随时为您服务',
  '不知道如何开始？点我咨询',
  '排课困难？AI来帮忙',
  '我能回答您的所有问题',
  '新功能上线啦，问我了解详情'
]

// 获取随机不重复的提示
const getRandomTip = () => {
  // 避免连续显示相同的提示
  let index
  do {
    index = Math.floor(Math.random() * tipMessages.length)
  } while (index === lastTipIndex.value && tipMessages.length > 1)

  lastTipIndex.value = index
  return tipMessages[index]
}

// 定时展示提示气泡
const startTipInterval = () => {
  // 初始显示，首次延迟3秒显示
  setTimeout(() => {
    if (!showAssistantChat.value) {
      currentTip.value = getRandomTip()
      showTip.value = true
      tipShowCount.value++

      // 5秒后隐藏
      setTimeout(() => {
        showTip.value = false
      }, 5000)
    }
  }, 3000)

  // 定期显示，根据用户互动调整显示频率
  setInterval(() => {
    if (!showAssistantChat.value && !showTip.value) {
      // 根据用户交互次数调整显示几率
      let showProbability = 0.1 // 基础几率10%

      // 如果用户尚未点击过助手，增加几率
      if (tipShowCount.value > 3 && tipShowCount.value % 3 === 0) {
        showProbability = 0.25  // 增加到25%
      }

      // 随机决定是否显示
      if (Math.random() < showProbability) {
        currentTip.value = getRandomTip()
        showTip.value = true
        tipShowCount.value++

        // 5秒后隐藏
        setTimeout(() => {
          showTip.value = false
        }, 5000)
      }
    }
  }, 15000) // 每15秒检查一次是否显示
}

// 升级提示文本，根据用户交互提供更个性化的建议
const updateTipsBasedOnUsage = () => {
  // 如果用户点击过助手，替换一些基本提示为更高级的提示
  if (chatMessages.value.length > 2) {
    // 用户已经进行过对话，添加一些针对性更强的提示
    if (tipMessages.indexOf('我还可以帮您解决课程冲突问题') === -1) {
      tipMessages.push('我还可以帮您解决课程冲突问题')
      tipMessages.push('需要查看更多排课技巧吗？')
      tipMessages.push('您上次的问题解决了吗？')
    }
  }
}

const toggleAssistant = () => {
  showAssistantChat.value = !showAssistantChat.value
  showTip.value = false

  if (showAssistantChat.value) {
    nextTick(() => {
      scrollToBottom()
      updateChatPosition()
    })
  } else {
    // 用户关闭了聊天窗口，等待一会再显示提示
    setTimeout(() => {
      if (!showAssistantChat.value) {
        currentTip.value = '有其他问题随时问我哦！'
        showTip.value = true

        setTimeout(() => {
          showTip.value = false
        }, 4000)
      }
    }, 3000)
  }

  // 更新提示文本
  updateTipsBasedOnUsage()
}

// 拖拽助手开始
const handleDragStart = (event: DragEvent) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', 'AI助手')
    // 设置拖拽时的透明度
    isDragging.value = true

    // 保存鼠标在元素内的偏移量，用于精确定位
    const target = event.target as HTMLElement
    const rect = target.getBoundingClientRect()
    const offsetX = event.clientX - rect.left
    const offsetY = event.clientY - rect.top

    event.dataTransfer.setData('offset', JSON.stringify({ x: offsetX, y: offsetY }))
  }
}

// 拖拽结束
const handleDragEnd = (event: DragEvent) => {
  isDragging.value = false

  // 获取鼠标在元素内的偏移量
  let offsetX = 30, offsetY = 30
  if (event.dataTransfer) {
    try {
      const offset = JSON.parse(event.dataTransfer.getData('offset'))
      offsetX = offset.x
      offsetY = offset.y
    } catch (e) {
      console.error('Error parsing offset data', e)
    }
  }

  // 计算新位置
  const x = event.clientX - offsetX
  const y = event.clientY - offsetY

  // 确保不会拖到窗口外
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  const maxX = windowWidth - 60
  const maxY = windowHeight - 60

  const newX = Math.min(Math.max(0, x), maxX)
  const newY = Math.min(Math.max(0, y), maxY)

  // 更新位置并重置fixed定位方式
  assistantPosition.value = {
    x: `${newX}px`,
    y: `${newY}px`,
    right: 'auto',
    bottom: 'auto'
  }

  // 更新聊天窗口位置
  if (showAssistantChat.value) {
    updateChatPosition()
  }
}

// 更新聊天窗口位置
const updateChatPosition = () => {
  nextTick(() => {
    const assistantElement = document.querySelector('.ai-assistant') as HTMLElement
    const chatElement = document.querySelector('.chat-container') as HTMLElement

    if (assistantElement && chatElement) {
      const rect = assistantElement.getBoundingClientRect()
      const assistantX = rect.left
      const assistantY = rect.top
      const assistantWidth = rect.width
      const assistantHeight = rect.height

      // 确定聊天窗口位置，避免超出屏幕
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight
      const chatWidth = 350 // 聊天窗口宽度
      const chatHeight = 450 // 聊天窗口高度

      // 默认聊天窗口在助手左侧
      let chatLeft = assistantX - chatWidth - 10
      let chatTop = assistantY - (chatHeight / 2) + (assistantHeight / 2)
      let isRightPosition = false

      // 如果左侧放不下（靠近左边屏幕边缘），放到右侧
      if (chatLeft < 10) {
        chatLeft = assistantX + assistantWidth + 10
        isRightPosition = true
      }

      // 如果顶部会超出屏幕
      if (chatTop < 10) {
        chatTop = 10
      }

      // 如果底部会超出屏幕
      if (chatTop + chatHeight > windowHeight - 10) {
        chatTop = windowHeight - chatHeight - 10
      }

      // 应用位置
      chatElement.style.left = `${chatLeft}px`
      chatElement.style.top = `${chatTop}px`
      chatElement.style.right = 'auto'
      chatElement.style.bottom = 'auto'

      // 更新连接器位置
      if (isRightPosition) {
        chatElement.classList.add('chat-right-position')
      } else {
        chatElement.classList.remove('chat-right-position')
      }
    }
  })
}

// 发送消息
const sendMessage = () => {
  if (!userInput.value.trim()) return

  // 添加用户消息
  chatMessages.value.push({
    content: userInput.value,
    isUser: true
  })

  // 模拟AI回复
  setTimeout(() => {
    // 基于用户输入内容提供更智能的回复
    let response = '我正在分析您的问题...'

    if (userInput.value.includes('排课') || userInput.value.includes('教室')) {
      response = '您可以使用系统的排课功能来安排课程，需要我为您演示具体步骤吗？'
    } else if (userInput.value.includes('培训') || userInput.value.includes('教程')) {
      response = '我们提供了完整的新手培训课程，您可以在首页的"新手排课人员智能培训"卡片中查看详细内容。'
    } else if (userInput.value.includes('AI') || userInput.value.includes('智能')) {
      response = '我们的AI系统可以根据历史数据、教室容量、教师偏好等多方面因素自动优化排课方案，提高教室利用率并减少冲突。'
    } else {
      const aiResponses = [
        '您可以通过点击"创建课表"按钮开始新的排课工作。',
        '系统支持手动排课和AI自动排课两种方式，您可以根据需要选择。',
        '如果遇到课程冲突，系统会自动提示并提供解决方案。',
        '教室利用率可以在首页的热力图中直观查看。',
        '您可以在"设置"中调整排课的优先级和约束条件。'
      ]
      response = aiResponses[Math.floor(Math.random() * aiResponses.length)]
    }

    chatMessages.value.push({
      content: response,
      isUser: false
    })

    nextTick(() => {
      scrollToBottom()
    })
  }, 600)

  userInput.value = ''
}

const scrollToBottom = () => {
  if (chatMessagesRef.value) {
    const el = chatMessagesRef.value as HTMLElement
    el.scrollTop = el.scrollHeight
  }
}

// 监听窗口大小变化，防止助手位置超出屏幕
const handleResize = () => {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  const assistantElement = document.querySelector('.ai-assistant') as HTMLElement
  if (assistantElement) {
    const rect = assistantElement.getBoundingClientRect()

    // 如果助手位置超出屏幕，重新调整
    if (rect.right > windowWidth || rect.bottom > windowHeight) {
      const newX = Math.min(rect.left, windowWidth - 60)
      const newY = Math.min(rect.top, windowHeight - 60)

      assistantPosition.value = {
        x: `${newX}px`,
        y: `${newY}px`,
        right: 'auto',
        bottom: 'auto'
      }
    }

    // 如果聊天窗口打开，也更新聊天窗口位置
    if (showAssistantChat.value) {
      updateChatPosition()
    }
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  initHeatmap()
  window.addEventListener('resize', resizeHeatmap)
  startTipInterval()  // 启动提示气泡定时器

  // 初始化天气按钮位置
  updateWeatherButtonPosition()

  // 如果天气卡片已显示，更新位置
  if (showWeatherCard.value) {
    nextTick(() => {
      updateWeatherCardPosition()
    })
  }

  // 监听窗口大小变化，更新天气卡片位置
  window.addEventListener('resize', () => {
    if (showWeatherCard.value) {
      updateWeatherCardPosition()
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('resize', updateWeatherCardPosition)
})

// 新手培训相关
const showTrainingDialog = ref(false)
const activeStep = ref(0)

const showTrainingGuide = () => {
  showTrainingDialog.value = true
  activeStep.value = 0
}

const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}

const nextStep = () => {
  if (activeStep.value < 3) {
    activeStep.value++
  }
}

const finishTraining = () => {
  showTrainingDialog.value = false
  activeStep.value = 0
}

// 热力图相关
const heatmapRef = ref(null)
let heatmapChart: echarts.ECharts | null = null

onMounted(() => {
  initHeatmap()
  window.addEventListener('resize', resizeHeatmap)
})

const initHeatmap = () => {
  if (heatmapRef.value) {
    heatmapChart = echarts.init(heatmapRef.value as HTMLElement)

    // 生成教室数据
    const rooms = ['教室A101', '教室A102', '教室B201', '教室B202', '教室C301', '教室C302', '实验室D101', '多媒体室E101']
    const hours = ['8:00', '9:00', '10:00', '11:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00']

    const data = []
    for (let i = 0; i < rooms.length; i++) {
      for (let j = 0; j < hours.length; j++) {
        // 随机生成使用率数据
        const value = Math.floor(Math.random() * 100)
        data.push([j, i, value])
      }
    }

    const option = {
      tooltip: {
        position: 'top',
        formatter: function (params: any) {
          return `${rooms[params.data[1]]} ${hours[params.data[0]]}<br>使用率: ${params.data[2]}%`
        }
      },
      grid: {
        top: '10%',
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: hours,
        splitArea: {
          show: true
        },
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'category',
        data: rooms,
        splitArea: {
          show: true
        }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '0%',
        inRange: {
          color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
        },
        show: false
      },
      series: [{
        name: '教室使用率',
        type: 'heatmap',
        data: data,
        label: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }

    heatmapChart.setOption(option)
  }
}

const resizeHeatmap = () => {
  if (heatmapChart) {
    heatmapChart.resize()
  }
}

// 系统公告相关
const hasNewAnnouncement = ref(true)
const showAnnouncementDialog = ref(false)
const currentAnnouncement = ref({
  id: '',
  title: '',
  brief: '',
  content: '',
  date: '',
  source: '',
  isRead: false
})

// 公告数据
const announcements = ref([
  {
    id: '1',
    title: '系统升级通知',
    brief: '排课系统将于6月1日进行功能升级，届时系统将暂停服务2小时',
    content: '<p>尊敬的用户：</p><p>为了提供更好的服务体验，我们计划于2023年6月1日 22:00-24:00 对系统进行升级维护。升级期间系统将暂停服务，请提前做好相关工作安排。</p><p>本次升级将带来以下新功能：</p><ul><li>AI自动排课算法优化，提高排课效率</li><li>新增教室资源利用分析报表</li><li>优化用户界面，提升使用体验</li></ul><p>感谢您的理解与支持！</p>',
    date: '2023-05-25',
    source: '信息中心',
    isRead: false
  },
  {
    id: '2',
    title: '2023-2024学年第一学期排课工作开始',
    brief: '请各院系于6月15日前完成下学期课程信息录入工作',
    content: '<p>各院系排课负责人：</p><p>2023-2024学年第一学期排课工作现已启动，请各院系于6月15日前完成课程信息录入，包括课程名称、学分、学时、教师信息等。</p><p>重要时间节点：</p><ul><li>6月15日：完成课程信息录入</li><li>6月20日：完成教师排课偏好设置</li><li>6月30日：系统生成初步排课方案</li><li>7月10日：各院系确认排课方案</li></ul><p>如有疑问，请联系教务处李老师（电话：123-4567）</p>',
    date: '2023-05-20',
    source: '教务处',
    isRead: false
  },
  {
    id: '3',
    title: '关于举办排课系统使用培训的通知',
    brief: '将于下周三下午2点在图书馆报告厅举办排课系统使用培训',
    content: '<p>为帮助新上岗的排课人员熟悉系统操作，我们将举办专题培训讲座。</p><p>培训详情：</p><ul><li>时间：2023年5月31日 14:00-16:00</li><li>地点：图书馆三楼报告厅</li><li>内容：系统基本操作、高级功能使用、常见问题解答</li></ul><p>请各院系安排相关人员参加。培训将提供实操环节，请携带笔记本电脑。</p>',
    date: '2023-05-18',
    source: '教务处',
    isRead: true
  }
])

// 打开公告详情
const openAnnouncementDetail = (announcement) => {
  currentAnnouncement.value = announcement
  showAnnouncementDialog.value = true
}

// 标记为已读
const markAsRead = () => {
  const index = announcements.value.findIndex(item => item.id === currentAnnouncement.value.id)
  if (index !== -1) {
    announcements.value[index].isRead = true
    // 检查是否还有未读公告
    const unreadExists = announcements.value.some(item => !item.isRead)
    hasNewAnnouncement.value = unreadExists
  }
  showAnnouncementDialog.value = false
}

// 确保天气按钮位置正确
const updateWeatherButtonPosition = () => {
  // 确保按钮初始位置在可见区域
  weatherPosition.value = {
    x: '20px',
    y: '20px',
    right: 'auto',
    bottom: 'auto'
  }

  // 动画效果提示用户注意天气按钮
  setTimeout(() => {
    const buttonElement = document.querySelector('.weather-button') as HTMLElement
    if (buttonElement) {
      buttonElement.classList.add('weather-attention')
      setTimeout(() => {
        buttonElement.classList.remove('weather-attention')
      }, 2000)
    }
  }, 1000)
}

// 视频演示相关
const showVideoDialog = ref(false)
const videoRef = ref<HTMLVideoElement | null>(null)
const currentVideo = ref({
  title: '',
  url: '',
  poster: '',
  description: '',
  tags: [] as string[]
})

// 假的视频数据
const videoData: Record<string, {
  url: string;
  poster: string;
  description: string;
  tags: string[];
}> = {
  '创建学期': {
    url: 'https://media.w3.org/2010/05/sintel/trailer.mp4', // 使用公开的示例视频
    poster: 'https://picsum.photos/800/450?random=1', // 使用随机图片作为封面
    description: '本视频将指导您如何在系统中创建新学期，包括设置学期名称、起止日期和教学周数等关键步骤。',
    tags: ['基础操作', '学期管理', '入门必看']
  },
  '导入课程': {
    url: 'https://media.w3.org/2010/05/bunny/movie.mp4',
    poster: 'https://picsum.photos/800/450?random=2',
    description: '通过本视频了解如何快速导入课程信息，包括Excel批量导入、手动创建和从历史学期复制等多种方式。',
    tags: ['课程管理', '数据导入', '批量操作']
  },
  '手动排课': {
    url: 'https://media.w3.org/2010/05/video/movie_300.mp4',
    poster: 'https://picsum.photos/800/450?random=3',
    description: '学习如何使用拖拽方式手动排课，以及如何处理课程冲突和教室分配问题。',
    tags: ['拖拽排课', '冲突检测', '操作技巧']
  },
  '全部基础操作': {
    url: 'https://media.w3.org/2010/05/sintel/trailer.mp4',
    poster: 'https://picsum.photos/800/450?random=4',
    description: '完整演示从创建学期到完成排课的全部基础操作流程，适合新手用户全面了解系统功能。',
    tags: ['完整流程', '入门教程', '基础知识']
  }
}

// 显示视频演示
const showDemoVideo = (title: string) => {
  showVideoDialog.value = true
  currentVideo.value.title = `${title}演示`

  // 从预设数据中获取视频信息，如果没有则使用默认值
  if (videoData[title]) {
    currentVideo.value.url = videoData[title].url
    currentVideo.value.poster = videoData[title].poster
    currentVideo.value.description = videoData[title].description
    currentVideo.value.tags = videoData[title].tags
  } else {
    // 默认视频信息
    currentVideo.value.url = 'https://media.w3.org/2010/05/sintel/trailer.mp4'
    currentVideo.value.poster = 'https://picsum.photos/800/450?random=0'
    currentVideo.value.description = `这是一个关于${title}的视频演示，向您展示相关功能的使用方法和技巧。`
    currentVideo.value.tags = ['排课系统', '操作演示', '使用教程']
  }
}

// 视频结束
const videoEnded = () => {
  console.log('视频播放结束')
  // 可以在这里添加视频结束后的逻辑，比如显示一个提示或者自动关闭弹窗
}

// 关闭视频对话框
const closeVideoDialog = () => {
  showVideoDialog.value = false
}

// 视频重放
const replayVideo = () => {
  if (videoRef.value) {
    videoRef.value.currentTime = 0
    videoRef.value.play()
  }
}
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.home-container {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  background-color: #f5f7fa;
  color: #303133;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
}

/* 头部导航栏样式 */
.header {
  background-color: #409EFF;
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  font-size: 24px;
  font-weight: bold;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 15px;
  color: #409EFF;
}

/* 主内容区域布局 */
.main-container {
  display: flex;
  flex: 1;
  position: relative;
  min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
  width: 220px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px 0;
  z-index: 5;
}

.menu-item {
  padding: 14px 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.menu-item:hover {
  background-color: #ecf5ff;
  color: #409EFF;
}

.menu-item.active {
  background-color: #ecf5ff;
  color: #409EFF;
  border-right: 3px solid #409EFF;
}

.menu-icon {
  margin-right: 10px;
  font-size: 18px;
  width: 24px;
  text-align: center;
}

/* 内容区样式 */
.content {
  flex: 1;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 天气按钮和卡片样式 */
.weather-button {
  position: absolute;
  top: 20px;
  right: auto;
  width: 50px;
  height: 50px;
  background: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
}

.weather-button i {
  font-size: 24px;
  color: #409EFF;
}

.weather-button:hover {
  transform: scale(1.1);
}

.weather-button-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(64, 158, 255, 0.1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.weather-card {
  position: absolute;
  background: white;
  border-radius: 12px;
  padding: 20px;
  width: 420px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 9;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.weather-connector {
  position: absolute;
  top: 25px;
  right: -10px;
  width: 20px;
  height: 20px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 3px -3px 5px rgba(0, 0, 0, 0.05);
}

.weather-card-left .weather-connector {
  right: auto;
  left: -10px;
  box-shadow: -3px -3px 5px rgba(0, 0, 0, 0.05);
}

.weather-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.weather-card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.weather-card-header h3 [class^="qi-"] {
  color: #409EFF;
  font-size: 22px;
  margin-right: 8px;
}

/* 和风天气图标样式 */
[class^="qi-"] {
  font-size: 24px;
  line-height: 1;
  display: inline-block;
}

.refresh-icon {
  cursor: pointer;
  color: #409EFF;
  margin-right: 5px;
  transition: all 0.3s;
}

.refresh-icon:hover {
  transform: rotate(180deg);
}

.location-icon {
  color: #ff6b6b;
  margin-right: 5px;
}

.weather-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.update-time {
  font-size: 12px;
  color: #999;
}

.weather-content {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.weather-loading {
  width: 100%;
  text-align: center;
  padding: 20px 0;
  color: #409EFF;
}

.weather-day {
  text-align: center;
  flex: 1;
  min-width: 110px;
  max-width: 33.33%;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.weather-day:hover {
  background-color: #f5f7fa;
}

.weather-date {
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.weather-icon {
  font-size: 28px;
  color: #f39c12;
  margin-bottom: 8px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.weather-icon i[class^="qi-"] {
  font-size: 36px;
  color: #f39c12;
}

.weather-icon-img {
  width: 40px;
  height: 40px;
}

.weather-info {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.weather-text {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.weather-temp {
  color: #409EFF;
  font-weight: 500;
  font-size: 14px;
}

.weather-wind {
  font-size: 12px;
  color: #777;
}

.weather-ai-note {
  padding: 10px;
  background: #fff7e6;
  border-left: 3px solid #e6a23c;
  border-radius: 4px;
  color: #e6a23c;
  font-size: 13px;
  margin-top: 12px;
  display: flex;
  align-items: center;
}

.weather-ai-note i {
  margin-right: 6px;
  font-size: 15px;
}

/* AI助手样式 */
.ai-assistant {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background: #409EFF;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.4);
  cursor: pointer;
  z-index: 100;
  transition: all 0.3s ease;
}

.ai-assistant i {
  font-size: 28px;
  color: white;
}

.ai-assistant:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 24px rgba(64, 158, 255, 0.5);
}

.assistant-tip {
  position: absolute;
  bottom: 75px;
  right: 0;
  width: 250px;
  background: white;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: #555;
  animation: fade-in 0.5s ease;
}

.tip-content {
  margin-bottom: 0;
}

.tip-arrow {
  position: absolute;
  bottom: -8px;
  right: 25px;
  width: 16px;
  height: 16px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 5px rgba(0, 0, 0, 0.05);
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chat-container {
  position: fixed;
  bottom: 100px;
  right: 30px;
  width: 350px;
  height: 450px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 99;
  animation: slide-up 0.3s ease;
  overflow: hidden;
}

@keyframes slide-up {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.chat-connector {
  position: absolute;
  bottom: -8px;
  right: 30px;
  width: 16px;
  height: 16px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 5px rgba(0, 0, 0, 0.05);
}

.chat-header {
  padding: 15px;
  background: #409EFF;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px 12px 0 0;
}

.chat-body {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-message {
  display: flex;
  gap: 10px;
  max-width: 80%;
}

.chat-message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #f0f8ff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.chat-message.user .message-avatar {
  background: #e6f7ff;
}

.message-avatar i {
  color: #409EFF;
  font-size: 18px;
}

.message-content {
  padding: 12px;
  border-radius: 12px;
  background: #f5f7fa;
  font-size: 14px;
  line-height: 1.5;
}

.chat-message.user .message-content {
  background: #ecf5ff;
}

.chat-footer {
  padding: 10px 15px;
  display: flex;
  gap: 10px;
  border-top: 1px solid #eee;
}

.chat-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  font-size: 14px;
}

.chat-input:focus {
  border-color: #409EFF;
}

.send-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409EFF;
  border: none;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
}

.send-button:hover {
  background: #3a8ee6;
}

/* 培训指南弹窗样式 */
.training-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background: #409EFF;
  color: white;
  border-radius: 8px 8px 0 0;
}

.training-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 500;
  font-size: 18px;
}

.training-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
}

.training-dialog :deep(.el-dialog__body) {
  padding: 30px;
}

.training-steps {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.step-content {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.step-content h3 {
  margin-top: 0;
  color: #333;
  font-size: 18px;
  margin-bottom: 15px;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 25px;
}

.feature-item {
  display: flex;
  gap: 15px;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.feature-item .el-icon {
  color: #409EFF;
  font-size: 24px;
  margin-top: 3px;
}

.feature-item h4 {
  margin: 0 0 8px;
  color: #333;
}

.feature-item p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.advanced-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.advanced-feature {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.advanced-feature h4 {
  margin-top: 0;
  color: #333;
}

.advanced-feature p {
  color: #666;
  font-size: 14px;
  margin-bottom: 15px;
}

.ai-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.ai-feature {
  display: flex;
  gap: 15px;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ai-feature .el-icon {
  color: #409EFF;
  font-size: 24px;
  margin-top: 3px;
}

.ai-feature h4 {
  margin: 0 0 8px;
  color: #333;
}

.ai-feature p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 公告弹窗样式 */
.announcement-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background: #409EFF;
  color: white;
  border-radius: 8px 8px 0 0;
}

.announcement-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 500;
  font-size: 18px;
}

.announcement-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
}

.announcement-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.announcement-detail {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.announcement-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eee;
}

.announcement-content {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

@media (max-width: 768px) {
  .features-list,
  .advanced-features,
  .ai-features {
    grid-template-columns: 1fr;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
  }

  .weather-card {
    width: 280px;
  }

  .chat-container {
    width: 300px;
    height: 400px;
  }
}

/* 错落有致的卡片布局 */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(12, 60px);
  grid-gap: 16px;
  margin-top: 10px;
  position: relative;
  height: calc(100vh - 70px);
  padding: 0 0 20px 0;
}

/* 卡片通用样式 */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-title i {
  color: #409EFF;
}

/* 快速操作区域样式 */
.quick-actions {
  min-height: 180px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: calc(33.33% - 10px);
  height: 90px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  background: #e6f7ff;
  transform: translateY(-3px);
}

.action-button i {
  font-size: 24px;
  color: #409EFF;
  margin-bottom: 8px;
}

.action-button span {
  font-size: 14px;
  color: #555;
}

/* 待办事项样式 */
.todo-card {
  min-height: 300px;
}

.todo-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 10px;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-checkbox {
  margin-right: 10px;
  font-size: 18px;
  color: #999;
  cursor: pointer;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.todo-deadline {
  font-size: 12px;
  color: #999;
}

.todo-priority {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.todo-priority.high {
  background: #fff0f0;
  color: #f56c6c;
}

.todo-priority.medium {
  background: #fff7e6;
  color: #e6a23c;
}

.todo-priority.low {
  background: #f0f9eb;
  color: #67c23a;
}

.todo-footer {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

.add-todo-btn, .view-all-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  border: none;
}

.add-todo-btn {
  background: #409EFF;
  color: white;
}

.view-all-btn {
  background: transparent;
  color: #409EFF;
}

/* 排课进度概览样式 */
.progress-card {
  min-height: 300px;
}

.progress-overview {
  flex: 1;
}

.progress-item {
  margin-bottom: 12px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
}

.progress-bar {
  height: 8px;
  background: #eee;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar.total {
  height: 10px;
}

.progress-fill {
  height: 100%;
  background: #409EFF;
  border-radius: 4px;
}

.progress-fill.total {
  background: linear-gradient(90deg, #409EFF, #67C23A);
}

.completion-date {
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}

.completion-date span {
  color: #409EFF;
  font-weight: 500;
}

/* 培训卡片样式 */
.training-card {
  min-height: 200px;
}

.tutorial-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.tutorial-item {
  display: flex;
  align-items: center;
  width: calc(50% - 8px);
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.2s;
}

.tutorial-item:hover {
  background: #e6f7ff;
  transform: translateY(-3px);
}

.tutorial-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #e6f7ff;
  border-radius: 50%;
  margin-right: 12px;
}

.tutorial-icon i {
  color: #409EFF;
  font-size: 18px;
}

.tutorial-info {
  flex: 1;
}

.tutorial-title {
  font-weight: 500;
  margin-bottom: 4px;
  font-size: 14px;
}

.tutorial-desc {
  color: #666;
  font-size: 12px;
}

/* 公告卡片样式 */
.announcement-card {
  min-height: 180px;
}

.notices {
  flex: 1;
  overflow-y: auto;
}

.notice-item {
  padding: 12px 0;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: all 0.2s;
}

.notice-item:hover {
  background: #f8f9fa;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-title {
  font-weight: 500;
  margin-bottom: 5px;
  font-size: 14px;
}

.notice-content {
  color: #666;
  margin-bottom: 5px;
  font-size: 13px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notice-time {
  font-size: 12px;
  color: #999;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .tutorial-item {
    width: 100%;
  }

  .action-button {
    width: calc(50% - 8px);
  }
}

/* 热力图样式 */
.heatmap-container {
  width: 100%;
  height: 100%;
  min-height: 320px;
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.heatmap-card:hover .heatmap-container {
  filter: brightness(1.05);
  transform: scale(1.02);
}

/* 天气按钮吸引注意力的动画 */
.weather-attention {
  animation: attention-bounce 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) 3;
}

@keyframes attention-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* 基础样式 */
.home-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  color: #333;
  position: relative;
}

.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  position: relative;
}

/* 网格布局相关样式 */
.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.dashboard-row {
  display: flex;
  gap: 20px;
  width: 100%;
}

.grid-item {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.grid-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.large-width {
  flex: 3;
}

.medium-width {
  flex: 2;
}

/* 响应式布局调整 */
@media (max-width: 1200px) {
  .dashboard-row {
    flex-direction: column;
  }

  .grid-item {
    width: 100%;
    margin-bottom: 20px;
  }

  .tutorial-item {
    width: calc(50% - 8px);
  }
}

@media (max-width: 768px) {
  .dashboard-grid {
    padding: 10px;
  }

  .tutorial-item,
  .action-button {
    width: 100%;
  }

  .weather-button {
    top: 10px;
    right: 10px;
  }

  .weather-card {
    width: 280px;
    right: 10px;
  }

  .chat-container {
    width: 300px;
    height: 400px;
    right: 10px;
  }

  .ai-assistant {
    right: 20px;
    bottom: 20px;
  }
}

.weather-card-left .weather-connector {
  right: auto;
  left: -10px;
  box-shadow: -3px -3px 5px rgba(0, 0, 0, 0.05);
}

/* 基础排课操作样式 */
.steps-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 25px 0;
}

.step-item {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.step-number {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #409EFF;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content-box {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
}

.step-content-box:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
  transform: translateY(-3px);
}

.step-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e8f4ff;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #409EFF;
  font-size: 18px;
}

.step-description p {
  margin-bottom: 15px;
  color: #555;
}

.step-tips {
  margin-bottom: 20px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.tip-item i {
  color: #67C23A;
  font-size: 14px;
}

.tip-item span {
  font-size: 14px;
  color: #555;
}

.step-image {
  display: flex;
  justify-content: center;
  margin: 20px 0 10px;
}

.image-container {
  width: 200px;
  height: 150px;
  background: #f0f7ff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.main-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 10px;
}

.image-overlay {
  margin-top: 15px;
  text-align: center;
}

.date-range {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 8px;
}

.date {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.date-divider {
  width: 20px;
  height: 2px;
  background: #409EFF;
}

.weeks-info {
  font-size: 13px;
  color: #555;
}

.step-connector {
  width: 2px;
  height: 25px;
  background: #ddd;
  margin-left: 17px;
}

.step-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.action-button.mini {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 80px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button.mini:hover {
  background: #e6f7ff;
  transform: translateY(-3px);
}

.action-button.mini i {
  font-size: 22px;
  color: #409EFF;
  margin-bottom: 8px;
}

.action-button.mini span {
  font-size: 13px;
  color: #555;
}

.schedule-preview {
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #eee;
  position: relative;
}

.preview-header {
  display: flex;
  background: #f0f7ff;
}

.preview-cell {
  flex: 1;
  padding: 8px;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.time-header {
  background: #e0efff;
  color: #409EFF;
}

.preview-row {
  display: flex;
}

.time-cell {
  background: #f5f7fa;
  color: #666;
  font-size: 12px;
}

.course-cell {
  background: #ecf5ff;
  color: #409EFF;
  font-size: 13px;
}

.drag-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
  padding: 8px 15px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.05);
  animation: pulse-light 2s infinite;
}

.guide-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

@keyframes pulse-light {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

.custom-step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e6f7ff;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #409EFF;
  font-size: 18px;
}

.animated-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 15px;
  transition: all 0.2s;
}

.animated-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
}

.feature-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e6f7ff;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #409EFF;
  font-size: 18px;
}

.feature-icon-container.ai {
  background: #409EFF;
}

.feature-icon-container.small {
  width: 30px;
  height: 30px;
}

.intro-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.step-demo-btn {
  margin-top: 15px;
  background: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.step-demo-btn:hover {
  background: #3a8ee6;
}

/* 视频对话框样式 */
.video-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background: #409EFF;
  color: white;
  border-radius: 8px 8px 0 0;
}

.video-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 500;
  font-size: 18px;
}

.video-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
}

.video-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.video-container {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .video-container {
    flex-direction: row;
  }
}

.video-player {
  flex: 1;
  background: #000;
  border-radius: 0 0 0 8px;
}

.demo-video {
  width: 100%;
  max-height: 450px;
  display: block;
}

.video-info {
  padding: 20px;
  width: 100%;
  max-width: 100%;
  background: #f8f9fa;
  border-radius: 0 0 8px 0;
}

@media (min-width: 768px) {
  .video-info {
    width: 350px;
    max-width: 350px;
  }
}

.video-info h3 {
  margin-top: 0;
  color: #333;
  margin-bottom: 15px;
}

.video-description {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.video-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.video-tag {
  margin-right: 0 !important;
}

.video-controls {
  display: flex;
  gap: 10px;
}

/* 步骤数字脉冲动画 */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}
</style>

