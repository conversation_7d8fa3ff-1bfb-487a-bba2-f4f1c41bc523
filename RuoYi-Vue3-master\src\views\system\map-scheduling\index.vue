<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>地图排课系统</span>
        </div>
      </template>
      
      <!-- 地图容器 -->
      <div class="map-container">
        <div id="map-container" style="width: 100%; height: 500px;"></div>
        <div class="search-box">
          <el-input 
            v-model="searchKeyword" 
            placeholder="输入地点名称搜索" 
            prefix-icon="Search" 
            @keyup.enter="searchLocation"
          >
            <template #append>
              <el-button @click="searchLocation">搜索</el-button>
            </template>
          </el-input>
          <div class="distance-tools" v-if="selectedLocation">
            <el-button 
              type="primary" 
              size="small" 
              :disabled="compareMode" 
              @click="startCompareMode"
            >
              计算距离
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              v-if="distanceResult" 
              @click="cancelCompare"
            >
              取消比较
            </el-button>
          </div>

          <!-- 距离计算结果 -->
          <div class="distance-result" v-if="distanceResult">
            <el-card shadow="hover">
              <div class="distance-info">
                <h4>距离计算结果</h4>
                <p>从: {{ distanceResult.from }}</p>
                <p>到: {{ distanceResult.to }}</p>
                <p class="distance-value">{{ distanceResult.formattedDistance }}</p>
              </div>
            </el-card>
          </div>
        </div>
        
        <!-- 比较模式提示 -->
        <div class="compare-mode-tip" v-if="compareMode">
          请选择第二个位置进行距离计算
        </div>
      </div>
      
      <!-- 位置列表 -->
      <div class="location-list" v-if="locations.length > 0">
        <el-divider content-position="center">可选位置</el-divider>
        <el-row :gutter="12">
          <el-col v-for="location in locations" :key="location.id" :span="8" :xs="24" :sm="12" :md="8">
            <el-card class="location-card" :class="{ 'selected': selectedLocation === location.id, 'office-building': location.isOfficeBuilding }" @click="selectLocation(location)">
              <div class="location-info">
                <h4>{{ location.name }} 
                  <el-tag v-if="location.isOfficeBuilding" type="success" size="small">教师办公楼</el-tag>
                </h4>
                <p>{{ location.address }}</p>
                <p class="classroom-count">可用教室: {{ location.classrooms.length }} 间</p>
                <p v-if="nearestOfficeDistance(location) !== null" class="distance-info">
                  距最近办公楼: {{ (nearestOfficeDistance(location) / 1000).toFixed(2) }}km
                </p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      
      <!-- 排课选项 -->
      <div class="scheduling-options">
        <el-form :model="schedulingForm" label-width="120px">
          <el-divider content-position="center">排课设置</el-divider>
          
          <!-- 新增：两个位置选择框 -->
          <el-form-item label="起始位置">
            <el-select
              v-model="schedulingForm.startLocation"
              placeholder="请选择起始位置"
              style="width: 100%;"
            >
              <el-option
                v-for="item in locations"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                @click="handleStartLocationSelect(item)"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="目标位置">
            <el-select
              v-model="schedulingForm.endLocation"
              placeholder="请选择目标位置"
              style="width: 100%;"
            >
              <el-option
                v-for="item in locations"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                @click="handleEndLocationSelect(item)"
              />
            </el-select>
          </el-form-item>
          
          <!-- 显示位置间距离 -->
          <el-form-item v-if="locationDistance">
            <el-alert
              :title="`位置间距离: ${locationDistance}`"
              type="info"
              :closable="false"
              show-icon
            />
          </el-form-item>
          
          <el-form-item label="排课模式">
            <el-radio-group v-model="schedulingForm.mode">
              <el-radio label="normal">正常排课</el-radio>
              <el-radio label="staggered">错峰排课</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <!-- 教师办公楼选择 -->
          <el-form-item label="教师所在楼">
            <el-select
              v-model="schedulingForm.teacherOffice"
              placeholder="请选择教师所在办公楼"
              style="width: 100%;"
            >
              <el-option
                v-for="item in officeBuildings"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          
          <!-- 错峰排课选项 -->
          <template v-if="schedulingForm.mode === 'staggered'">
            <el-form-item label="错峰方式">
              <el-alert
                title="年级错峰：不同年级在相隔5-10分钟内上下课"
                type="info"
                :closable="false"
                show-icon
              />
            </el-form-item>
            
            <!-- 年级错峰选项 -->
            <!-- <el-form-item label="年级上课时间">
              <el-table :data="gradeTimeTable" style="width: 100%">
                <el-table-column prop="grade" label="年级" />
                <el-table-column prop="startTime" label="上课时间">
                  <template #default="scope">
                    <el-time-select
                      v-model="scope.row.startTime"
                      :step="5"
                      start="07:00"
                      end="22:00"
                      placeholder="请选择时间"
                      @change="updateGradeTimeDiff(scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="endTime" label="下课时间">
                  <template #default="scope">
                    <el-time-select
                      v-model="scope.row.endTime"
                      :step="5"
                      start="07:00"
                      end="22:00"
                      placeholder="请选择时间"
                      @change="updateGradeTimeDiff(scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="与上一年级间隔">
                  <template #default="scope">
                    <el-tag v-if="scope.row.timeDiff !== null" :type="getTimeDiffTagType(scope.row.timeDiff)">
                      {{ scope.row.timeDiff }} 分钟
                    </el-tag>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
             -->
            <el-form-item>
              <el-button type="primary" @click="applyDefaultStaggeredTimes">应用默认错峰时间</el-button>
              <el-button type="success" @click="optimizeStaggeredTimes">优化错峰时间</el-button>
            </el-form-item>
          </template>
          
          <el-form-item label="选择教室">
            <el-select
              v-model="schedulingForm.selectedClassrooms"
              multiple
              placeholder="请选择教室"
              style="width: 100%;"
              :disabled="!selectedLocation"
            >
              <el-option
                v-for="item in selectedLocationClassrooms"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="距离优先级">
            <el-slider
              v-model="schedulingForm.distancePriority"
              :min="0"
              :max="100"
              :format-tooltip="formatDistancePriority"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="submitScheduling">保存排课设置</el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button type="success" @click="addAsOfficeBuilding" v-if="selectedLocation && !isOfficeBuilding(selectedLocation)">
              设为办公楼
            </el-button>
            <el-button type="danger" @click="removeFromOfficeBuilding" v-if="selectedLocation && isOfficeBuilding(selectedLocation)">
              取消办公楼
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { getMapAPI, loadTMapScript, createMarkerStyle, calculateDistance } from '@/utils/mapUtils';

// 获取腾讯地图API密钥
const mapApiKey = getMapAPI();
const searchKeyword = ref('');
let map = null;
let searchService = null;
let markerLayer = null;
const selectedLocation = ref(null);

// 排课表单数据
const schedulingForm = reactive({
  mode: 'normal',
  staggeredOptions: ['grade'], // 默认使用年级错峰，防止卡住
  selectedTimeSlots: [],
  selectedGrades: [],
  selectedMajors: [],
  selectedClasses: [],
  selectedClassrooms: [],
  teacherOffice: null,
  distancePriority: 70, // 距离优先级，0-100
  startLocation: null,  // 新增：起始位置
  endLocation: null     // 新增：目标位置
});

// 添加距离计算相关变量
const compareMode = ref(false);
const compareLocations = ref(null);
const distanceResult = ref(null);

// 时间段定义
const timeSlots = [
  { value: '7:00-8:00', label: '7:00-8:00 (早高峰)', peak: true },
  { value: '8:00-9:00', label: '8:00-9:00 (早高峰)', peak: true },
  { value: '9:00-10:00', label: '9:00-10:00', peak: false },
  { value: '10:00-11:00', label: '10:00-11:00', peak: false },
  { value: '11:00-12:00', label: '11:00-12:00', peak: false },
  { value: '12:00-13:00', label: '12:00-13:00 (午休高峰)', peak: true },
  { value: '13:00-14:00', label: '13:00-14:00', peak: false },
  { value: '14:00-15:00', label: '14:00-15:00', peak: false },
  { value: '15:00-16:00', label: '15:00-16:00', peak: false },
  { value: '16:00-17:00', label: '16:00-17:00', peak: false },
  { value: '17:00-18:00', label: '17:00-18:00 (晚高峰)', peak: true },
  { value: '18:00-19:00', label: '18:00-19:00 (晚高峰)', peak: true },
  { value: '19:00-20:00', label: '19:00-20:00', peak: false },
  { value: '20:00-21:00', label: '20:00-21:00', peak: false },
  { value: '21:00-22:00', label: '21:00-22:00', peak: false }
];

// 模拟数据
const locations = reactive([
  {
    id: 1,
    name: '校本部A区',
    address: '郑州西亚斯学院医学院',
    lat: 34.403339,
    lng: 113.763183,
    isOfficeBuilding: true,
    classrooms: [
      { id: 101, name: 'A101教室' },
      { id: 102, name: 'A102教室' },
      { id: 103, name: 'A103教室' },
      { id: 104, name: 'A104多媒体教室' }
    ]
  },
  {
    id: 2,
    name: '校本部B区',
    address: '郑州西亚斯学院外语学院',
    lat: 34.403039,
    lng: 113.762683,
    isOfficeBuilding: true,
    classrooms: [
      { id: 201, name: 'B201阶梯教室' },
      { id: 202, name: 'B202阶梯教室' },
      { id: 203, name: 'B203教室' },
      { id: 204, name: 'B204多媒体教室' }
    ]
  },
  {
    id: 3,
    name: '大学城校区',
    address: '郑州西亚斯学院新烟街道',
    lat: 34.75500,
    lng: 113.77000,
    isOfficeBuilding: false,
    classrooms: [
      { id: 301, name: 'C301教室' },
      { id: 302, name: 'C302教室' },
      { id: 303, name: 'C303多媒体教室' },
      { id: 304, name: 'C304实验室' }
    ]
  },
  {
    id: 4,
    name: '教学楼D区',
    address: '郑州西亚斯学院新烟街道',
    lat: 34.76050,
    lng: 113.78050,
    isOfficeBuilding: false,
    classrooms: [
      { id: 401, name: 'D101教室' },
      { id: 402, name: 'D102教室' },
      { id: 403, name: 'D103教室' },
      { id: 404, name: 'D104多媒体教室' }
    ]
  },
  {
    id: 5,
    name: '教学楼E区',
    address: '郑州西亚斯学院商学院',
    lat: 34.75750,
    lng: 113.77500,
    isOfficeBuilding: false,
    classrooms: [
      { id: 501, name: 'E101教室' },
      { id: 502, name: 'E102教室' },
      { id: 503, name: 'E103教室' },
      { id: 504, name: 'E104多媒体教室' }
    ]
  }
]);

// 根据isOfficeBuilding属性获取办公楼列表
const officeBuildings = computed(() => {
  return locations.filter(loc => loc.isOfficeBuilding);
});

// 年级和专业选项
const gradeOptions = [
  { value: '2020', label: '2020级' },
  { value: '2021', label: '2021级' },
  { value: '2022', label: '2022级' },
  { value: '2023', label: '2023级' }
];

const majorOptions = [
  { value: 'computer', label: '计算机科学与技术' },
  { value: 'software', label: '软件工程' },
  { value: 'network', label: '网络工程' },
  { value: 'ai', label: '人工智能' },
  { value: 'data', label: '数据科学与大数据技术' }
];

// 班级选项
const classOptions = [
  { value: 'class-1', label: '1班' },
  { value: 'class-2', label: '2班' },
  { value: 'class-3', label: '3班' },
  { value: 'class-4', label: '4班' },
  { value: 'class-5', label: '5班' },
  { value: 'class-6', label: '6班' }
];

// 根据选择的位置筛选教室
const selectedLocationClassrooms = computed(() => {
  if (!selectedLocation.value) return [];
  const location = locations.find(loc => loc.id === selectedLocation.value);
  return location ? location.classrooms : [];
});

// 判断位置是否为办公楼
const isOfficeBuilding = (location) => {
  return location && location.isOfficeBuilding;
};

// 设置当前选中的位置为办公楼
const addAsOfficeBuilding = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择一个位置');
    return;
  }
  
  const location = locations.find(loc => loc.id === selectedLocation.value);
  if (location) {
    location.isOfficeBuilding = true;
    updateMarkerStyles();
    ElMessage.success(`已将 ${location.name} 设置为教师办公楼`);
  }
};

// 取消当前选中位置的办公楼设置
const removeFromOfficeBuilding = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择一个位置');
    return;
  }
  
  const location = locations.find(loc => loc.id === selectedLocation.value);
  if (location) {
    location.isOfficeBuilding = false;
    updateMarkerStyles();
    ElMessage.success(`已取消 ${location.name} 的教师办公楼设置`);
  }
};

// 计算位置距离最近办公楼的距离
const nearestOfficeDistance = (location) => {
  if (!location || isOfficeBuilding(location)) return null;
  
  let minDistance = Infinity;
  officeBuildings.value.forEach(office => {
    const distance = calculateDistance(
      location.lat, location.lng,
      office.lat, office.lng
    );
    if (distance < minDistance) {
      minDistance = distance;
    }
  });
  
  return minDistance === Infinity ? null : minDistance;
};

// 格式化距离优先级提示
const formatDistancePriority = (value) => {
  if (value <= 20) return '低优先级';
  if (value <= 50) return '中低优先级';
  if (value <= 80) return '中高优先级';
  return '高优先级';
};

// 位置间距离
const locationDistance = ref(null);

// 计算和更新两个选择位置之间的距离
const updateLocationDistance = () => {
  if (!schedulingForm.startLocation || !schedulingForm.endLocation) {
    locationDistance.value = null;
    return;
  }
  
  const startLoc = locations.find(loc => loc.id === schedulingForm.startLocation);
  const endLoc = locations.find(loc => loc.id === schedulingForm.endLocation);
  
  if (!startLoc || !endLoc) {
    locationDistance.value = null;
    return;
  }
  
  const distance = calculateDistance(
    startLoc.lat, startLoc.lng,
    endLoc.lat, endLoc.lng
  );
  
  // 格式化距离显示
  locationDistance.value = `${(distance / 1000).toFixed(2)} 公里`;
  
  // 更新地图显示，添加连线
  showLocationConnection(startLoc, endLoc);
};

// 处理起始位置选择
const handleStartLocationSelect = (location) => {
  schedulingForm.startLocation = location.id;
  
  // 自动选中地图上的位置
  selectLocation(location);
  
  // 更新距离计算
  updateLocationDistance();
};

// 处理目标位置选择
const handleEndLocationSelect = (location) => {
  schedulingForm.endLocation = location.id;
  
  // 自动选中地图上的位置
  selectLocation(location);
  
  // 更新距离计算
  updateLocationDistance();
};

// 在地图上显示两个位置的连线
let connectionLine = null;
const showLocationConnection = (startLoc, endLoc) => {
  if (!map || !startLoc || !endLoc) return;
  
  try {
    // 检查是否选择了相同的位置
    if (startLoc.id === endLoc.id) {
      console.log("起始位置和目标位置相同，不需要显示连线");
      return;
    }
    
    // 创建或更新连线图层
    if (connectionLine) {
      // 正确的移除图层方法是setMap(null)
      connectionLine.setMap(null);
      connectionLine = null;
    }
    
    // 创建连线
    connectionLine = new TMap.MultiPolyline({
      map: map,
      styles: {
        'style_blue': new TMap.PolylineStyle({
          color: '#3777FF', // 线颜色
          width: 4, // 线宽
          borderWidth: 1, // 描边宽度
          borderColor: '#FFF', // 描边颜色
          lineCap: 'round' // 线帽样式
        })
      },
      geometries: [{
        id: 'connection',
        styleId: 'style_blue',
        paths: [
          new TMap.LatLng(startLoc.lat, startLoc.lng),
          new TMap.LatLng(endLoc.lat, endLoc.lng)
        ]
      }]
    });
    
    // 确保两点坐标不同，才进行视野调整
    if (startLoc.lat !== endLoc.lat || startLoc.lng !== endLoc.lng) {
      // 调整地图视野确保两点都可见
      const minLat = Math.min(startLoc.lat, endLoc.lat);
      const maxLat = Math.max(startLoc.lat, endLoc.lat);
      const minLng = Math.min(startLoc.lng, endLoc.lng);
      const maxLng = Math.max(startLoc.lng, endLoc.lng);
      
      // 添加边距，确保边界有一定的间距
      const padding = 0.01;
      
      try {
        // 使用地图的视野范围方法安全地调整
        map.panTo(new TMap.LatLng(
          (minLat + maxLat) / 2,
          (minLng + maxLng) / 2
        ));
        
        // 调整缩放等级以适应两个点
        const zoomLevel = 13; // 设置一个合适的缩放等级
        map.setZoom(zoomLevel);
      } catch (error) {
        console.error("调整地图视野失败:", error);
      }
    }
  } catch (error) {
    console.error("显示位置连线失败:", error);
  }
};

// 初始化地图
const initMap = () => {
  // 添加加载状态指示
  const mapContainer = document.getElementById('map-container');
 
  // 手动设置全局错误处理以捕获可能的API错误
  window.onerror = function(message, source, lineno, colno, error) {
    console.error("全局错误:", message, source, lineno, colno, error);
    return false;
  };

  loadTMapScript(() => {
    try {
      console.log("TMap脚本加载成功，开始初始化地图...");
      
      // 先检查TMap对象是否真的可用
      if (!window.TMap) {
        throw new Error("TMap对象未找到，无法初始化地图");
      }
      
      // 设置初始中心位置为郑州西亚斯学院
      const initialCenter = new TMap.LatLng(34.403339, 113.763183);
      
      // 地图初始化
      map = new TMap.Map(document.getElementById('map-container'), {
        center: initialCenter, // 设置为郑州西亚斯学院位置
        zoom: 15,   // 缩放比例设置得更大一些，以便更好地看到校园
        viewMode: '2D'  // 2D模式
      });
      
      console.log("地图创建成功");
      
      // 单独初始化搜索服务
      initSearchService();
      
      // 创建标记图层
      createMarkerLayer();
      
      // 添加地图点击事件
      map.on('click', (evt) => {
        console.log('地图点击事件', evt);
        
        // 如果处于比较模式，则保存点击位置作为第二个比较点
        if (compareMode.value) {
          handleMapClickInCompareMode(evt);
        }
      });
      
    } catch (error) {
      console.error("地图初始化失败:", error);
      ElMessage.error(`地图初始化失败: ${error.message}`);
      
      // 更新加载状态提示
      if (mapContainer) {
        mapContainer.innerHTML = '<div style="display:flex;justify-content:center;align-items:center;height:100%;color:red;font-size:16px;">地图加载失败，请刷新页面重试</div>';
      }
    }
  });
};

// 初始化搜索服务
const initSearchService = () => {
  try {
    // 预加载搜索服务模块
    const script = document.createElement('script');
    script.src = `https://map.qq.com/api/gljs?v=1.exp&libraries=service&key=${mapApiKey}`;
    script.onload = () => {
      console.log("搜索服务模块预加载成功");
    };
    document.body.appendChild(script);
  } catch (error) {
    console.error("预加载搜索服务模块失败:", error);
  }
};

// 创建标记图层
const createMarkerLayer = () => {
  try {
    console.log("开始创建标记图层");
    
    // 检查地图是否准备好
    if (!map) {
      console.error("地图未初始化，无法创建标记图层");
      return;
    }
    
    if (markerLayer) {
      console.log("移除旧的标记图层");
      markerLayer.setMap(null);
    }
    
    // 准备标记点数据
    const markers = locations.map(location => ({
      id: `location_${location.id}`,
      styleId: location.isOfficeBuilding ? 'office' : 'normal',
      position: new TMap.LatLng(location.lat, location.lng),
      properties: {
        title: location.name
      }
    }));
    
    console.log("创建标记样式");
    
    // 创建标记图层
    markerLayer = new TMap.MultiMarker({
      map: map,
      styles: {
        'normal': new TMap.MarkerStyle(createMarkerStyle('#FF0000')),
        'selected': new TMap.MarkerStyle(createMarkerStyle('#0000FF')),
        'office': new TMap.MarkerStyle(createMarkerStyle('#00AA00'))
      },
      geometries: markers
    });
    
    console.log("标记图层创建成功，添加点击事件");
    
    // 添加点击事件
    markerLayer.on('click', (evt) => {
      console.log("标记点被点击:", evt.geometry.id);
      const locationId = parseInt(evt.geometry.id.split('_')[1]);
      selectLocation(locations.find(loc => loc.id === locationId));
    });
    
    console.log("标记图层初始化完成");
  } catch (error) {
    console.error("创建标记图层失败:", error);
    ElMessage.error(`创建标记图层失败: ${error.message}`);
  }
};

// 选择位置
const selectLocation = (location) => {
  selectedLocation.value = location.id;
  
  // 如果选中的是办公楼，自动设置为教师所在楼
  if (location.isOfficeBuilding) {
    schedulingForm.teacherOffice = location.id;
  }
  
  // 根据当前操作设置起始或目标位置
  // 如果两个位置都未设置，则设置为起始位置
  if (!schedulingForm.startLocation) {
    schedulingForm.startLocation = location.id;
    // 触发更新UI，确保下拉选择框反映当前选择
    ElMessage.info(`已选择 ${location.name} 作为起始位置`);
  } 
  // 如果起始位置已设置，且未设置目标位置，则设置为目标位置
  else if (!schedulingForm.endLocation && schedulingForm.startLocation !== location.id) {
    schedulingForm.endLocation = location.id;
    // 触发更新UI，确保下拉选择框反映当前选择
    ElMessage.info(`已选择 ${location.name} 作为目标位置`);
    // 计算两个位置的距离
    updateLocationDistance();
  }
  // 如果两个位置都已设置，则更新起始位置
  else if (schedulingForm.endLocation && schedulingForm.startLocation) {
    // 根据当前操作，决定更新起始位置还是目标位置
    // 如果当前点击的位置与目标位置相同，则更新起始位置
    if (location.id === schedulingForm.endLocation) {
      ElMessage.warning('该位置已设为目标位置，请选择其他位置作为起始位置');
      return;
    }
    
    schedulingForm.startLocation = location.id;
    ElMessage.info(`已更新 ${location.name} 作为起始位置`);
    // 计算两个位置的距离
    updateLocationDistance();
  }
  
  // 更新标记样式
  updateMarkerStyles();
  
  // 将地图中心设置为选中的位置
  map.setCenter(new TMap.LatLng(location.lat, location.lng));
  map.setZoom(16);
};

// 更新所有标记的样式
const updateMarkerStyles = () => {
  if (!markerLayer) return;
  
  try {
    const geometries = markerLayer.getGeometries();
    geometries.forEach(geo => {
      const locationId = parseInt(geo.id.split('_')[1]);
      const location = locations.find(loc => loc.id === locationId);
      if (location) {
        if (locationId === selectedLocation.value) {
          geo.styleId = 'selected';
        } else if (location.isOfficeBuilding) {
          geo.styleId = 'office';
        } else {
          geo.styleId = 'normal';
        }
      }
    });
    
    markerLayer.updateGeometries(geometries);
  } catch (error) {
    console.error("更新标记样式失败:", error);
  }
};

// 搜索位置
const searchLocation = () => {
  if (!searchKeyword.value) {
    ElMessage.warning('请输入搜索关键词');
    return;
  }
  
  if (!map) {
    ElMessage.error('地图尚未初始化，请刷新页面重试');
    return;
  }
  
  ElMessage.info('正在搜索...');
  
  // 使用腾讯地图SDK内置搜索服务
  if (!window.TMap.service || !window.TMap.service.Search) {
    // 如果没有加载搜索服务模块，先加载
    loadSearchServiceModule();
  } else {
    performTMapSearch();
  }
};

// 加载搜索服务模块
const loadSearchServiceModule = () => {
  try {
    // 动态加载搜索服务模块
    const script = document.createElement('script');
    script.src = `https://map.qq.com/api/gljs?v=1.exp&libraries=service&key=${mapApiKey}`;
    script.onload = () => {
      console.log("搜索服务模块加载成功");
      performTMapSearch();
    };
    script.onerror = (error) => {
      console.error("搜索服务模块加载失败:", error);
      ElMessage.error("搜索服务模块加载失败，请刷新页面重试");
    };
    document.body.appendChild(script);
  } catch (error) {
    console.error("加载搜索服务模块失败:", error);
    ElMessage.error("加载搜索服务模块失败");
  }
};

// 使用腾讯地图SDK执行搜索
const performTMapSearch = () => {
  try {
    console.log("使用腾讯地图SDK执行搜索");
    // 确保Search服务可用
    if (!window.TMap.service || !window.TMap.service.Search) {
      throw new Error("搜索服务不可用");
    }
    
    // 创建搜索服务实例
    const searchService = new TMap.service.Search({
      pageCapacity: 10 // 设置每页结果数
    });
    
    // 设置搜索参数
    const searchOption = {
      keyword: searchKeyword.value,  // 搜索关键词
      region: '郑州',               // 搜索区域改为郑州
      page_index: 1,               // 页码
      auto_extend: 1               // 自动扩大搜索范围
    };
    
    // 执行搜索
    searchService.searchPoi(searchOption)
      .then(result => {
        console.log("搜索结果:", result);
        if (result.data && result.data.length > 0) {
          // 处理搜索结果
          handleSearchResults({ data: result.data });
        } else {
          ElMessage.warning("未找到相关位置，请尝试更具体的关键词");
        }
      })
      .catch(error => {
        console.error("搜索失败:", error);
        ElMessage.error(`搜索失败: ${error.message || '未知错误'}`);
        
        // 尝试备用的搜索方式
        tryBackupSearch();
      });
  } catch (error) {
    console.error("执行搜索出错:", error);
    ElMessage.error(`执行搜索出错: ${error.message || '未知错误'}`);
    
    // 尝试备用的搜索方式
    tryBackupSearch();
  }
};

// 备用搜索方法 - 使用腾讯地图WebService API
const tryBackupSearch = () => {
  const url = `https://apis.map.qq.com/ws/geocoder/v1/?address=${encodeURIComponent(searchKeyword.value)}&region=郑州&key=${mapApiKey}&output=jsonp&callback=handleGeocoderResult`;
  
  // 添加全局回调函数
  window.handleGeocoderResult = function(res) {
    try {
      console.log("地理编码结果:", res);
      if (res.status === 0 && res.result) {
        // 处理结果
        const location = res.result.location;
        const formatted_address = res.result.address;
        const title = res.result.title || formatted_address;
        
        const searchResults = {
          data: [{
            title: title,
            address: formatted_address,
            location: {
              lat: location.lat,
              lng: location.lng
            }
          }]
        };
        
        handleSearchResults(searchResults);
      } else {
        ElMessage.warning("未找到相关位置，请尝试更具体的关键词");
      }
    } catch (error) {
      console.error("处理地理编码结果出错:", error);
      ElMessage.error("处理搜索结果失败");
    }
    
    // 清理回调函数
    delete window.handleGeocoderResult;
  };
  
  // 创建script标签执行JSONP请求
  const script = document.createElement('script');
  script.src = url;
  script.onerror = function() {
    console.error("地理编码请求失败");
    ElMessage.error("搜索服务暂时不可用，请稍后再试");
    delete window.handleGeocoderResult;
  };
  document.body.appendChild(script);
  
  // 设置超时清理
  setTimeout(() => {
    if (document.body.contains(script)) {
      document.body.removeChild(script);
    }
    if (window.handleGeocoderResult) {
      delete window.handleGeocoderResult;
    }
  }, 5000);
};

// 处理搜索结果
const handleSearchResults = (res) => {
  console.log("搜索结果:", res);
  if (!res.data || res.data.length === 0) {
    ElMessage.warning('未找到相关位置');
    return;
  }
  
  // 设置地图中心点为第一个搜索结果
  const firstResult = res.data[0];
  console.log("搜索找到位置:", firstResult.title);
  
  const position = new TMap.LatLng(firstResult.location.lat, firstResult.location.lng);
  
  // 确保地图移动到搜索位置
  map.setCenter(position);
  map.setZoom(16);
  
  // 将搜索到的位置添加到列表中
  const newLocation = {
    id: locations.length + 1,
    name: firstResult.title,
    address: firstResult.address || firstResult.title,
    lat: firstResult.location.lat,
    lng: firstResult.location.lng,
    isOfficeBuilding: false,
    classrooms: [
      { id: 1000 + locations.length * 10 + 1, name: `${firstResult.title}教室1` },
      { id: 1000 + locations.length * 10 + 2, name: `${firstResult.title}教室2` },
      { id: 1000 + locations.length * 10 + 3, name: `${firstResult.title}多媒体教室` }
    ]
  };
  
  // 检查是否已存在相同位置
  const exists = locations.some(loc => 
    Math.abs(loc.lat - newLocation.lat) < 0.001 && 
    Math.abs(loc.lng - newLocation.lng) < 0.001
  );
  
  if (!exists) {
    locations.push(newLocation);
    
    // 添加新标记点到地图
    if (markerLayer) {
      console.log("添加新标记点:", newLocation.name);
      markerLayer.add([{
        id: `location_${newLocation.id}`,
        styleId: 'normal',
        position: position,
        properties: {
          title: newLocation.name
        }
      }]);
    } else {
      console.error("标记图层未初始化");
      // 如果标记图层未初始化，尝试重新创建
      createMarkerLayer();
      setTimeout(() => {
        if (markerLayer) {
          markerLayer.add([{
            id: `location_${newLocation.id}`,
            styleId: 'normal',
            position: position,
            properties: {
              title: newLocation.name
            }
          }]);
        }
      }, 500);
    }
    
    // 选中新位置
    selectLocation(newLocation);
    
    ElMessage.success(`已找到并添加: ${firstResult.title}`);
  } else {
    // 如果位置已存在，直接定位并选中
    const existingLocation = locations.find(loc => 
      Math.abs(loc.lat - newLocation.lat) < 0.001 && 
      Math.abs(loc.lng - newLocation.lng) < 0.001
    );
    if (existingLocation) {
      selectLocation(existingLocation);
      ElMessage.info(`该位置已存在: ${existingLocation.name}`);
    }
  }
};

// 提交排课
const submitScheduling = () => {
  try {
    if (!selectedLocation.value) {
      ElMessage.warning('请先选择一个位置');
      return;
    }
    
    if (schedulingForm.selectedClassrooms.length === 0) {
      ElMessage.warning('请选择至少一个教室');
      return;
    }
    
    if (!schedulingForm.teacherOffice) {
      ElMessage.warning('请选择教师所在办公楼');
      return;
    }
    
    // 检查staggeredOptions是否有值但不是数组（修复错峰排课卡死问题）
    if (schedulingForm.mode === 'staggered' && 
        (!Array.isArray(schedulingForm.staggeredOptions) || schedulingForm.staggeredOptions.length === 0)) {
      schedulingForm.staggeredOptions = ['grade']; // 默认使用年级错峰
    }
    
    // 控制台输出排课数据
    console.log('排课设置数据:', {
      location: locations.find(loc => loc.id === selectedLocation.value),
      teacherOffice: locations.find(loc => loc.id === schedulingForm.teacherOffice),
      schedulingData: schedulingForm
    });
    
    ElMessage.success('排课设置保存成功');
  } catch (error) {
    console.error("保存排课设置失败:", error);
    ElMessage.error("保存失败，请检查设置后重试");
  }
};

// 重置表单
const resetForm = () => {
  try {
    schedulingForm.mode = 'normal';
    schedulingForm.staggeredOptions = ['grade'];
    schedulingForm.selectedTimeSlots = [];
    schedulingForm.selectedGrades = [];
    schedulingForm.selectedMajors = [];
    schedulingForm.selectedClasses = [];
    schedulingForm.selectedClassrooms = [];
    schedulingForm.teacherOffice = null;
    schedulingForm.distancePriority = 70;
    schedulingForm.startLocation = null;
    schedulingForm.endLocation = null;
    selectedLocation.value = null;
    
    // 清除连线
    if (connectionLine) {
      try {
        connectionLine.setMap(null);
        connectionLine = null;
      } catch (error) {
        console.error("清除连线失败:", error);
      }
    }
    
    // 清除距离显示
    locationDistance.value = null;
    
    // 重置标记样式
    try {
      updateMarkerStyles();
    } catch (error) {
      console.error("更新标记样式失败:", error);
    }
    
    // 重置地图视图到学校位置
    try {
      if (map) {
        map.panTo(new TMap.LatLng(34.403339, 113.763183));
        map.setZoom(15);
      }
    } catch (error) {
      console.error("重置地图视图失败:", error);
    }
    
    ElMessage.success('已重置表单');
  } catch (error) {
    console.error("重置表单失败:", error);
    ElMessage.error("重置失败，请刷新页面重试");
  }
};

// 在组件挂载后初始化地图
onMounted(() => {
  try {
    console.log("组件挂载，开始初始化地图");
    initMap();
  } catch (error) {
    console.error("地图初始化过程中出错:", error);
    ElMessage.error(`地图初始化失败: ${error.message}`);
  }
});

// 比较两个位置之间的距离
const compareDistance = () => {
  if (!selectedLocation.value || !compareLocations.value) {
    ElMessage.warning('请先选择两个位置进行比较');
    return;
  }
  
  try {
    const location1 = locations.find(loc => loc.id === selectedLocation.value);
    const location2 = locations.find(loc => loc.id === compareLocations.value);
    
    if (!location1 || !location2) {
      ElMessage.warning('位置信息不完整，无法计算距离');
      return;
    }
    
    const distance = calculateDistance(
      location1.lat, location1.lng,
      location2.lat, location2.lng
    );
    
    distanceResult.value = {
      from: location1.name,
      to: location2.name,
      distance: distance,
      formattedDistance: (distance / 1000).toFixed(2) + ' 公里'
    };
    
    ElMessage.success(`已计算 ${location1.name} 到 ${location2.name} 的距离: ${(distance / 1000).toFixed(2)}km`);
  } catch (error) {
    console.error("计算距离时出错:", error);
    ElMessage.error(`计算距离失败: ${error.message}`);
  }
};

// 比较模式下处理地图点击
const handleMapClickInCompareMode = (evt) => {
  if (!compareMode.value) return;
  
  try {
    // 获取点击位置的坐标
    const clickLatLng = evt.latLng;
    console.log("比较模式点击位置:", clickLatLng);
    
    // 创建临时位置对象
    const tempLocation = {
      id: -1, // 临时ID
      name: "点击位置",
      address: `坐标: (${clickLatLng.lat.toFixed(6)}, ${clickLatLng.lng.toFixed(6)})`,
      lat: clickLatLng.lat,
      lng: clickLatLng.lng,
      isOfficeBuilding: false,
      classrooms: []
    };
    
    // 计算与已选位置之间的距离
    if (selectedLocation.value) {
      const location1 = locations.find(loc => loc.id === selectedLocation.value);
      if (location1) {
        const distance = calculateDistance(
          location1.lat, location1.lng,
          tempLocation.lat, tempLocation.lng
        );
        
        distanceResult.value = {
          from: location1.name,
          to: tempLocation.name,
          distance: distance,
          formattedDistance: (distance / 1000).toFixed(2) + ' 公里'
        };
        
        ElMessage.success(`已计算距离: ${(distance / 1000).toFixed(2)}km`);
        compareMode.value = false; // 退出比较模式
      }
    }
  } catch (error) {
    console.error("比较模式处理点击事件失败:", error);
    ElMessage.error("计算距离失败");
  }
};

// 开始比较模式
const startCompareMode = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择一个位置作为起点');
    return;
  }
  
  compareMode.value = true;
  ElMessage.info('请在地图上点击或选择另一个位置作为终点进行距离计算');
};

// 取消比较
const cancelCompare = () => {
  compareMode.value = false;
  compareLocations.value = null;
  distanceResult.value = null;
  
  // 重置标记样式，恢复正常显示
  updateMarkerStyles();
};

// 年级错峰选项
const gradeTimeTable = ref([
  { id: 1, grade: '2020级', startTime: null, endTime: null, timeDiff: null },
  { id: 2, grade: '2021级', startTime: null, endTime: null, timeDiff: null },
  { id: 3, grade: '2022级', startTime: null, endTime: null, timeDiff: null },
  { id: 4, grade: '2023级', startTime: null, endTime: null, timeDiff: null }
]);

// 更新年级错峰时间差
const updateGradeTimeDiff = (row) => {
  // 检查是否有上下课时间
  if (!row.startTime || !row.endTime) {
    row.timeDiff = null;
    return;
  }
  
  // 计算课程持续时间
  const startMinutes = parseTimeStringToMinutes(row.startTime);
  const endMinutes = parseTimeStringToMinutes(row.endTime);
  
  if (startMinutes >= 0 && endMinutes >= 0) {
    // 确保下课时间晚于上课时间
    if (endMinutes <= startMinutes) {
      ElMessage.warning('下课时间必须晚于上课时间');
      row.endTime = null;
      row.timeDiff = null;
      return;
    }
    
    // 计算与前一个年级的时间差
    const currentIndex = gradeTimeTable.value.findIndex(item => item.id === row.id);
    if (currentIndex > 0) {
      const prevRow = gradeTimeTable.value[currentIndex - 1];
      if (prevRow.startTime) {
        const prevStartMinutes = parseTimeStringToMinutes(prevRow.startTime);
        if (prevStartMinutes >= 0) {
          // 计算时间差（分钟）
          const diff = Math.abs(startMinutes - prevStartMinutes);
          row.timeDiff = diff;
          
          // 如果时间差不在5-10分钟范围内，给出警告
          if (diff < 5 || diff > 10) {
            ElMessage.warning(`${row.grade}与${prevRow.grade}的上课时间差为${diff}分钟，建议保持在5-10分钟之间`);
          }
          return;
        }
      }
    }
    
    row.timeDiff = null;
  } else {
    row.timeDiff = null;
  }
};

// 解析时间字符串为分钟数
const parseTimeStringToMinutes = (timeStr) => {
  if (!timeStr || typeof timeStr !== 'string') return -1;
  
  const parts = timeStr.split(':');
  if (parts.length !== 2) return -1;
  
  const hours = parseInt(parts[0]);
  const minutes = parseInt(parts[1]);
  
  if (isNaN(hours) || isNaN(minutes)) return -1;
  
  return hours * 60 + minutes;
};

// 应用默认错峰时间
const applyDefaultStaggeredTimes = () => {
  // 默认设置：从早上8点开始，每个年级间隔7分钟
  const baseHour = 8;
  const baseMinute = 0;
  const interval = 7; // 间隔7分钟
  
  gradeTimeTable.value.forEach((row, index) => {
    // 计算上课时间
    const startMinutes = baseHour * 60 + baseMinute + index * interval;
    const startHour = Math.floor(startMinutes / 60);
    const startMinute = startMinutes % 60;
    
    // 计算下课时间（上课后45分钟）
    const endMinutes = startMinutes + 45;
    const endHour = Math.floor(endMinutes / 60);
    const endMinute = endMinutes % 60;
    
    // 格式化时间
    row.startTime = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`;
    row.endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;
    
    // 更新时间差
    if (index > 0) {
      row.timeDiff = interval;
    } else {
      row.timeDiff = null;
    }
  });
  
  ElMessage.success('已应用默认错峰时间，每个年级间隔7分钟');
};

// 优化错峰时间
const optimizeStaggeredTimes = () => {
  // 检查是否有设置时间的年级
  const hasSetTimes = gradeTimeTable.value.some(row => row.startTime && row.endTime);
  
  if (!hasSetTimes) {
    ElMessage.warning('请先为至少一个年级设置上下课时间');
    return;
  }
  
  // 获取已设置时间的年级
  const setRows = gradeTimeTable.value.filter(row => row.startTime && row.endTime);
  
  if (setRows.length === gradeTimeTable.value.length) {
    // 所有年级已设置时间，优化间隔
    optimizeIntervals();
  } else {
    // 部分年级已设置时间，为其他年级安排时间
    arrangeRemainingGrades();
  }
};

// 优化年级间的时间间隔
const optimizeIntervals = () => {
  // 按上课时间排序
  const sortedRows = [...gradeTimeTable.value].sort((a, b) => {
    const aMinutes = parseTimeStringToMinutes(a.startTime);
    const bMinutes = parseTimeStringToMinutes(b.startTime);
    return aMinutes - bMinutes;
  });
  
  // 检查时间间隔是否在理想范围内
  let needsOptimization = false;
  
  for (let i = 1; i < sortedRows.length; i++) {
    const prevStartMinutes = parseTimeStringToMinutes(sortedRows[i-1].startTime);
    const currStartMinutes = parseTimeStringToMinutes(sortedRows[i].startTime);
    const diff = currStartMinutes - prevStartMinutes;
    
    if (diff < 5 || diff > 10) {
      needsOptimization = true;
      break;
    }
  }
  
  if (!needsOptimization) {
    ElMessage.info('所有年级的上课时间间隔已在理想范围内（5-10分钟）');
    return;
  }
  
  // 优化：确保每个年级之间间隔7分钟
  const firstStartMinutes = parseTimeStringToMinutes(sortedRows[0].startTime);
  
  sortedRows.forEach((row, index) => {
    if (index === 0) return; // 保持第一个年级的时间不变
    
    // 设置新的上课时间
    const newStartMinutes = firstStartMinutes + index * 7;
    const newStartHour = Math.floor(newStartMinutes / 60);
    const newStartMinute = newStartMinutes % 60;
    
    // 保持课程持续时间不变
    const currStartMinutes = parseTimeStringToMinutes(row.startTime);
    const currEndMinutes = parseTimeStringToMinutes(row.endTime);
    const duration = currEndMinutes - currStartMinutes;
    
    // 计算新的下课时间
    const newEndMinutes = newStartMinutes + duration;
    const newEndHour = Math.floor(newEndMinutes / 60);
    const newEndMinute = newEndMinutes % 60;
    
    // 格式化并更新时间
    const originalGrade = row.grade;
    const targetRow = gradeTimeTable.value.find(r => r.grade === originalGrade);
    
    if (targetRow) {
      targetRow.startTime = `${newStartHour.toString().padStart(2, '0')}:${newStartMinute.toString().padStart(2, '0')}`;
      targetRow.endTime = `${newEndHour.toString().padStart(2, '0')}:${newEndMinute.toString().padStart(2, '0')}`;
      
      // 更新时间差
      if (index > 0) {
        targetRow.timeDiff = 7;
      }
    }
  });
  
  ElMessage.success('已优化所有年级的上课时间间隔为7分钟');
};

// 为未设置时间的年级安排时间
const arrangeRemainingGrades = () => {
  // 获取已设置和未设置时间的年级
  const setRows = gradeTimeTable.value.filter(row => row.startTime && row.endTime);
  const unsetRows = gradeTimeTable.value.filter(row => !row.startTime || !row.endTime);
  
  if (setRows.length === 0 || unsetRows.length === 0) return;
  
  // 按上课时间排序已设置的年级
  setRows.sort((a, b) => {
    const aMinutes = parseTimeStringToMinutes(a.startTime);
    const bMinutes = parseTimeStringToMinutes(b.startTime);
    return aMinutes - bMinutes;
  });
  
  // 找到最早和最晚的上课时间
  const firstRow = setRows[0];
  const lastRow = setRows[setRows.length - 1];
  
  const firstStartMinutes = parseTimeStringToMinutes(firstRow.startTime);
  const lastStartMinutes = parseTimeStringToMinutes(lastRow.startTime);
  
  // 假设课程持续时间为45分钟
  const duration = 45;
  
  // 为每个未设置时间的年级安排时间
  unsetRows.forEach((row, index) => {
    let newStartMinutes;
    
    // 决定在已有年级之前还是之后安排
    if (index < Math.floor(unsetRows.length / 2)) {
      // 在最早的年级之前安排
      newStartMinutes = firstStartMinutes - (index + 1) * 7;
    } else {
      // 在最晚的年级之后安排
      newStartMinutes = lastStartMinutes + (index - Math.floor(unsetRows.length / 2) + 1) * 7;
    }
    
    // 确保时间在合理范围内
    newStartMinutes = Math.max(7 * 60, Math.min(22 * 60 - duration, newStartMinutes));
    
    const newStartHour = Math.floor(newStartMinutes / 60);
    const newStartMinute = newStartMinutes % 60;
    
    // 计算下课时间
    const newEndMinutes = newStartMinutes + duration;
    const newEndHour = Math.floor(newEndMinutes / 60);
    const newEndMinute = newEndMinutes % 60;
    
    // 格式化并更新时间
    row.startTime = `${newStartHour.toString().padStart(2, '0')}:${newStartMinute.toString().padStart(2, '0')}`;
    row.endTime = `${newEndHour.toString().padStart(2, '0')}:${newEndMinute.toString().padStart(2, '0')}`;
  });
  
  // 重新计算所有年级之间的时间差
  gradeTimeTable.value.sort((a, b) => {
    const aMinutes = parseTimeStringToMinutes(a.startTime);
    const bMinutes = parseTimeStringToMinutes(b.startTime);
    return aMinutes - bMinutes;
  });
  
  // 更新时间差
  for (let i = 1; i < gradeTimeTable.value.length; i++) {
    const prevStartMinutes = parseTimeStringToMinutes(gradeTimeTable.value[i-1].startTime);
    const currStartMinutes = parseTimeStringToMinutes(gradeTimeTable.value[i].startTime);
    gradeTimeTable.value[i].timeDiff = currStartMinutes - prevStartMinutes;
  }
  
  ElMessage.success('已为所有年级安排上下课时间');
};

// 获取时间差标签类型
const getTimeDiffTagType = (timeDiff) => {
  if (timeDiff === null) return '';
  const diff = parseInt(timeDiff);
  if (diff >= 5 && diff <= 10) return 'success'; // 理想范围内
  if (diff < 5) return 'danger'; // 太短
  return 'warning'; // 太长
};
</script>

<style scoped>
.map-container {
  position: relative;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.search-box {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 350px;
  z-index: 1000;
  background-color: white;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.distance-tools {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}

.distance-result {
  margin-top: 10px;
}

.distance-info {
  text-align: center;
}

.distance-info h4 {
  margin: 0 0 10px;
  color: #409EFF;
}

.distance-info p {
  margin: 5px 0;
}

.distance-value {
  font-size: 18px;
  font-weight: bold;
  color: #E6A23C;
}

.location-list {
  margin-top: 20px;
  margin-bottom: 20px;
}

.location-card {
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.location-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.location-card.selected {
  border: 2px solid #409EFF;
}

.location-card.office-building {
  border-left: 5px solid #67C23A;
}

.location-info h4 {
  margin: 0 0 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.location-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.classroom-count {
  color: #409EFF !important;
}

.distance-info {
  color: #E6A23C !important;
  font-weight: bold;
}

.scheduling-options {
  margin-top: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 高峰时段标记样式 */
.peak-time {
  color: #E6A23C;
  font-weight: bold;
}

/* 比较模式提示样式 */
.compare-mode-tip {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: rgba(64, 158, 255, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  z-index: 2000;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}
</style> 