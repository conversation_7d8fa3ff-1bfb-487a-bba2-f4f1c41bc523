import request from '@/utils/request'

// 发送消息
export function sendMessage(sessionId, content) {
  return request({
    url: '/system/ai/chat/send',
    method: 'post',
    data: {
      sessionId: sessionId,
      content: content
    }
  })
}

// 创建新会话
export function createSession(data) {
  return request({
    url: '/system/ai/chat/session',
    method: 'post',
    data: data
  })
}

// 获取会话列表
export function listSessions() {
  return request({
    url: '/system/ai/chat/session/list',
    method: 'get'
  })
}

// 获取聊天记录
export function listMessages(sessionId) {
  return request({
    url: `/system/ai/chat/message/list/${sessionId}`,
    method: 'get'
  })
}

// 删除会话
export function deleteSession(sessionId) {
  return request({
    url: `/system/ai/chat/session/${sessionId}`,
    method: 'delete'
  })
}