<template>
  <div class="ai-assistant">
    <el-card class="chat-card">
      <template #header>
        <div class="card-header">
          <div class="assistant-info">
            <el-avatar :size="40" src="/ai-avatar.png" />
            <div class="info">
              <h2>AI 智能助手</h2>
              <span class="status">在线</span>
            </div>
          </div>
          <div class="session-actions" v-if="sessionId">
            <el-tooltip content="新会话">
              <el-button :icon="Plus" circle size="small" @click="createNewSession" />
            </el-tooltip>
            <el-dropdown @command="changeSession">
              <el-button type="primary" size="small">
                {{ currentSessionTitle || '当前会话' }} <el-icon><CaretBottom /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="session in sessionList" :key="session.id" :command="session.id">
                    {{ session.title || formatSessionTitle(session) }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>

      <div class="chat-container" ref="chatContainer">
        <div class="message-list">
          <!-- 系统欢迎消息 -->
          <div class="message system" v-if="messageList.length === 0">
            <div class="message-content">
              <p>您好！我是您的 AI 助手，专注于大学智慧排课系统的相关问题。您可以向我咨询关于课程安排、教室分配、师资调配等方面的问题。</p>
            </div>
          </div>

          <!-- 聊天记录 -->
          <div v-for="msg in messageList" :key="msg.id"
               :class="['message', msg.messageType === '0' ? 'user' : 'ai']">
            <el-avatar :size="36" :src="msg.messageType === '0' ? '/avatar.png' : '/ai-avatar.png'" />
            <div class="message-content">
              <div class="message-info">
                <span class="name">{{ msg.messageType === '0' ? '我' : 'AI 助手' }}</span>
                <span class="time">{{ formatTime(msg.createTime) }}</span>
              </div>
              <div class="text" v-html="formatMessage(msg.content)"></div>
              
              <!-- 显示上传的文件 -->
              <div v-if="msg.fileInfo" class="uploaded-file">
                <el-icon><Document /></el-icon>
                <span>{{ msg.fileInfo.name }}</span>
                <span class="file-size">({{ formatFileSize(msg.fileInfo.size) }})</span>
              </div>
            </div>
          </div>

          <!-- AI 思考状态 -->
          <div class="message ai thinking" v-if="isThinking">
            <el-avatar :size="36" src="/ai-avatar.png" />
            <div class="message-content">
              <div class="message-info">
                <span class="name">AI 助手</span>
                <span class="time">{{ formatTime(new Date()) }}</span>
              </div>
              <div class="thinking-dots">
                <span></span><span></span><span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-toolbar">
          <el-tooltip content="上传文件 (限300KB以内)">
            <el-upload
                class="upload-btn"
                action="#"
                :show-file-list="false"
                :before-upload="handleFileUpload"
                accept=".pdf,.doc,.docx,.txt,.xlsx,.xls"
            >
              <el-button :icon="Document" circle />
            </el-upload>
          </el-tooltip>
          <el-tooltip :content="isRecording ? '点击停止录音' : '按下开始语音输入'">
            <el-button
                :icon="Microphone"
                circle
                :class="{ 'recording': isRecording }"
                @click="toggleRecording"
            />
          </el-tooltip>
        </div>

        <!-- 语音识别状态显示 -->
        <div v-if="recognitionStatus" class="recognition-status-bar">
          <el-icon :class="{'recording-icon': isRecording}"><Microphone /></el-icon>
          <span>{{ recognitionStatus }}</span>
        </div>

        <div v-if="pendingFile" class="pending-file">
          <div class="file-info">
            <el-icon><Document /></el-icon>
            <span>{{ pendingFile.name }}</span>
            <span class="file-size">({{ formatFileSize(pendingFile.size) }})</span>
          </div>
          <el-button type="danger" size="small" @click="removePendingFile" :icon="Delete" circle />
        </div>

        <el-input
            v-model="messageContent"
            type="textarea"
            :rows="3"
            placeholder="请输入您的问题..."
            @keyup.enter.native.exact="handleSendMessage"
        />

        <div class="input-actions">
          <div class="recognition-status" v-if="isRecording">
            <el-icon class="recording-icon"><Microphone /></el-icon>
            <span>正在录音...</span>
          </div>
          <el-button type="primary" :loading="isThinking" @click="handleSendMessage">
            发送
          </el-button>
        </div>

        <!-- 文件上传进度 -->
        <div v-if="uploadProgress > 0" class="upload-progress">
          <el-progress :percentage="uploadProgress" :format="progressFormat" :status="uploadProgress >= 100 ? 'success' : ''" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Microphone, Plus, CaretBottom, Delete } from '@element-plus/icons-vue'
import { marked } from 'marked'
import { parseTime } from '@/utils/ruoyi'
import { createSession, sendMessage, listSessions, listMessages } from '@/api/system/ai'
import useUserStore from '@/store/modules/user'
import { useRouter } from 'vue-router'

const router = useRouter()
const userStore = useUserStore()

// 会话ID
const sessionId = ref('')
const currentSessionTitle = ref('')
const sessionList = ref([])

// 消息列表
const messageList = ref([])
// 输入内容
const messageContent = ref('')
// AI思考状态
const isThinking = ref(false)
// 聊天容器引用
const chatContainer = ref(null)

// 新增的状态
const isRecording = ref(false)
const uploadProgress = ref(0)
const recognition = ref(null)
const recognitionActive = ref(false)
const pendingFile = ref(null) // 待发送的文件
const pendingFileContent = ref('') // 文件内容（Base64）
const recognitionStatus = ref('') // 语音识别状态
const recognitionResult = ref('') // 语音识别结果

// 会话上下文信息，用于存储历史会话的重要信息
const sessionContext = ref({
  historyKeyPoints: [] // 存储历史会话的重要信息点
});

// 格式化消息内容（支持 Markdown）
const formatMessage = (content) => {
  return marked(content)
}

// 格式化时间
const formatTime = (time) => {
  return parseTime(time)
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + 'KB'
  } else {
    return (size / (1024 * 1024)).toFixed(1) + 'MB'
  }
}

// 格式化会话标题，优先使用第一条用户消息作为标题
const formatSessionTitle = (session) => {
  // 如果会话有自定义标题，使用它
  if (session.title) {
    return session.title;
  }
  
  // 如果会话有firstMessage属性，使用它作为标题
  if (session.firstMessage) {
    // 如果消息太长，截断显示
    const maxLength = 20;
    return session.firstMessage.length > maxLength 
      ? session.firstMessage.substring(0, maxLength) + '...' 
      : session.firstMessage;
  }
  
  // 默认使用创建时间
  const date = new Date(session.createTime)
  return `会话 ${parseTime(date, '{y}-{m}-{d} {h}:{i}')}`;
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  const container = chatContainer.value
  if (container) {
    // 确保有足够的延迟，等待DOM更新完成后再滚动
    setTimeout(() => {
      container.scrollTop = container.scrollHeight
    }, 50)
  }
}

// 获取会话列表
const getSessionList = async () => {
  if (!checkLogin()) return
  
  try {
    const res = await listSessions()
    if (res.code === 200 && res.rows) {
      sessionList.value = res.rows
      
      // 如果有会话但没有选择会话，则选择第一个
      if (sessionList.value.length > 0 && !sessionId.value) {
        changeSession(sessionList.value[0].id)
      }
    }
  } catch (error) {
    console.error('获取会话列表失败:', error)
  }
}

// 创建新会话
const createNewSession = async () => {
  try {
    // 提取当前会话的关键信息（如果有）
    if (sessionId.value && messageList.value.length > 0) {
      // 获取当前会话中AI的关键回复
      const aiMessages = messageList.value.filter(msg => msg.messageType === '1');
      
      // 如果有AI消息，存储最后三条作为上下文
      if (aiMessages.length > 0) {
        // 最多保存最后3条AI消息
        const lastMessages = aiMessages.slice(-3);
        
        // 将这些消息添加到历史上下文中
        lastMessages.forEach(msg => {
          sessionContext.value.historyKeyPoints.push({
            time: msg.createTime,
            content: msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : '')
          });
        });
        
        // 限制上下文长度，最多保留最近的10条记录
        if (sessionContext.value.historyKeyPoints.length > 10) {
          sessionContext.value.historyKeyPoints = sessionContext.value.historyKeyPoints.slice(-10);
        }
      }
    }
    
    // 创建带有历史上下文的新会话
    let createParams = {};
    
    // 如果有历史上下文，将其添加到创建参数中
    if (sessionContext.value.historyKeyPoints.length > 0) {
      createParams.context = JSON.stringify(sessionContext.value);
    }
    
    const res = await createSession(createParams);
    
    if (res.code === 200) {
      ElMessage.success('创建会话成功')
      await getSessionList()
      changeSession(res.data.id)
    }
  } catch (error) {
    ElMessage.error('创建会话失败')
    console.error('创建会话失败:', error)
  }
}

// 切换会话
const changeSession = async (sid) => {
  sessionId.value = sid
  messageList.value = []
  
  // 查找当前会话标题
  const session = sessionList.value.find(s => s.id === sid)
  if (session) {
    currentSessionTitle.value = session.title || formatSessionTitle(session)
  }
  
  // 获取聊天记录
  try {
    const res = await listMessages(sid)
    if (res.code === 200 && res.rows) {
      // 解析消息中的文件信息
      messageList.value = res.rows.map(msg => {
        if (msg.content && msg.content.startsWith('[文件上传]')) {
          const fileName = msg.content.replace('[文件上传]', '').trim();
          msg.fileInfo = {
            name: fileName,
            size: 0
          };
        }
        return msg;
      });
      await scrollToBottom()
    }
  } catch (error) {
    console.error('获取聊天记录失败:', error)
  }
}

// 格式化上传进度
const progressFormat = (percentage) => {
  return percentage === 100 ? '上传完成' : `上传中 ${percentage}%`
}

// 移除待发送的文件
const removePendingFile = () => {
  pendingFile.value = null
  pendingFileContent.value = ''
  recognitionStatus.value = ''
}

// 处理文件上传
const handleFileUpload = async (file) => {
  // 文件大小限制检查 (300KB)
  const maxSize = 300 * 1024; // 300KB
  if (file.size > maxSize) {
    ElMessage.error(`文件大小超过限制，最大允许上传 300KB`)
    return false
  }
  
  try {
    // 显示上传进度
    uploadProgress.value = 0
    const interval = setInterval(() => {
      uploadProgress.value += 10
      if (uploadProgress.value >= 100) {
        clearInterval(interval)
        setTimeout(() => {
          uploadProgress.value = 0
        }, 2000)
      }
    }, 100)
    
    // 读取文件内容为Base64
    const reader = new FileReader()
    reader.onload = (e) => {
      // 获取Base64编码的文件内容
      pendingFileContent.value = e.target.result
      
      // 如果是文本文件，尝试提取文本内容
      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
        // 再次读取为文本
        const textReader = new FileReader()
        textReader.onload = (textEvent) => {
          const textContent = textEvent.target.result
          // 限制预览长度
          const previewLength = 200
          const preview = textContent.length > previewLength 
            ? textContent.substring(0, previewLength) + '...' 
            : textContent
            
          // 在状态中显示文件预览
          recognitionStatus.value = `文件内容预览: ${preview}`
          setTimeout(() => {
            if (recognitionStatus.value.startsWith('文件内容预览:')) {
              recognitionStatus.value = ''
            }
          }, 5000)
        }
        textReader.readAsText(file)
      }
      
      ElMessage.success('文件已准备好，将随您的下一条消息一起发送')
    }
    
    reader.onerror = () => {
      ElMessage.error('读取文件失败，请重试')
      clearInterval(interval)
      uploadProgress.value = 0
    }
    
    // 开始读取文件
    reader.readAsDataURL(file)
    
    // 存储文件以便稍后发送
    pendingFile.value = file
  } catch (error) {
    console.error('文件上传处理错误:', error)
    ElMessage.error('文件处理失败: ' + error.message)
    uploadProgress.value = 0
  }
  
  return false
}

// 发送消息
const handleSendMessage = async () => {
  if (!messageContent.value.trim() && !pendingFile.value) {
    ElMessage.warning('请输入内容或上传文件')
    return
  }
  
  if (!checkLogin()) return
  
  try {
    // 如果没有会话ID，先创建会话
    if (!sessionId.value) {
      try {
        const res = await createSession()
        if (res.code === 200) {
          sessionId.value = res.data.id
          await getSessionList()
        } else {
          ElMessage.error(res.msg || '创建会话失败')
          return
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          ElMessage.error('您未登录或登录已过期，请先登录')
          checkLogin()
          return
        }
        ElMessage.error('创建会话失败，请检查网络连接')
        console.error('创建会话失败:', error)
        return
      }
    }

    let userMessage = messageContent.value
    let hasFile = false
    let fileInfo = null

    // 如果有待发送的文件，添加到消息中
    if (pendingFile.value) {
      hasFile = true
      fileInfo = {
        name: pendingFile.value.name,
        size: pendingFile.value.size,
        type: pendingFile.value.type
      }

      // 在消息前面添加文件信息标记和文件内容（Base64）
      userMessage = `[文件上传] ${pendingFile.value.name} [FILE_CONTENT:${pendingFileContent.value}]\n\n${userMessage}`
      
      // 启动上传进度显示
      uploadProgress.value = 0
      const interval = setInterval(() => {
        uploadProgress.value += 10
        if (uploadProgress.value >= 100) {
          clearInterval(interval)
          setTimeout(() => {
            uploadProgress.value = 0
          }, 2000)
        }
      }, 200)
    }

    // 添加用户消息到本地显示（立即反馈）
    const userMsg = {
      id: Date.now(),
      messageType: '0',
      userName: '我',
      content: hasFile 
        ? `[文件上传] ${pendingFile.value.name}\n\n${messageContent.value}` 
        : userMessage,
      createTime: new Date().toLocaleString()
    };
    
    // 如果有文件，添加文件信息
    if (hasFile) {
      userMsg.fileInfo = fileInfo
    }
    
    messageList.value.push(userMsg);
    
    // 如果是该会话的第一条消息，更新会话标题
    const isFirstMessage = messageList.value.filter(msg => msg.messageType === '0').length === 1;
    if (isFirstMessage) {
      // 获取纯文本内容（不包含[文件上传]前缀）
      let titleContent = userMessage;
      if (titleContent.startsWith('[文件上传]')) {
        const newlineIndex = titleContent.indexOf('\n\n');
        if (newlineIndex !== -1) {
          titleContent = titleContent.substring(newlineIndex + 2);
        }
      }
      
      // 更新当前会话标题
      if (titleContent.trim()) {
        // 更新本地会话标题
        const sessionIndex = sessionList.value.findIndex(s => s.id === sessionId.value);
        if (sessionIndex !== -1) {
          sessionList.value[sessionIndex].firstMessage = titleContent.trim();
          currentSessionTitle.value = formatSessionTitle(sessionList.value[sessionIndex]);
        }
      }
    }

    // 清空输入框和待发送文件
    messageContent.value = ''
    pendingFile.value = null
    pendingFileContent.value = ''
    await scrollToBottom()
    
    // AI 响应
    isThinking.value = true
    try {
      // 调用真实的AI接口
      const response = await sendMessage(sessionId.value, userMessage)
      
      if (response.code === 200) {
        // 添加AI回复
        messageList.value.push({
          id: Date.now(),
          messageType: '1',
          userName: 'AI 助手',
          content: response.data,
          createTime: new Date().toLocaleString()
        })
        await scrollToBottom()
      } else {
        ElMessage.error(response.msg || 'AI 响应失败')
      }
    } catch (error) {
      if (error.response && error.response.status === 401) {
        ElMessage.error('您未登录或登录已过期，请先登录')
        checkLogin()
      } else if (error.message && error.message.includes('timeout')) {
        ElMessage.error('AI 响应超时，请稍后再试或尝试简化您的问题')
        // 添加AI回复（超时）
        messageList.value.push({
          id: Date.now(),
          messageType: '1',
          userName: 'AI 助手',
          content: '非常抱歉，我的响应时间超出了预期。请尝试简化您的问题，或稍后再试。',
          createTime: new Date().toLocaleString()
        })
        await scrollToBottom()
      } else {
        ElMessage.error('AI 响应出错，请稍后再试')
        console.error('AI 响应出错:', error)
      }
    } finally {
      isThinking.value = false
    }
  } catch (error) {
    ElMessage.error('操作失败，请稍后再试')
    console.error('操作失败:', error)
    isThinking.value = false
  }
}

// 初始化语音识别
const initSpeechRecognition = () => {
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  if (SpeechRecognition) {
    recognition.value = new SpeechRecognition()
    recognition.value.lang = 'zh-CN'
    recognition.value.continuous = true
    recognition.value.interimResults = true

    recognition.value.onresult = (event) => {
      const result = event.results[event.results.length - 1]
      if (result.isFinal) {
        recognitionResult.value = result[0].transcript
        // 将识别结果添加到输入框
        messageContent.value += recognitionResult.value
        recognitionStatus.value = '已识别: ' + recognitionResult.value
      } else {
        // 显示中间结果
        recognitionStatus.value = '正在识别: ' + result[0].transcript
      }
    }

    recognition.value.onerror = (event) => {
      console.error('语音识别错误:', event.error)
      
      // 根据错误类型提供更具体的错误信息
      let errorMessage = '语音识别出错'
      if (event.error === 'network') {
        errorMessage = '网络连接问题，请检查您的网络连接'
      } else if (event.error === 'not-allowed') {
        errorMessage = '麦克风访问被拒绝，请允许浏览器访问您的麦克风'
      } else if (event.error === 'aborted') {
        errorMessage = '语音识别被中断'
      } else if (event.error === 'audio-capture') {
        errorMessage = '无法捕获音频，请检查您的麦克风是否正常工作'
      } else if (event.error === 'no-speech') {
        errorMessage = '未检测到语音，请靠近麦克风并尝试清晰地说话'
      } else if (event.error === 'service-not-allowed') {
        errorMessage = '浏览器不允许使用语音识别服务'
      }
      
      recognitionStatus.value = errorMessage
      ElMessage.error(errorMessage)
      
      // 即使出错也尝试保持当前识别的结果
      if (recognitionResult.value) {
        messageContent.value += recognitionResult.value
        recognitionResult.value = ''
      }
      
      stopRecording()
    }
    
    recognition.value.onend = () => {
      // 如果需要继续录音，则重新开始
      if (recognitionActive.value) {
        // 尝试重新启动，但如果有错误则优雅地关闭
        try {
          recognition.value.start()
        } catch (error) {
          console.error('无法重新启动语音识别:', error)
          recognitionActive.value = false
          isRecording.value = false
          recognitionStatus.value = '录音已停止'
        }
      } else {
        // 正常结束录音
        isRecording.value = false
        
        // 确保最终结果被添加到输入框
        if (recognitionResult.value && !messageContent.value.endsWith(recognitionResult.value)) {
          messageContent.value += recognitionResult.value
        }
        
        // 短暂显示结束提示后清空状态
        recognitionStatus.value = '语音识别已完成'
        setTimeout(() => {
          if (recognitionStatus.value === '语音识别已完成') {
            recognitionStatus.value = ''
          }
        }, 3000)
        
        recognitionResult.value = ''
      }
    }
  }
}

// 切换录音状态
const toggleRecording = () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

// 开始录音
const startRecording = () => {
  if (!recognition.value) {
    ElMessage.warning('您的浏览器不支持语音识别')
    return
  }
  
  // 重置状态
  recognitionResult.value = ''
  recognitionStatus.value = '正在录音，请说话...'
  
  try {
    isRecording.value = true
    recognitionActive.value = true
    recognition.value.start()
    ElMessage.success('语音输入已开始，请说话...')
  } catch (error) {
    console.error('语音识别启动失败:', error)
    ElMessage.error('无法启动语音识别: ' + error.message)
    isRecording.value = false
    recognitionActive.value = false
    recognitionStatus.value = '语音识别启动失败'
  }
}

// 停止录音
const stopRecording = () => {
  if (recognition.value) {
    recognitionActive.value = false
    recognitionStatus.value = '正在处理语音...'
    
    try {
      recognition.value.stop()
      ElMessage.info('语音输入已结束')
    } catch (error) {
      console.error('停止语音识别时出错:', error)
      isRecording.value = false
      recognitionStatus.value = '语音识别已停止'
    }
  }
}

// 添加检查登录状态的函数
const checkLogin = () => {
  if (!userStore.token) {
    ElMessageBox.confirm(
      '您需要登录才能使用AI聊天功能，是否立即登录？',
      '未登录提示',
      {
        confirmButtonText: '前往登录',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        router.push('/login?redirect=' + encodeURIComponent(router.currentRoute.value.fullPath))
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '您可以继续浏览，但无法使用AI聊天功能',
        })
      })
    return false
  }
  return true
}

// 监听消息列表变化，始终保持滚动到底部
watch(messageList, () => {
  scrollToBottom()
}, { deep: true })

onMounted(() => {
  initSpeechRecognition()
  // 检查登录状态并获取会话列表
  if (checkLogin()) {
    getSessionList()
  }
})
</script>

<style lang="scss" scoped>
.ai-assistant {
  padding: 20px;
  height: calc(100vh - 120px);
  background: linear-gradient(135deg, #1a1f35 0%, #0f172a 100%);
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;

  .chat-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(99, 179, 237, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.35);
    border-radius: 12px;
    overflow: hidden;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 0;
      overflow: hidden; /* 防止溢出 */
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg,
          transparent 0%,
          rgba(99, 179, 237, 0.5) 50%,
          transparent 100%
      );
    }
  }

  .card-header {
    background: rgba(15, 23, 42, 0.7);
    border-bottom: 1px solid rgba(99, 179, 237, 0.15);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;

    .assistant-info {
      display: flex;
      align-items: center;
      gap: 15px;

      .info {
        h2 {
          margin: 0;
          font-size: 18px;
          color: #e2e8f0;
          text-shadow: 0 0 10px rgba(99, 179, 237, 0.3);
          letter-spacing: 1px;
        }

        .status {
          color: #4ade80;
          font-size: 14px;
          position: relative;
          padding-left: 12px;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 8px;
            height: 8px;
            background: currentColor;
            border-radius: 50%;
            transform: translateY(-50%);
            box-shadow: 0 0 8px currentColor;
            animation: pulse 2s infinite;
          }
        }
      }
    }
    
    .session-actions {
      display: flex;
      gap: 10px;
      align-items: center;
      
      :deep(.el-button) {
        transition: all 0.3s ease;
        border: 1px solid rgba(99, 179, 237, 0.3);
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  .chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    background: rgba(15, 23, 42, 0.4);
    scroll-behavior: smooth;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 关键: 确保滚动正常工作 */

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(99, 179, 237, 0.3);
      border-radius: 3px;
    }

    .message-list {
      display: flex;
      flex-direction: column;
      min-height: 0; /* 确保容器能正确滚动 */
    }

    .message {
      display: flex;
      gap: 14px;
      max-width: 70%;
      margin-bottom: 20px;
      position: relative;
      animation: fadeIn 0.3s ease-out;

      .el-avatar {
        flex-shrink: 0;
        border: 2px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        
        &::after {
          content: '';
          position: absolute;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          bottom: 0;
          right: 0;
          background: #4ade80;
          box-shadow: 0 0 5px rgba(74, 222, 128, 0.5);
        }
      }

      &.user {
        flex-direction: row-reverse;
        margin-left: auto;

        .message-content {
          background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
          color: white;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
          border-radius: 16px 4px 16px 16px;
        }
        
        .el-avatar::after {
          left: 0;
          right: auto;
          background: #60a5fa;
          box-shadow: 0 0 5px rgba(96, 165, 250, 0.5);
        }
      }

      &.ai .message-content {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(99, 179, 237, 0.15);
        color: #e2e8f0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-radius: 4px 16px 16px 16px;
      }

      &.system .message-content {
        background: rgba(99, 179, 237, 0.08);
        color: #94a3b8;
        border: 1px solid rgba(99, 179, 237, 0.15);
        border-radius: 14px;
      }

      .message-content {
        padding: 12px 16px;
        position: relative;
        transition: all 0.3s ease;
        flex: 1;

        &:hover {
          transform: translateY(-2px);
        }

        .message-info {
          margin-bottom: 6px;
          font-size: 13px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .name {
            font-weight: 600;
            color: #94a3b8;
          }

          .time {
            margin-left: 8px;
            color: #64748b;
            font-size: 12px;
          }
        }

        .text {
          font-size: 14px;
          line-height: 1.5;
          white-space: pre-wrap;
          
          :deep(pre) {
            background: rgba(0, 0, 0, 0.25);
            padding: 12px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
          }
          
          :deep(code) {
            font-family: 'Consolas', 'Monaco', monospace;
            background: rgba(0, 0, 0, 0.25);
            padding: 2px 5px;
            border-radius: 4px;
            font-size: 14px;
          }
          
          :deep(table) {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            
            th, td {
              border: 1px solid rgba(255, 255, 255, 0.15);
              padding: 10px;
            }
            
            th {
              background: rgba(0, 0, 0, 0.3);
              font-weight: 600;
            }
            
            tr:nth-child(even) td {
              background: rgba(0, 0, 0, 0.1);
            }
          }
          
          :deep(a) {
            color: #60a5fa;
            text-decoration: none;
            border-bottom: 1px dotted;
            
            &:hover {
              border-bottom: 1px solid;
            }
          }
          
          :deep(ul), :deep(ol) {
            padding-left: 24px;
            margin: 10px 0;
          }
          
          :deep(blockquote) {
            border-left: 4px solid rgba(99, 179, 237, 0.5);
            padding: 2px 16px;
            margin: 10px 0;
            background: rgba(99, 179, 237, 0.05);
          }
        }
        
        .uploaded-file {
          display: flex;
          align-items: center;
          margin-top: 8px;
          padding: 8px 10px;
          background: rgba(0, 0, 0, 0.2);
          border-radius: 6px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          
          .el-icon {
            margin-right: 8px;
            color: #60a5fa;
          }
          
          .file-size {
            color: #64748b;
            margin-left: 6px;
            font-size: 12px;
          }
        }
      }
    }

    .thinking {
      .thinking-dots {
        display: flex;
        gap: 6px;
        padding: 10px 4px;

        span {
          width: 10px;
          height: 10px;
          background: #3b82f6;
          border-radius: 50%;
          animation: thinking 1.4s infinite ease-in-out;
          box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);

          &:nth-child(1) { animation-delay: 0s; }
          &:nth-child(2) { animation-delay: 0.2s; }
          &:nth-child(3) { animation-delay: 0.4s; }
        }
      }
    }
  }

  .chat-input {
    padding: 16px 20px;
    background: rgba(15, 23, 42, 0.7);
    border-top: 1px solid rgba(99, 179, 237, 0.15);
    z-index: 10; /* 确保输入框始终在顶部 */

    .input-toolbar {
      display: flex;
      gap: 10px;
      margin-bottom: 12px;
      
      .el-button {
        transition: all 0.3s ease;
        
        &.recording {
          background: #ef4444;
          color: white;
          border-color: #ef4444;
          box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
          animation: pulse 1.5s infinite;
        }
      }
    }

    .el-input {
      :deep(.el-textarea__inner) {
        background: rgba(255, 255, 255, 0.04);
        border: 1px solid rgba(99, 179, 237, 0.2);
        color: #e2e8f0;
        border-radius: 10px;
        transition: all 0.3s ease;
        resize: none;
        font-size: 14px;
        padding: 10px 14px;

        &:focus {
          border-color: rgba(99, 179, 237, 0.5);
          box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.2);
          background: rgba(255, 255, 255, 0.06);
        }

        &::placeholder {
          color: #64748b;
        }
      }
    }

    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 14px;
      
      .recognition-status {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #ef4444;
        animation: pulse 1.5s infinite;
        
        .recording-icon {
          animation: pulse 1.5s infinite;
        }
      }

      .el-button {
        background: rgba(99, 179, 237, 0.1);
        border: 1px solid rgba(99, 179, 237, 0.2);
        color: #e2e8f0;
        transition: all 0.3s ease;
        padding: 12px 24px;
        font-size: 15px;

        &:hover {
          background: rgba(99, 179, 237, 0.2);
          transform: translateY(-2px);
        }

        &.is-loading {
          background: rgba(99, 179, 237, 0.05);
        }

        &[type="primary"] {
          background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
          border: none;

          &:hover {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
          }
        }
      }
    }
    
    .upload-progress {
      margin-top: 14px;
      
      :deep(.el-progress-bar__outer) {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
      }
      
      :deep(.el-progress-bar__inner) {
        border-radius: 8px;
        background: linear-gradient(90deg, #3b82f6, #60a5fa);
      }
      
      :deep(.el-progress__text) {
        color: #94a3b8;
      }
    }
  }
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

@keyframes thinking {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recognition-status-bar {
  margin: 10px 0;
  padding: 8px 12px;
  background: rgba(59, 130, 246, 0.08);
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #e2e8f0;
  border: 1px solid rgba(59, 130, 246, 0.2);
  
  .recording-icon {
    color: #ef4444;
    animation: pulse 1.5s infinite;
  }
  
  span {
    font-size: 13px;
    flex: 1;
  }
}

.pending-file {
  margin: 10px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(59, 130, 246, 0.1);
  border: 1px dashed rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 8px 12px;
  
  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e2e8f0;
    
    .el-icon {
      color: #60a5fa;
    }
    
    .file-size {
      color: #64748b;
      font-size: 12px;
    }
  }
}
</style>