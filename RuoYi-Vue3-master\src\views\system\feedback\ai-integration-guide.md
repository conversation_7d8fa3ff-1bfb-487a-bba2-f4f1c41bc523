# AI 集成指南

要将前端的AI聊天窗口与后端的DeepSeek AI服务集成，请按照以下步骤修改 `detail.vue` 文件：

## 1. 添加会话管理相关状态

在 `<script setup>` 部分开头附近添加：

```js
// 会话ID
const sessionId = ref('')
const currentSessionTitle = ref('')
const sessionList = ref([])

// 引入需要的组件
import { Plus, CaretBottom } from '@element-plus/icons-vue'
import axios from 'axios'
import { parseTime } from '@/utils/ruoyi'
```

## 2. 修改头部增加会话选择功能

在 `<template>` 中找到 `.card-header` 部分，替换为：

```html
<div class="card-header">
  <div class="assistant-info">
    <el-avatar :size="40" src="/ai-avatar.png" />
    <div class="info">
      <h2>AI 智能助手</h2>
      <span class="status">在线</span>
    </div>
  </div>
  <div class="session-actions" v-if="sessionId">
    <el-tooltip content="新会话">
      <el-button :icon="Plus" circle size="small" @click="createNewSession" />
    </el-tooltip>
    <el-dropdown @command="changeSession">
      <el-button type="primary" size="small">
        {{ currentSessionTitle || '当前会话' }} <el-icon><CaretBottom /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="session in sessionList" :key="session.id" :command="session.id">
            {{ session.title }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</div>
```

## 3. 修改消息显示方式

修改显示消息的部分，确保兼容后端返回的消息格式：

```html
<!-- 聊天记录 -->
<div v-for="msg in messageList" :key="msg.id"
     :class="['message', msg.messageType === '0' ? 'user' : 'ai']">
  <el-avatar :size="36" :src="msg.messageType === '0' ? '/user-avatar.png' : '/ai-avatar.png'" />
  <div class="message-content">
    <div class="message-info">
      <span class="name">{{ msg.messageType === '0' ? '我' : 'AI 助手' }}</span>
      <span class="time">{{ formatTime(msg.createTime) }}</span>
    </div>
    <div class="text" v-html="formatMessage(msg.content)"></div>
  </div>
</div>
```

## 4. 添加会话管理函数

在 `<script setup>` 部分中添加以下函数：

```js
// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  
  // 如果是字符串日期，直接返回
  if (typeof time === 'string') {
    return time;
  }
  
  // 否则尝试格式化日期
  try {
    return parseTime(time);
  } catch (error) {
    return new Date(time).toLocaleString();
  }
}

// 获取会话列表
const getSessionList = async () => {
  try {
    const res = await axios.get('/dev-api/system/ai/chat/session/list')
    if (res.data.code === 200 && res.data.rows) {
      sessionList.value = res.data.rows
      
      // 如果有会话但没有选择会话，则选择第一个
      if (sessionList.value.length > 0 && !sessionId.value) {
        changeSession(sessionList.value[0].id)
      } else if (sessionList.value.length === 0) {
        // 如果没有会话，创建一个新会话
        createNewSession()
      }
    }
  } catch (error) {
    console.error('获取会话列表失败:', error)
  }
}

// 创建新会话
const createNewSession = async () => {
  try {
    const res = await axios.post('/dev-api/system/ai/chat/session')
    if (res.data.code === 200) {
      ElMessage.success('创建会话成功')
      await getSessionList()
      changeSession(res.data.data.id)
    }
  } catch (error) {
    ElMessage.error('创建会话失败')
    console.error('创建会话失败:', error)
  }
}

// 切换会话
const changeSession = async (sid) => {
  sessionId.value = sid
  messageList.value = []
  
  // 查找当前会话标题
  const session = sessionList.value.find(s => s.id === sid)
  if (session) {
    currentSessionTitle.value = session.title
  }
  
  // 获取聊天记录
  try {
    const res = await axios.get(`/dev-api/system/ai/chat/message/list/${sid}`)
    if (res.data.code === 200 && res.data.rows) {
      messageList.value = res.data.rows
      await scrollToBottom()
    }
  } catch (error) {
    console.error('获取聊天记录失败:', error)
  }
}
```

## 5. 替换模拟AI响应函数为真实API调用

删除原有的 `simulateAIResponse` 函数，并修改 `handleSendMessage` 函数:

```js
// 发送消息
const handleSendMessage = async () => {
  if (!messageContent.value.trim()) {
    ElMessage.warning('请输入内容')
    return
  }
  
  // 如果没有会话ID，先创建会话
  if (!sessionId.value) {
    try {
      const res = await axios.post('/dev-api/system/ai/chat/session')
      if (res.data.code === 200) {
        sessionId.value = res.data.data.id
        await getSessionList()
      } else {
        ElMessage.error('创建会话失败')
        return
      }
    } catch (error) {
      ElMessage.error('创建会话失败')
      console.error('创建会话失败:', error)
      return
    }
  }

  const userMessage = messageContent.value

  // 清空输入框
  messageContent.value = ''
  
  // AI 响应
  isThinking.value = true
  try {
    // 调用真实的AI接口
    const response = await axios.post('/dev-api/system/ai/chat/send', {
      sessionId: sessionId.value,
      content: userMessage
    })
    
    if (response.data.code === 200) {
      // 成功后刷新消息列表
      await changeSession(sessionId.value)
    } else {
      ElMessage.error('AI 响应失败')
    }
  } catch (error) {
    ElMessage.error('AI 响应出错')
    console.error('AI 响应出错:', error)
  } finally {
    isThinking.value = false
  }
}
```

## 6. 修改 `onMounted` 钩子添加获取会话列表的调用

```js
onMounted(() => {
  initSpeechRecognition()
  getSessionList() // 获取会话列表
})
```

## 7. 修改 CSS 样式

添加以下样式到现有的 `<style>` 部分:

```scss
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  
  .session-actions {
    display: flex;
    gap: 10px;
    align-items: center;
  }
}
```

另外，在 `.message` 样式中增加 margin-bottom 属性:

```scss
.message {
  margin-bottom: 20px;
}
```

## 8. 修改文件上传函数

修改 `handleFileUpload` 函数:

```js
// 处理文件上传
const handleFileUpload = async (file) => {
  try {
    uploadProgress.value = 0
    // 模拟上传进度
    const interval = setInterval(() => {
      uploadProgress.value += 10
      if (uploadProgress.value >= 100) {
        clearInterval(interval)
        setTimeout(() => {
          uploadProgress.value = 0
        }, 1000)
      }
    }, 200)

    if (!sessionId.value) {
      await createNewSession();
    }
    
    // 通知AI有文件上传
    try {
      const fileContent = `[文件上传] ${file.name} (文件类型: ${file.type}, 大小: ${(file.size / 1024).toFixed(2)}KB)`;
      
      await axios.post('/dev-api/system/ai/chat/send', {
        sessionId: sessionId.value,
        content: fileContent
      });
      
      // 刷新消息列表
      await changeSession(sessionId.value);
      ElMessage.success('文件上传成功');
    } catch (error) {
      console.error('发送文件信息失败:', error);
      ElMessage.error('文件处理失败');
    }

    return false
  } catch (error) {
    ElMessage.error('文件上传失败')
    return false
  }
}
```

通过这些修改，您的前端聊天界面将能够与后端的DeepSeek AI进行交互，实现真实的AI对话功能。