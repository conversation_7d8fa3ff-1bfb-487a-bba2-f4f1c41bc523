<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="周几" prop="weekday">
        <el-select v-model="queryParams.weekday" placeholder="请选择周几" clearable>
          <el-option
            v-for="dict in workday"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="起始节次" prop="sectionStart">
        <el-select v-model="queryParams.sectionStart" placeholder="请选择起始节次" clearable>
          <el-option
            v-for="dict in starting_segment"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="结束节次" prop="sectionEnd">
        <el-select v-model="queryParams.sectionEnd" placeholder="请选择结束节次" clearable>
          <el-option
            v-for="dict in end_segment"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学期ID" prop="semesterId">
        <el-select v-model="queryParams.semesterId" placeholder="请选择学期ID" clearable>
          <el-option
            v-for="dict in semester"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker clearable
          v-model="queryParams.startTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker clearable
          v-model="queryParams.endTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:slot:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:slot:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:slot:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:slot:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="slotList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="时间槽ID" align="center" prop="id" />
      
      <el-table-column label="学期ID" align="center" prop="semesterId">
        <template #default="scope">
          <dict-tag :options="semester" :value="scope.row.semesterId"/>
        </template>
      </el-table-column>
      
      
      <el-table-column label="周几" align="center" prop="weekday">
        <template #default="scope">
          <dict-tag :options="workday" :value="scope.row.weekday"/>
        </template>
      </el-table-column>
      <el-table-column label="起始节次" align="center" prop="sectionStart">
        <template #default="scope">
          <dict-tag :options="starting_segment" :value="scope.row.sectionStart"/>
        </template>
      </el-table-column>
      <el-table-column label="结束节次" align="center" prop="sectionEnd">
        <template #default="scope">
          <dict-tag :options="end_segment" :value="scope.row.sectionEnd"/>
        </template>
      </el-table-column>
      
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:slot:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:slot:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改排课时间槽对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="slotRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="周几" prop="weekday">
          <el-select v-model="form.weekday" placeholder="请选择周几">
            <el-option
              v-for="dict in workday"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="起始节次" prop="sectionStart">
          <el-select v-model="form.sectionStart" placeholder="请选择起始节次">
            <el-option
              v-for="dict in starting_segment"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结束节次" prop="sectionEnd">
          <el-select v-model="form.sectionEnd" placeholder="请选择结束节次">
            <el-option
              v-for="dict in end_segment"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学期ID" prop="semesterId">
          <el-select v-model="form.semesterId" placeholder="请选择学期ID">
            <el-option
              v-for="dict in semester"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable
            v-model="form.startTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable
            v-model="form.endTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Slot">
import { listSlot, getSlot, delSlot, addSlot, updateSlot } from "@/api/system/slot";

const { proxy } = getCurrentInstance();
const { workday, starting_segment, semester, end_segment } = proxy.useDict('workday', 'starting_segment', 'semester', 'end_segment');

const slotList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    weekday: null,
    sectionStart: null,
    sectionEnd: null,
    semesterId: null,
    startTime: null,
    endTime: null
  },
  rules: {
    weekday: [
      { required: true, message: "周几不能为空", trigger: "change" }
    ],
    sectionStart: [
      { required: true, message: "起始节次不能为空", trigger: "change" }
    ],
    sectionEnd: [
      { required: true, message: "结束节次不能为空", trigger: "change" }
    ],
    semesterId: [
      { required: true, message: "学期ID不能为空", trigger: "change" }
    ],
    startTime: [
      { required: true, message: "开始时间不能为空", trigger: "blur" }
    ],
    endTime: [
      { required: true, message: "结束时间不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询排课时间槽列表 */
function getList() {
  loading.value = true;
  listSlot(queryParams.value).then(response => {
    slotList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    weekday: null,
    sectionStart: null,
    sectionEnd: null,
    semesterId: null,
    startTime: null,
    endTime: null
  };
  proxy.resetForm("slotRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加排课时间槽";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getSlot(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改排课时间槽";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["slotRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateSlot(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSlot(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除排课时间槽编号为"' + _ids + '"的数据项？').then(function() {
    return delSlot(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/slot/export', {
    ...queryParams.value
  }, `slot_${new Date().getTime()}.xlsx`)
}

getList();
</script>
