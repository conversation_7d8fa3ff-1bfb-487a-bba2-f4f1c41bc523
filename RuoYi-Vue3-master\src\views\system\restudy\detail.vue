<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">重修课程详情</span>
          <div class="header-buttons">
            <el-button 
              type="info" 
              plain 
              @click="goToList">
              <el-icon><List /></el-icon> 课程列表
            </el-button>
            <el-button 
              type="success" 
              plain 
              @click="goToSchedules">
              <el-icon><Calendar /></el-icon> 课程排课
            </el-button>
            <el-button type="primary" @click="goBack">
              <el-icon><Back /></el-icon> 返回
            </el-button>
            <el-button 
              type="success" 
              @click="goToSchedule" 
              v-if="courseInfo.status !== 'arranged'"
              v-hasPermi="['system:restudy:schedule']">
              <el-icon><Calendar /></el-icon> 排课
            </el-button>
          </div>
        </div>
      </template>

      <el-descriptions
        v-loading="loading"
        title="课程基本信息"
        :column="3"
        border>
        <el-descriptions-item label="课程ID">{{ courseInfo.id }}</el-descriptions-item>
        <el-descriptions-item label="原课程ID">{{ courseInfo.courseId }}</el-descriptions-item>
        <el-descriptions-item label="课程名称">{{ courseInfo.courseName }}</el-descriptions-item>
        <el-descriptions-item label="课程代码">{{ courseInfo.courseCode }}</el-descriptions-item>
        <el-descriptions-item label="教师姓名">{{ courseInfo.teacherName }}</el-descriptions-item>
        <el-descriptions-item label="学分">{{ courseInfo.credits }}</el-descriptions-item>
        <el-descriptions-item label="课时">{{ courseInfo.hours }}</el-descriptions-item>
        <el-descriptions-item label="所属学期">{{ courseInfo.semesterId }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(courseInfo.status)">
            {{ getStatusText(courseInfo.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最少学生数">{{ courseInfo.minStudents }}</el-descriptions-item>
        <el-descriptions-item label="最大学生数">{{ courseInfo.maxStudents }}</el-descriptions-item>
        <el-descriptions-item label="当前学生数">
          <el-tag :type="courseInfo.currentStudents >= courseInfo.minStudents ? 'success' : 'danger'">
            {{ courseInfo.currentStudents }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ courseInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ courseInfo.updateTime }}</el-descriptions-item>
      </el-descriptions>

      <div v-if="courseInfo.status === 'arranged'" class="mt20">
        <el-descriptions
          title="排课信息"
          :column="2"
          border>
          <el-descriptions-item label="教室名称">{{ courseInfo.classroomName || '尚未分配' }}</el-descriptions-item>
          <el-descriptions-item label="时间槽信息">{{ courseInfo.timeslotInfo || '尚未安排' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="mt20">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="已选学生" name="students">
            <!-- 学生列表 -->
            <el-table v-loading="studentsLoading" :data="studentList" border stripe>
              <el-table-column type="index" width="55" align="center" />
              <el-table-column label="学生ID" align="center" prop="studentId" />
              <el-table-column label="学生姓名" align="center" prop="studentName" />
              <el-table-column label="选课时间" align="center" prop="selectionTime" :formatter="formatDate" />
              <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                  <el-tag :type="getStudentStatusType(scope.row.status)">
                    {{ getStudentStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="成绩" align="center" prop="score" />
              <el-table-column label="挂科原因" align="center" prop="failReason" :show-overflow-tooltip="true" />
              <el-table-column label="操作" align="center" width="150">
                <template #default="scope">
                  <el-button 
                    link 
                    type="primary" 
                    v-if="scope.row.status === 'pending'"
                    @click="updateStudentStatus(scope.row, 'approved')">
                    批准
                  </el-button>
                  <el-button 
                    link 
                    type="danger" 
                    v-if="scope.row.status === 'pending'"
                    @click="updateStudentStatus(scope.row, 'rejected')">
                    拒绝
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="studentTotal > 0"
              :total="studentTotal"
              v-model:page="studentQueryParams.pageNum"
              v-model:limit="studentQueryParams.pageSize"
              @pagination="getStudentList"
            />
          </el-tab-pane>
          <el-tab-pane label="备注信息" name="remark">
            <div v-if="courseInfo.remark" class="remark-content">
              {{ courseInfo.remark }}
            </div>
            <div v-else class="no-remark">
              暂无备注信息
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Back, Calendar, List } from '@element-plus/icons-vue'
import { getRestudyCourse, listRestudyStudents } from '@/api/system/restudy'
import { useRoute, useRouter } from 'vue-router'
import { parseTime } from '@/utils/ruoyi'

const route = useRoute()
const router = useRouter()
const courseId = ref(route.params.id)

// 课程信息
const courseInfo = ref({})

// 加载状态
const loading = ref(false)
const studentsLoading = ref(false)

// 当前激活的标签页
const activeTab = ref('students')

// 学生列表相关
const studentList = ref([])
const studentTotal = ref(0)
const studentQueryParams = reactive({
  pageNum: 1,
  pageSize: 10
})

/** 获取课程信息 */
function getCourseInfo() {
  loading.value = true
  getRestudyCourse(courseId.value).then(response => {
    courseInfo.value = response.data
    loading.value = false
    // 获取学生列表
    getStudentList()
  }).catch(() => {
    loading.value = false
  })
}

/** 获取学生列表 */
function getStudentList() {
  studentsLoading.value = true
  const params = {
    pageNum: studentQueryParams.pageNum,
    pageSize: studentQueryParams.pageSize,
    restudyCourseId: courseId.value
  }
  listRestudyStudents(courseId.value).then(response => {
    studentList.value = response.rows
    studentTotal.value = response.total
    studentsLoading.value = false
  }).catch(() => {
    studentsLoading.value = false
  })
}

/** 更新学生状态 */
function updateStudentStatus(student, status) {
  ElMessage.success("状态更新功能待实现")
  // 这里需要调用后端API来更新学生状态
}

/** 日期格式化 */
function formatDate(row, column) {
  return parseTime(row[column.property])
}

/** 获取课程状态类型 */
function getStatusType(status) {
  switch (status) {
    case 'waiting': return 'info'
    case 'arranging': return 'warning'
    case 'arranged': return 'success'
    default: return ''
  }
}

/** 获取课程状态文本 */
function getStatusText(status) {
  switch (status) {
    case 'waiting': return '等待中'
    case 'arranging': return '排课中'
    case 'arranged': return '已排课'
    default: return status
  }
}

/** 获取学生状态类型 */
function getStudentStatusType(status) {
  switch (status) {
    case 'pending': return 'info'
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    default: return ''
  }
}

/** 获取学生状态文本 */
function getStudentStatusText(status) {
  switch (status) {
    case 'pending': return '待处理'
    case 'approved': return '已批准'
    case 'rejected': return '已拒绝'
    default: return status
  }
}

/** 返回列表 */
function goBack() {
  router.go(-1) // 返回上一页，更灵活
}

/** 前往排课页面 */
function goToSchedule() {
  router.push(`/system/restudy/schedule/${courseId.value}`)
}

/** 跳转到课程列表页面 */
function goToList() {
  router.push('/system/restudy/index')
}

/** 跳转到课程排课页面 */
function goToSchedules() {
  router.push('/system/restudy/schedules')
}

onMounted(() => {
  getCourseInfo()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title {
  font-size: 18px;
  font-weight: bold;
}
.mt20 {
  margin-top: 20px;
}
.remark-content {
  padding: 16px;
  background-color: #f8f8f8;
  border-radius: 4px;
  min-height: 100px;
  line-height: 1.6;
}
.no-remark {
  padding: 16px;
  color: #999;
  font-style: italic;
}
.header-buttons {
  display: flex;
  gap: 10px;
}
</style> 