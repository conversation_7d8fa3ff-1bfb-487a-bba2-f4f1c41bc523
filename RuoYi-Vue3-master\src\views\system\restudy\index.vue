<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">挂科重修课程管理</span>
          <div class="header-buttons">
            <el-button 
              type="info" 
              plain 
              @click="goToDetails">
              <el-icon><Document /></el-icon> 课程详情
            </el-button>
            <el-button 
              type="success" 
              plain 
              @click="goToSchedules">
              <el-icon><Calendar /></el-icon> 课程排课
            </el-button>
            <el-button
              type="primary"
              plain
              @click="handleAdd"
              v-hasPermi="['system:restudy:add']">
              <el-icon><Plus /></el-icon> 新增重修课程
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="86px">
        <el-form-item label="课程名称" prop="courseName">
          <el-input
            v-model="queryParams.courseName"
            placeholder="请输入课程名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="课程代码" prop="courseCode">
          <el-input
            v-model="queryParams.courseCode"
            placeholder="请输入课程代码"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="教师姓名" prop="teacherName">
          <el-input
            v-model="queryParams.teacherName"
            placeholder="请输入教师姓名"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="课程状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="课程状态" clearable>
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery"><el-icon><Search /></el-icon> 搜索</el-button>
          <el-button @click="resetQuery"><el-icon><Refresh /></el-icon> 重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区域 -->
      <el-row :gutter="10" class="mb12">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:restudy:remove']"
          ><el-icon><Delete /></el-icon> 删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            @click="handleExport"
            v-hasPermi="['system:restudy:export']"
          ><el-icon><Download /></el-icon> 导出</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 课程列表 -->
      <el-table v-loading="loading" :data="courseList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="重修课程ID" align="center" prop="id" />
        <el-table-column label="课程名称" align="center" prop="courseName" :show-overflow-tooltip="true" />
        <el-table-column label="课程代码" align="center" prop="courseCode" />
        <el-table-column label="教师" align="center" prop="teacherName" />
        <el-table-column label="学分" align="center" prop="credits" />
        <el-table-column label="课时" align="center" prop="hours" />
        <el-table-column label="当前学生数/最大学生数" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.currentStudents >= scope.row.minStudents ? 'success' : 'danger'">
              {{ scope.row.currentStudents }}/{{ scope.row.maxStudents }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="statusTagType(scope.row.status)">
              {{ statusFormat(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="View"
              @click="handleDetail(scope.row)"
              v-hasPermi="['system:restudy:query']"
            >详情</el-button>
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:restudy:edit']"
              v-if="scope.row.status !== 'arranged'"
            >修改</el-button>
            <el-button
              link
              type="primary"
              icon="Calendar"
              @click="handleSchedule(scope.row)"
              v-hasPermi="['system:restudy:schedule']"
              v-if="scope.row.status !== 'arranged'"
            >排课</el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:restudy:remove']"
              v-if="scope.row.status === 'waiting'"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改重修课程对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="courseForm" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="原课程ID" prop="courseId">
          <el-input v-model="form.courseId" placeholder="请输入原课程ID" />
        </el-form-item>
        <el-form-item label="课程名称" prop="courseName">
          <el-input v-model="form.courseName" placeholder="请输入课程名称" />
        </el-form-item>
        <el-form-item label="课程代码" prop="courseCode">
          <el-input v-model="form.courseCode" placeholder="请输入课程代码" />
        </el-form-item>
        <el-form-item label="教师ID" prop="teacherId">
          <el-input v-model="form.teacherId" placeholder="请输入教师ID" />
        </el-form-item>
        <el-form-item label="教师姓名" prop="teacherName">
          <el-input v-model="form.teacherName" placeholder="请输入教师姓名" />
        </el-form-item>
        <el-form-item label="学分" prop="credits">
          <el-input-number v-model="form.credits" :precision="1" :step="0.5" :min="0.5" placeholder="请输入学分" />
        </el-form-item>
        <el-form-item label="课时" prop="hours">
          <el-input-number v-model="form.hours" :min="1" :precision="0" placeholder="请输入课时" />
        </el-form-item>
        <el-form-item label="所属学期ID" prop="semesterId">
          <el-input v-model="form.semesterId" placeholder="请输入所属学期ID" />
        </el-form-item>
        <el-form-item label="最少学生数" prop="minStudents">
          <el-input-number v-model="form.minStudents" :min="1" :precision="0" placeholder="请输入最少学生数" />
        </el-form-item>
        <el-form-item label="最大学生数" prop="maxStudents">
          <el-input-number v-model="form.maxStudents" :min="1" :precision="0" placeholder="请输入最大学生数" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Delete, Download, View, Edit, Calendar, Document } from '@element-plus/icons-vue'
import { listRestudyCourse, getRestudyCourse, addRestudyCourse, updateRestudyCourse, delRestudyCourse } from '@/api/system/restudy'
import { useRouter } from 'vue-router'

const router = useRouter()

// 遮罩层
const loading = ref(false)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 重修课程表格数据
const courseList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)

// 状态数据字典
const statusOptions = [
  { value: "waiting", label: "等待中" },
  { value: "arranging", label: "排课中" },
  { value: "arranged", label: "已排课" }
]

// 表单参数
const form = reactive({
  id: null,
  courseId: null,
  courseName: null,
  courseCode: null,
  teacherId: null,
  teacherName: null,
  credits: 1,
  hours: 16,
  semesterId: null,
  status: "waiting",
  minStudents: 1,
  maxStudents: 60,
  currentStudents: 0,
  classroomId: null,
  classroomName: null,
  timeslotId: null,
  timeslotInfo: null,
  remark: null
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  courseName: null,
  courseCode: null,
  teacherName: null,
  status: null
})

// 表单校验
const rules = reactive({
  courseId: [
    { required: true, message: "原课程ID不能为空", trigger: "blur" }
  ],
  courseName: [
    { required: true, message: "课程名称不能为空", trigger: "blur" }
  ],
  courseCode: [
    { required: true, message: "课程代码不能为空", trigger: "blur" }
  ],
  credits: [
    { required: true, message: "学分不能为空", trigger: "blur" }
  ],
  hours: [
    { required: true, message: "课时不能为空", trigger: "blur" }
  ],
  semesterId: [
    { required: true, message: "所属学期ID不能为空", trigger: "blur" }
  ]
})

const courseForm = ref()

/** 查询重修课程列表 */
function getList() {
  loading.value = true
  listRestudyCourse(queryParams).then(response => {
    courseList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.id = null
  form.courseId = null
  form.courseName = null
  form.courseCode = null
  form.teacherId = null
  form.teacherName = null
  form.credits = 1
  form.hours = 16
  form.semesterId = null
  form.status = "waiting"
  form.minStudents = 1
  form.maxStudents = 60
  form.currentStudents = 0
  form.classroomId = null
  form.classroomName = null
  form.timeslotId = null
  form.timeslotInfo = null
  form.remark = null
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.courseName = null
  queryParams.courseCode = null
  queryParams.teacherName = null
  queryParams.status = null
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 状态格式化 */
function statusFormat(status) {
  return statusOptions.find(item => item.value === status)?.label || status
}

/** 状态标签类型 */
function statusTagType(status) {
  switch (status) {
    case 'waiting': return 'info'
    case 'arranging': return 'warning'
    case 'arranged': return 'success'
    default: return ''
  }
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加重修课程"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const courseId = row.id || ids.value[0]
  getRestudyCourse(courseId).then(response => {
    Object.assign(form, response.data)
    open.value = true
    title.value = "修改重修课程"
  })
}

/** 详情按钮操作 */
function handleDetail(row) {
  router.push('/system/restudy/detail/' + row.id)
}

/** 排课按钮操作 */
function handleSchedule(row) {
  router.push('/system/restudy/schedule/' + row.id)
}

/** 提交按钮 */
function submitForm() {
  courseForm.value.validate(valid => {
    if (valid) {
      if (form.id !== null) {
        updateRestudyCourse(form).then(response => {
          ElMessage.success("修改成功")
          open.value = false
          getList()
        })
      } else {
        addRestudyCourse(form).then(response => {
          ElMessage.success("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const courseIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除重修课程编号为"' + courseIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delRestudyCourse(courseIds)
  }).then(() => {
    getList()
    ElMessage.success("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  // 导出操作
}

/** 跳转到课程详情页面 */
function goToDetails() {
  router.push('/system/restudy/details')
}

/** 跳转到课程排课页面 */
function goToSchedules() {
  router.push('/system/restudy/schedules')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title {
  font-size: 18px;
  font-weight: bold;
}
.mb12 {
  margin-bottom: 12px;
}
.header-buttons {
  display: flex;
  gap: 10px;
}
</style> 