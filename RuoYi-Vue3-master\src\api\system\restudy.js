import request from '@/utils/request'

// 查询挂科重修课程列表
export function listRestudyCourse(query) {
  return request({
    url: '/system/restudy/list',
    method: 'get',
    params: query
  })
}

// 查询挂科重修课程详细
export function getRestudyCourse(id) {
  return request({
    url: `/system/restudy/${id}`,
    method: 'get'
  })
}

// 新增挂科重修课程
export function addRestudyCourse(data) {
  return request({
    url: '/system/restudy',
    method: 'post',
    data: data
  })
}

// 修改挂科重修课程
export function updateRestudyCourse(data) {
  return request({
    url: '/system/restudy',
    method: 'put',
    data: data
  })
}

// 删除挂科重修课程
export function delRestudyCourse(id) {
  return request({
    url: `/system/restudy/${id}`,
    method: 'delete'
  })
}

// 查询重修课程的学生列表
export function listRestudyStudents(restudyCourseId) {
  return request({
    url: `/system/restudy/students/${restudyCourseId}`,
    method: 'get'
  })
}

// 学生选择重修课程
export function studentSelectCourse(data) {
  return request({
    url: '/system/restudy/student/select',
    method: 'post',
    data: data
  })
}

// 获取学生重修课程列表
export function getStudentCourses(studentId) {
  return request({
    url: `/system/restudy/student/courses/${studentId}`,
    method: 'get'
  })
}

// 获取课程学生共同时间段
export function getCommonTime(restudyCourseId) {
  return request({
    url: `/system/restudy/common-time/${restudyCourseId}`,
    method: 'get'
  })
}

// 为重修课程排课
export function scheduleCourse(data) {
  return request({
    url: '/system/restudy/schedule',
    method: 'post',
    data: data
  })
} 