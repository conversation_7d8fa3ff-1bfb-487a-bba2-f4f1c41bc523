// src/api/system/strategy.js
import request from '@/utils/request'

// 查询策略列表
export function getStrategies(query) {
  return request({
    url: '/system/strategy/list',
    method: 'get',
    params: query
  })
}

// 查询策略详细
export function getStrategy(id) {
  return request({
    url: '/system/strategy/' + id,
    method: 'get'
  })
}

// 新增策略
export function addStrategy(data) {
  return request({
    url: '/system/strategy',
    method: 'post',
    data: data
  })
}

// 修改策略
export function updateStrategy(data) {
  return request({
    url: '/system/strategy',
    method: 'put',
    data: data
  })
}

// 修改策略状态
export function updateStrategyStatus(data) {
  return request({
    url: '/system/strategy/status',
    method: 'put',
    data: data
  })
}

// 删除策略
export function deleteStrategy(id) {
  return request({
    url: '/system/strategy/' + id,
    method: 'delete'
  })
}

// 获取策略历史记录
export function getStrategyHistory(strategyId) {
  return request({
    url: '/system/strategy/history/' + strategyId,
    method: 'get'
  })
}

// 还原策略版本
export function restoreStrategyVersion(historyId) {
  return request({
    url: '/system/strategy/restore/' + historyId,
    method: 'post'
  })
}

// 获取策略版本详情
export function getStrategyVersionDetail(versionId) {
  return request({
    url: '/system/strategy/version/' + versionId,
    method: 'get'
  })
}

// 比较策略版本
export function compareStrategyVersions(versionId1, versionId2) {
  return request({
    url: '/system/strategy/compare/' + versionId1 + '/' + versionId2,
    method: 'get'
  })
}
