<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">重修课程排课列表</span>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="86px">
        <el-form-item label="课程名称" prop="courseName">
          <el-input
            v-model="queryParams.courseName"
            placeholder="请输入课程名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="课程代码" prop="courseCode">
          <el-input
            v-model="queryParams.courseCode"
            placeholder="请输入课程代码"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="教师姓名" prop="teacherName">
          <el-input
            v-model="queryParams.teacherName"
            placeholder="请输入教师姓名"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="课程状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="课程状态" clearable>
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery"><el-icon><Search /></el-icon> 搜索</el-button>
          <el-button @click="resetQuery"><el-icon><Refresh /></el-icon> 重置</el-button>
        </el-form-item>
      </el-form>

      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>

      <!-- 课程列表 -->
      <el-table v-loading="loading" :data="courseList">
        <el-table-column label="重修课程ID" align="center" prop="id" />
        <el-table-column label="课程名称" align="center" prop="courseName" :show-overflow-tooltip="true" />
        <el-table-column label="课程代码" align="center" prop="courseCode" />
        <el-table-column label="教师" align="center" prop="teacherName" />
        <el-table-column label="学分" align="center" prop="credits" />
        <el-table-column label="课时" align="center" prop="hours" />
        <el-table-column label="当前学生数/最大学生数" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.currentStudents >= scope.row.minStudents ? 'success' : 'danger'">
              {{ scope.row.currentStudents }}/{{ scope.row.maxStudents }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="statusTagType(scope.row.status)">
              {{ statusFormat(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Calendar"
              @click="handleSchedule(scope.row)"
              v-hasPermi="['system:restudy:schedule']"
              v-if="scope.row.status !== 'arranged'"
            >进行排课</el-button>
            <el-button
              link
              type="info"
              icon="View"
              @click="viewSchedule(scope.row)"
              v-if="scope.row.status === 'arranged'"
            >查看排课</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { listRestudyCourse } from '@/api/system/restudy'
import { useRouter } from 'vue-router'

const router = useRouter()

// 遮罩层
const loading = ref(false)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 重修课程表格数据
const courseList = ref([])

// 状态数据字典
const statusOptions = [
  { value: "waiting", label: "等待中" },
  { value: "arranging", label: "排课中" },
  { value: "arranged", label: "已排课" }
]

// 查询参数，默认只查询待排课状态
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  courseName: null,
  courseCode: null,
  teacherName: null,
  status: null
})

/** 查询重修课程列表 */
function getList() {
  loading.value = true
  listRestudyCourse(queryParams).then(response => {
    courseList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.courseName = null
  queryParams.courseCode = null
  queryParams.teacherName = null
  queryParams.status = null
  handleQuery()
}

/** 状态格式化 */
function statusFormat(status) {
  return statusOptions.find(item => item.value === status)?.label || status
}

/** 状态标签类型 */
function statusTagType(status) {
  switch (status) {
    case 'waiting': return 'info'
    case 'arranging': return 'warning'
    case 'arranged': return 'success'
    default: return ''
  }
}

/** 排课按钮操作 */
function handleSchedule(row) {
  router.push('/system/restudy/schedule/' + row.id)
}

/** 查看排课按钮操作 */
function viewSchedule(row) {
  router.push('/system/restudy/detail/' + row.id)
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title {
  font-size: 18px;
  font-weight: bold;
}
</style> 