<!-- CourseScheduleShare.vue -->
<script setup>
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
import { ElMessage } from "element-plus"
import { computed, onMounted, reactive, ref, watch } from "vue"
import { useRoute } from "vue-router"

// 路由信息
const route = useRoute()

// 查询信息
const shareInfo = reactive({
  viewType: "class",
  semester: "2024-1",
  targetId: "",
  mode: "week",
  week: 1,
  loaded: false
})

// 查询类型标签映射
const viewTypeLabels = {
  student: "学生",
  teacher: "教师",
  classroom: "教室",
  class: "班级"
}

// 目标对象信息
const targetInfo = ref({
  id: "",
  name: ""
})

// 标题信息
const pageTitle = computed(() => {
  if (!shareInfo.loaded) return "课表加载中..."
  return `${targetInfo.value.name}的${shareInfo.semester}学期课表`
})

// 视图模式
const viewMode = ref("week")
const currentWeek = ref(1)

// 周视图数据
const weekDays = [
  { label: "周一", value: "monday" },
  { label: "周二", value: "tuesday" },
  { label: "周三", value: "wednesday" },
  { label: "周四", value: "thursday" },
  { label: "周五", value: "friday" },
  { label: "周六", value: "saturday" },
  { label: "周日", value: "sunday" }
]

const weeklySchedule = [
  { time: "第1-2节", value: "1-2" },
  { time: "第3-4节", value: "3-4" },
  { time: "第5-6节", value: "5-6" },
  { time: "第7-8节", value: "7-8" },
  { time: "第9-10节", value: "9-10" },
  { time: "第11-12节", value: "11-12" }
]

// 月视图数据
const currentMonth = ref(new Date().getMonth())
const currentYear = ref(new Date().getFullYear())

const currentYearMonth = computed(() => {
  return `${currentYear.value}年${currentMonth.value + 1}月`
})

const monthDays = computed(() => {
  const year = currentYear.value
  const month = currentMonth.value
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  const daysInMonth = lastDay.getDate()
  const firstDayOfWeek = firstDay.getDay() || 7 // 调整为周一为1，周日为7

  const days = []

  // 上个月的最后几天
  const prevMonthLastDay = new Date(year, month, 0).getDate()
  for (let i = firstDayOfWeek - 1; i > 0; i--) {
    days.push({
      date: `${year}-${month === 0 ? 12 : month}-${prevMonthLastDay - i + 1}`,
      dayNumber: prevMonthLastDay - i + 1,
      currentMonth: false,
      isToday: false,
      courses: []
    })
  }

  // 当前月的天数
  const today = new Date()
  for (let i = 1; i <= daysInMonth; i++) {
    const isToday = today.getDate() === i && today.getMonth() === month && today.getFullYear() === year
    days.push({
      date: `${year}-${month + 1}-${i}`,
      dayNumber: i,
      currentMonth: true,
      isToday,
      courses: getCoursesForDay(year, month + 1, i)
    })
  }

  // 下个月的前几天
  const remainingDays = 42 - days.length // 6行7列总共42个格子
  for (let i = 1; i <= remainingDays; i++) {
    days.push({
      date: `${year}-${month + 2 > 12 ? 1 : month + 2}-${i}`,
      dayNumber: i,
      currentMonth: false,
      isToday: false,
      courses: []
    })
  }

  return days
})

// 课程详情
const courseDetailVisible = ref(false)
const selectedCourse = ref(null)

// 课程数据
const coursesData = ref([])

// 从URL参数加载数据
function loadFromUrlParams() {
  const params = route.query
  
  if (params.viewType) shareInfo.viewType = params.viewType
  if (params.semester) shareInfo.semester = params.semester
  if (params.targetId) shareInfo.targetId = params.targetId
  if (params.mode) shareInfo.mode = params.mode
  if (params.week) shareInfo.week = parseInt(params.week) || 1
  
  // 设置初始视图模式
  viewMode.value = shareInfo.mode
  currentWeek.value = shareInfo.week
  
  // 加载目标信息
  loadTargetInfo()
  
  // 加载课程数据
  loadCourseData()
  
  shareInfo.loaded = true
}

// 加载目标信息
function loadTargetInfo() {
  // 模拟从服务器获取目标信息
  const options = {
    student: [
      { id: "s001", name: "张三" },
      { id: "s002", name: "李四" },
      { id: "s003", name: "王五" }
    ],
    teacher: [
      { id: "t001", name: "教师A" },
      { id: "t002", name: "教师B" },
      { id: "t003", name: "教师C" }
    ],
    classroom: [
      { id: "r001", name: "一教101" },
      { id: "r002", name: "一教102" },
      { id: "r003", name: "二教201" }
    ],
    class: [
      { id: "c001", name: "计算机2101班" },
      { id: "c002", name: "计算机2102班" },
      { id: "c003", name: "软件工程2101班" }
    ]
  }
  
  // 查找目标信息
  const targetList = options[shareInfo.viewType] || []
  const target = targetList.find(item => item.id === shareInfo.targetId)
  
  if (target) {
    targetInfo.value = target
  } else {
    targetInfo.value = { id: shareInfo.targetId, name: `未知${viewTypeLabels[shareInfo.viewType]}` }
    ElMessage.warning("未找到对应的信息，可能链接已过期或信息已变更")
  }
}

// 加载课程数据
function loadCourseData() {
  // 这里模拟从服务器获取课程数据
  // 实际应用中，应该调用API获取真实数据
  const mockData = [
    {
      id: "course001",
      name: "高等数学",
      teacher: "张教授",
      location: "一教101",
      dayOfWeek: "monday",
      timeSlot: "1-2",
      weeks: "1-16周",
      weekList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
      type: "必修课",
      credit: 4,
      className: "计算机2101班",
      classId: "c001",
      hasConflict: false
    },
    {
      id: "course002",
      name: "线性代数",
      teacher: "李教授",
      location: "一教102",
      dayOfWeek: "tuesday",
      timeSlot: "3-4",
      weeks: "1-16周",
      weekList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
      type: "必修课",
      credit: 3,
      className: "计算机2101班",
      classId: "c001",
      hasConflict: false
    },
    {
      id: "course003",
      name: "大学英语",
      teacher: "王教授",
      location: "二教201",
      dayOfWeek: "wednesday",
      timeSlot: "5-6",
      weeks: "1-16周单周",
      weekList: [1, 3, 5, 7, 9, 11, 13, 15],
      type: "必修课",
      credit: 2,
      className: "计算机2101班",
      classId: "c001",
      hasConflict: true,
      conflicts: ["与\"程序设计\"在5-6节存在冲突"]
    },
    {
      id: "course004",
      name: "程序设计",
      teacher: "赵教授",
      location: "计算机楼301",
      dayOfWeek: "wednesday",
      timeSlot: "5-6",
      weeks: "1-16周双周",
      weekList: [2, 4, 6, 8, 10, 12, 14, 16],
      type: "必修课",
      credit: 3,
      className: "计算机2101班",
      classId: "c001",
      hasConflict: true,
      conflicts: ["与\"大学英语\"在5-6节存在冲突"]
    }
  ]
  
  // 根据目标ID筛选数据
  // 在实际应用中，这部分筛选应该在服务器端完成
  if (shareInfo.viewType === 'class') {
    coursesData.value = mockData.filter(course => course.classId === shareInfo.targetId)
  } else if (shareInfo.viewType === 'teacher') {
    coursesData.value = mockData.filter(course => course.teacher.includes(targetInfo.value.name))
  } else if (shareInfo.viewType === 'classroom') {
    coursesData.value = mockData.filter(course => course.location === targetInfo.value.name)
  } else if (shareInfo.viewType === 'student') {
    // 学生课表筛选逻辑
    // 这里简化处理，实际应基于学生所在班级等信息
    coursesData.value = mockData.filter(course => course.classId === 'c001')
  } else {
    coursesData.value = []
  }
}

// 处理课程详情查看
function showCourseDetail(course) {
  selectedCourse.value = course
  courseDetailVisible.value = true
}

// 处理周次变化
function handleWeekChange() {
  // 可以在这里添加额外逻辑
}

// 月份导航
function prevMonth() {
  if (currentMonth.value === 0) {
    currentMonth.value = 11
    currentYear.value--
  } else {
    currentMonth.value--
  }
}

function nextMonth() {
  if (currentMonth.value === 11) {
    currentMonth.value = 0
    currentYear.value++
  } else {
    currentMonth.value++
  }
}

// 辅助函数
function getCoursesByTimeAndDay(timeSlot, dayOfWeek) {
  // 根据当前周次过滤课程
  return coursesData.value.filter((course) => {
    return course.timeSlot === timeSlot
      && course.dayOfWeek === dayOfWeek
      && course.weekList.includes(currentWeek.value)
  })
}

function getCoursesForDay(year, month, day) {
  // 计算当天是第几周的星期几
  const date = new Date(year, month - 1, day)
  const dayOfWeek = date.getDay() || 7 // 调整为周一为1，周日为7

  // 根据学期开始日期计算当前是第几周
  // 这里假设学期从2024年2月26日开始
  const semesterStart = new Date(2024, 1, 26) // 2月26日
  const diffDays = Math.floor((date - semesterStart) / (24 * 60 * 60 * 1000))
  const weekNumber = Math.floor(diffDays / 7) + 1

  if (weekNumber < 1 || weekNumber > 20) {
    return [] // 不在学期内
  }

  // 映射星期几到dayOfWeek
  const dayMapping = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]

  // 过滤出当天的课程
  return coursesData.value.filter((course) => {
    return course.dayOfWeek === dayMapping[dayOfWeek] && course.weekList.includes(weekNumber)
  }).map(course => ({
    ...course,
    time: `${course.timeSlot.replace("-", "-")}节`
  }))
}

// 处理打印
function handlePrint() {
  window.print()
}

// 监听路由变化
watch(() => route.query, () => {
  loadFromUrlParams()
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadFromUrlParams()
})
</script>

<template>
  <div class="schedule-share-container">
    <!-- 头部信息区域 -->
    <el-card class="header-card animate__animated animate__fadeIn">
      <div class="share-header">
        <h1 class="page-title">{{ pageTitle }}</h1>
      </div>
      <el-divider />
      <div class="share-info">
        <div class="info-item">
          <label>查询类型:</label>
          <span>{{ viewTypeLabels[shareInfo.viewType] }}课表</span>
        </div>
        <div class="info-item">
          <label>学年学期:</label>
          <span>{{ shareInfo.semester }}</span>
        </div>
        <div class="info-item">
          <label>{{ viewTypeLabels[shareInfo.viewType] }}:</label>
          <span>{{ targetInfo.name }}</span>
        </div>
      </div>
    </el-card>

    <!-- 课表区域 -->
    <el-card class="schedule-card animate__animated animate__fadeIn animate__delay-1s">
      <template #header>
        <div class="card-header">
          <span class="card-title">课表信息</span>
          <div class="view-controls">
            <el-radio-group v-model="viewMode" size="small" class="view-switcher">
              <el-radio-button label="week">
                周视图
              </el-radio-button>
              <el-radio-button label="month">
                月视图
              </el-radio-button>
            </el-radio-group>
            <el-select 
              v-model="currentWeek" 
              size="small" 
              placeholder="周次" 
              v-if="viewMode === 'week'" 
              class="week-selector"
              @change="handleWeekChange"
            >
              <el-option
                v-for="i in 20"
                :key="i"
                :label="`第${i}周`"
                :value="i"
              />
            </el-select>
          </div>
        </div>
      </template>

      <!-- 周视图 -->
      <div v-if="viewMode === 'week'" class="week-view animate__animated" :class="{'animate__fadeIn': viewMode === 'week'}">
        <el-table :data="weeklySchedule" border class="schedule-table">
          <el-table-column prop="time" label="节次/星期" width="100" />
          <el-table-column
            v-for="day in weekDays"
            :key="day.value"
            :label="day.label"
          >
            <template #default="scope">
              <div
                v-for="course in getCoursesByTimeAndDay(scope.row.value, day.value)"
                :key="course.id"
                class="course-cell"
                :class="{ 'conflict-course': course.hasConflict }"
                @click="showCourseDetail(course)"
              >
                <div class="course-name">
                  {{ course.name }}
                </div>
                <div class="course-info">
                  <i class="el-icon-user mr-5"></i>{{ course.teacher }}
                </div>
                <div class="course-info">
                  <i class="el-icon-location mr-5"></i>{{ course.location }}
                </div>
                <div class="course-info" v-if="course.weeks">
                  <i class="el-icon-date mr-5"></i>{{ course.weeks }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 月视图 -->
      <div v-else-if="viewMode === 'month'" class="month-view animate__animated" :class="{'animate__fadeIn': viewMode === 'month'}">
        <div class="month-controls">
          <el-button-group class="month-navigator">
            <el-button @click="prevMonth" icon="ArrowLeft" class="nav-btn" />
            <el-button disabled class="month-display">
              {{ currentYearMonth }}
            </el-button>
            <el-button @click="nextMonth" icon="ArrowRight" class="nav-btn" />
          </el-button-group>
        </div>
        <div class="month-calendar">
          <div class="week-header">
            <div v-for="day in weekDays" :key="day.value" class="day-header">
              {{ day.label }}
            </div>
          </div>
          <div class="month-days">
            <div
              v-for="day in monthDays"
              :key="day.date"
              class="day-cell"
              :class="{ 'non-current-month': !day.currentMonth, 'today': day.isToday }"
            >
              <div class="day-number">
                {{ day.dayNumber }}
              </div>
              <div class="day-courses">
                <transition-group name="course-list">
                  <div
                    v-for="course in day.courses"
                    :key="course.id"
                    class="month-course-item"
                    :class="{ 'conflict-course': course.hasConflict }"
                    @click="showCourseDetail(course)"
                  >
                    {{ course.name }}
                  </div>
                </transition-group>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 课程详情对话框 -->
    <el-dialog
      v-model="courseDetailVisible"
      title="课程详情"
      width="50%"
      class="course-detail-dialog"
      destroy-on-close
    >
      <template v-if="selectedCourse">
        <el-descriptions :column="2" border class="course-descriptions">
          <el-descriptions-item label="课程名称">
            {{ selectedCourse.name }}
          </el-descriptions-item>
          <el-descriptions-item label="教师">
            {{ selectedCourse.teacher }}
          </el-descriptions-item>
          <el-descriptions-item label="教室">
            {{ selectedCourse.location }}
          </el-descriptions-item>
          <el-descriptions-item label="上课时间">
            {{ selectedCourse.timeSlot.replace("-", "-") }}节
          </el-descriptions-item>
          <el-descriptions-item label="周次">
            {{ selectedCourse.weeks }}
          </el-descriptions-item>
          <el-descriptions-item label="课程类型">
            {{ selectedCourse.type }}
          </el-descriptions-item>
          <el-descriptions-item label="学分">
            {{ selectedCourse.credit }}
          </el-descriptions-item>
          <el-descriptions-item label="授课班级">
            {{ selectedCourse.className }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedCourse.hasConflict" class="conflict-warning animate__animated animate__pulse animate__infinite">
          <el-alert
            title="课程冲突提示"
            type="warning"
            description="该课程与其他课程存在时间或地点冲突，请注意调整！"
            show-icon
            :closable="false"
            class="conflict-alert"
          />
          <div class="conflict-detail" v-if="selectedCourse.conflicts">
            <p>冲突详情：</p>
            <ul>
              <li v-for="(conflict, index) in selectedCourse.conflicts" :key="index">
                {{ conflict }}
              </li>
            </ul>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 分享页脚注 -->
    <div class="share-footer animate__animated animate__fadeIn animate__delay-2s">
      <p>此课表由课程管理系统生成分享 © 2024</p>
    </div>
  </div>
</template>

<style scoped>
/* 引入动画库 */
@import url('https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css');

/* 全局样式变量 */
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  --primary-bg: #ecf5ff;
  --hover-bg: #d9ecff;
  --border-radius: 8px;
  --box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --hover-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  --transition-time: 0.3s;
}

/* 通用样式 */
.mr-5 {
  margin-right: 5px;
}

.schedule-share-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
  transition: background-color 0.5s ease;
}

.schedule-share-container:hover {
  background-color: #f0f2f5;
}

/* 卡片样式 */
.header-card,
.schedule-card {
  margin-bottom: 24px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: all var(--transition-time);
  overflow: hidden;
}

.header-card:hover,
.schedule-card:hover {
  box-shadow: var(--hover-shadow);
  transform: translateY(-2px);
}

.header-card :deep(.el-card__body) {
  padding: 20px;
}

/* 头部样式 */
.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.share-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 600;
  margin-right: 8px;
  color: #606266;
}

.info-item span {
  color: #303133;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.view-controls {
  display: flex;
  gap: 12px;
}

.view-switcher {
  transition: all var(--transition-time);
}

.view-switcher:hover {
  transform: translateY(-2px);
}

.week-selector {
  min-width: 100px;
  transition: all var(--transition-time);
}

.week-selector:hover {
  transform: translateY(-2px);
}

/* 按钮效果 */
.btn-with-effect {
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.btn-with-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-with-effect:active {
  transform: translateY(1px);
}

.btn-with-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-with-effect:hover::before {
  width: 300px;
  height: 300px;
}

/* 周视图样式 */
.week-view {
  margin-top: 20px;
  transition: all 0.5s;
}

.schedule-table {
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all var(--transition-time);
}

.schedule-table:hover {
  box-shadow: var(--hover-shadow);
}

.schedule-table :deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  height: 48px;
}

.schedule-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.course-cell {
  padding: 10px;
  margin-bottom: 6px;
  border-radius: 6px;
  background-color: var(--primary-bg);
  border-left: 3px solid var(--primary-color);
  cursor: pointer;
  transition: all var(--transition-time);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease;
}

.course-cell:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: var(--hover-bg);
}

.conflict-course {
  background-color: #fff5f5;
  border-left: 3px solid var(--danger-color);
}

.conflict-course:hover {
  background-color: #ffecec;
}

.course-name {
  font-weight: 600;
  margin-bottom: 6px;
  color: #303133;
}

.course-info {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

/* 月视图样式 */
.month-view {
  margin-top: 24px;
  transition: all 0.5s;
}

.month-controls {
  margin-bottom: 20px;
  text-align: center;
}

.month-navigator {
  display: inline-flex;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.month-display {
  min-width: 150px;
  font-weight: 600;
}

.nav-btn {
  transition: all 0.3s;
}

.nav-btn:hover {
  background-color: #f5f7fa;
  color: var(--primary-color);
}

.week-header {
  display: flex;
  background: #fff;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.day-header {
  flex: 1;
  text-align: center;
  padding: 12px;
  font-weight: 600;
  color: #606266;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.month-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background-color: #ebeef5;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.day-cell {
  background-color: white;
  min-height: 120px;
  padding: 8px;
  position: relative;
  transition: all 0.3s;
}

.day-cell:hover {
  background-color: #fafafa;
  transform: scale(1.01);
  z-index: 1;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.day-number {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: 500;
  transition: all 0.3s;
}

.day-cell:hover .day-number {
  transform: scale(1.1);
}

.non-current-month {
  background-color: #fafafa;
  color: #c0c4cc;
}

.today .day-number {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
}

.day-courses {
  margin-top: 36px;
  max-height: calc(100% - 36px);
  overflow-y: auto;
}

.month-course-item {
  padding: 4px 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  background-color: var(--primary-bg);
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: all 0.3s;
  border-left: 2px solid var(--primary-color);
}

.month-course-item:hover {
  background-color: var(--hover-bg);
  transform: translateX(3px);
  padding-left: 12px;
}

/* 月视图课程列表动画 */
.course-list-enter-active,
.course-list-leave-active {
  transition: all 0.5s ease;
}

.course-list-enter-from,
.course-list-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 对话框样式 */
.course-detail-dialog {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.course-descriptions {
  margin-bottom: 20px;
}

/* 冲突提示样式 */
.conflict-warning {
  margin-top: 24px;
}

.conflict-alert {
  margin-bottom: 10px;
}

.conflict-detail {
  margin-top: 12px;
  padding: 12px;
  background-color: #fff7e6;
  border-radius: 6px;
  border-left: 3px solid #faad14;
  transition: all 0.3s;
}

.conflict-detail:hover {
  transform: translateX(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.conflict-detail p {
  color: #d46b08;
  font-weight: 500;
  margin-bottom: 8px;
}

.conflict-detail ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

/* 页脚样式 */
.share-footer {
  text-align: center;
  margin-top: 30px;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

/* 打印样式 */
@media print {
  .header-actions,
  .view-controls,
  .el-dialog,
  .share-footer {
    display: none !important;
  }

  .schedule-share-container {
    padding: 0;
    background-color: white;
  }

  .header-card,
  .schedule-card {
    box-shadow: none;
    margin: 0;
    border: none;
  }
  
  .header-card :deep(.el-card__header),
  .schedule-card :deep(.el-card__header) {
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
  }
}

/* 全局渐变动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 