<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学号" prop="studentId">
        <el-input
          v-model="queryParams.studentId"
          placeholder="请输入学号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      
      <el-form-item label="性别" prop="gender">
        <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable>
          <el-option
            v-for="dict in gender"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
     
      <el-form-item label="身份证号" prop="idCard">
        <el-input
          v-model="queryParams.idCard"
          placeholder="请输入身份证号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="国籍" prop="nationality">
        <el-select v-model="queryParams.nationality" placeholder="请选择国籍" clearable>
          <el-option
            v-for="dict in nationality"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="民族" prop="ethnicity">
        <el-select v-model="queryParams.ethnicity" placeholder="请选择民族" clearable>
          <el-option
            v-for="dict in nation"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="政治面貌" prop="politicalStatus">
        <el-select v-model="queryParams.politicalStatus" placeholder="请选择政治面貌" clearable>
          <el-option
            v-for="dict in political_landscape"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      
   
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:student:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:student:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:student:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:student:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="studentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学号" align="center" prop="studentId" />
      <el-table-column label="用户名/登录名" align="center" prop="username" />
      <el-table-column label="学生姓名" align="center" prop="fullName" />
      <el-table-column label="英文名" align="center" prop="englishName" />
      <el-table-column label="性别" align="center" prop="gender">
        <template #default="scope">
          <dict-tag :options="gender" :value="scope.row.gender"/>
        </template>
      </el-table-column>
      <el-table-column label="出生日期" align="center" prop="birthDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.birthDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="身份证号" align="center" prop="idCard" />
      <el-table-column label="国籍" align="center" prop="nationality">
        <template #default="scope">
          <dict-tag :options="nationality" :value="scope.row.nationality"/>
        </template>
      </el-table-column>
      <el-table-column label="民族" align="center" prop="ethnicity">
        <template #default="scope">
          <dict-tag :options="nation" :value="scope.row.ethnicity"/>
        </template>
      </el-table-column>
      <el-table-column label="政治面貌" align="center" prop="politicalStatus">
        <template #default="scope">
          <dict-tag :options="political_landscape" :value="scope.row.politicalStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="入学日期" align="center" prop="enrollmentDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.enrollmentDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预计毕业日期" align="center" prop="graduationDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.graduationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学历层次" align="center" prop="educationLevel">
        <template #default="scope">
          <dict-tag :options="education" :value="scope.row.educationLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="学籍状态" align="center" prop="studentStatus">
        <template #default="scope">
          <dict-tag :options="student_status" :value="scope.row.studentStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="所属院系代码" align="center" prop="departmentCode" />
      <el-table-column label="专业代码" align="center" prop="majorCode" />
      <el-table-column label="专业方向代码" align="center" prop="majorDirectionCode" />
      <el-table-column label="班级ID" align="center" prop="classId" />
      <el-table-column label="当前年级" align="center" prop="currentGrade" />
      <el-table-column label="录取类别" align="center" prop="admissionCategory" />
      <el-table-column label="生源地" align="center" prop="studentSource" />
      <el-table-column label="招生类型" align="center" prop="enrollmentType">
        <template #default="scope">
          <dict-tag :options="enrollment_type" :value="scope.row.enrollmentType"/>
        </template>
      </el-table-column>
      <el-table-column label="是否国际生" align="center" prop="isInternational">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isInternational"/>
        </template>
      </el-table-column>
      <el-table-column label="是否交换生" align="center" prop="isExchange">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isExchange"/>
        </template>
      </el-table-column>
      <el-table-column label="电子邮箱" align="center" prop="email" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="紧急联系人" align="center" prop="emergencyContact" />
      <el-table-column label="紧急联系电话" align="center" prop="emergencyPhone" />
      <el-table-column label="当前住址" align="center" prop="currentAddress" />
      <el-table-column label="家庭住址" align="center" prop="homeAddress" />
      <el-table-column label="邮政编码" align="center" prop="postalCode" />
      <el-table-column label="特殊需求" align="center" prop="specialRequirements" />
      <el-table-column label="最后登录时间" align="center" prop="lastLoginTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastLoginTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否激活" align="center" prop="isActive" />
      <el-table-column label="是否删除" align="center" prop="isDeleted" />
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updatedAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remarks" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:student:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:student:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改学生信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="studentRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户名/登录名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名/登录名" />
        </el-form-item>
        <el-form-item label="学生姓名" prop="fullName">
          <el-input v-model="form.fullName" placeholder="请输入学生姓名" />
        </el-form-item>
        <el-form-item label="英文名" prop="englishName">
          <el-input v-model="form.englishName" placeholder="请输入英文名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender" placeholder="请选择性别">
            <el-option
              v-for="dict in gender"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出生日期" prop="birthDate">
          <el-date-picker clearable
            v-model="form.birthDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择出生日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="国籍" prop="nationality">
          <el-select v-model="form.nationality" placeholder="请选择国籍">
            <el-option
              v-for="dict in nationality"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="民族" prop="ethnicity">
          <el-select v-model="form.ethnicity" placeholder="请选择民族">
            <el-option
              v-for="dict in nation"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="政治面貌" prop="politicalStatus">
          <el-select v-model="form.politicalStatus" placeholder="请选择政治面貌">
            <el-option
              v-for="dict in political_landscape"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="入学日期" prop="enrollmentDate">
          <el-date-picker clearable
            v-model="form.enrollmentDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择入学日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预计毕业日期" prop="graduationDate">
          <el-date-picker clearable
            v-model="form.graduationDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择预计毕业日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="学历层次" prop="educationLevel">
          <el-select v-model="form.educationLevel" placeholder="请选择学历层次">
            <el-option
              v-for="dict in education"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学籍状态" prop="studentStatus">
          <el-select v-model="form.studentStatus" placeholder="请选择学籍状态">
            <el-option
              v-for="dict in student_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属院系代码" prop="departmentCode">
          <el-input v-model="form.departmentCode" placeholder="请输入所属院系代码" />
        </el-form-item>
        <el-form-item label="专业代码" prop="majorCode">
          <el-input v-model="form.majorCode" placeholder="请输入专业代码" />
        </el-form-item>
        <el-form-item label="专业方向代码" prop="majorDirectionCode">
          <el-input v-model="form.majorDirectionCode" placeholder="请输入专业方向代码" />
        </el-form-item>
        <el-form-item label="班级ID" prop="classId">
          <el-input v-model="form.classId" placeholder="请输入班级ID" />
        </el-form-item>
        <el-form-item label="当前年级" prop="currentGrade">
          <el-input-number v-model="form.currentGrade" :min="1" :max="127" placeholder="请输入当前年级" />
        </el-form-item>
        <el-form-item label="录取类别" prop="admissionCategory">
          <el-input v-model="form.admissionCategory" placeholder="请输入录取类别" />
        </el-form-item>
        <el-form-item label="生源地" prop="studentSource">
          <el-input v-model="form.studentSource" placeholder="请输入生源地" />
        </el-form-item>
        <el-form-item label="招生类型" prop="enrollmentType">
          <el-select v-model="form.enrollmentType" placeholder="请选择招生类型">
            <el-option
              v-for="dict in enrollment_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否国际生" prop="isInternational">
          <el-select v-model="form.isInternational" placeholder="请选择是否国际生">
            <el-option
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否交换生" prop="isExchange">
          <el-radio-group v-model="form.isExchange">
            <el-radio
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮箱" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="紧急联系人" prop="emergencyContact">
          <el-input v-model="form.emergencyContact" placeholder="请输入紧急联系人" />
        </el-form-item>
        <el-form-item label="紧急联系电话" prop="emergencyPhone">
          <el-input v-model="form.emergencyPhone" placeholder="请输入紧急联系电话" />
        </el-form-item>
        <el-form-item label="当前住址" prop="currentAddress">
          <el-input v-model="form.currentAddress" placeholder="请输入当前住址" />
        </el-form-item>
        <el-form-item label="家庭住址" prop="homeAddress">
          <el-input v-model="form.homeAddress" placeholder="请输入家庭住址" />
        </el-form-item>
        <el-form-item label="邮政编码" prop="postalCode">
          <el-input v-model="form.postalCode" placeholder="请输入邮政编码" />
        </el-form-item>
        <el-form-item label="特殊需求" prop="specialRequirements">
          <el-input v-model="form.specialRequirements" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="最后登录时间" prop="lastLoginTime">
          <el-date-picker clearable
            v-model="form.lastLoginTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择最后登录时间">
          </el-date-picker>
        </el-form-item>

        
         
           
         

        <el-form-item label="是否激活" prop="isActive">
          <el-select v-model="form.isActive" placeholder="请输入是否激活">
            <el-option
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否删除" prop="isDeleted">
          <el-select v-model="form.isDeleted" placeholder="请输入是否删除">
            <el-option
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间" prop="createdAt">
          <el-date-picker clearable
            v-model="form.createdAt"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新时间" prop="updatedAt">
          <el-date-picker clearable
            v-model="form.updatedAt"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择更新时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Student">
import { listStudent, getStudent, delStudent, addStudent, updateStudent } from "@/api/system/student";

const { proxy } = getCurrentInstance();
const { nationality, political_landscape, sys_yes_no, gender, nation, student_status, education, enrollment_type } = proxy.useDict('nationality', 'political_landscape', 'sys_yes_no', 'gender', 'nation', 'student_status', 'education', 'enrollment_type');

const studentList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    studentId: null,
    username: null,
    fullName: null,
    englishName: null,
    gender: null,
    birthDate: null,
    idCard: null,
    nationality: null,
    ethnicity: null,
    politicalStatus: null,
    enrollmentDate: null,
    graduationDate: null,
    educationLevel: null,
    studentStatus: null,
    departmentCode: null,
    majorCode: null,
    majorDirectionCode: null,
    classId: null,
    currentGrade: null,
    admissionCategory: null,
    studentSource: null,
    enrollmentType: null,
    isInternational: null,
    isExchange: null,
    email: null,
    phone: null,
    emergencyContact: null,
    emergencyPhone: null,
    currentAddress: null,
    homeAddress: null,
    postalCode: null,
    specialRequirements: null,
    lastLoginTime: null,
    isActive: null,
    isDeleted: null,
    createdAt: null,
    updatedAt: null,
    remarks: null
  },
  rules: {
    studentId: [
      { required: true, message: "学号不能为空", trigger: "blur" }
    ],
    username: [
      { required: true, message: "用户名/登录名不能为空", trigger: "blur" }
    ],
    fullName: [
      { required: true, message: "学生姓名不能为空", trigger: "blur" }
    ],
    gender: [
      { required: true, message: "性别不能为空", trigger: "change" }
    ],
    enrollmentDate: [
      { required: true, message: "入学日期不能为空", trigger: "blur" }
    ],
    educationLevel: [
      { required: true, message: "学历层次不能为空", trigger: "change" }
    ],
    studentStatus: [
      { required: true, message: "学籍状态不能为空", trigger: "change" }
    ],
    departmentCode: [
      { required: true, message: "所属院系代码不能为空", trigger: "blur" }
    ],
    majorCode: [
      { required: true, message: "专业代码不能为空", trigger: "blur" }
    ],
    classId: [
      { required: true, message: "班级ID不能为空", trigger: "blur" }
    ],currentGrade: [
      { required: false, message: "当前年级不能为空", trigger: "blur" },
      { type: 'number', min: 1, max: 999, message: "当前年级必须是1-127之间的整数", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询学生信息列表 */
function getList() {
  loading.value = true;
  listStudent(queryParams.value).then(response => {
    studentList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    studentId: null,
    username: null,
    fullName: null,
    englishName: null,
    gender: null,
    birthDate: null,
    idCard: null,
    nationality: null,
    ethnicity: null,
    politicalStatus: null,
    enrollmentDate: null,
    graduationDate: null,
    educationLevel: null,
    studentStatus: null,
    departmentCode: null,
    majorCode: null,
    majorDirectionCode: null,
    classId: null,
    currentGrade: null,
    admissionCategory: null,
    studentSource: null,
    enrollmentType: null,
    isInternational: null,
    isExchange: null,
    email: null,
    phone: null,
    emergencyContact: null,
    emergencyPhone: null,
    currentAddress: null,
    homeAddress: null,
    postalCode: null,
    specialRequirements: null,
    lastLoginTime: null,
    isActive: null,
    isDeleted: null,
    createdAt: null,
    updatedAt: null,
    remarks: null
  };
  proxy.resetForm("studentRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.studentId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加学生信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _studentId = row.studentId || ids.value
  getStudent(_studentId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改学生信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["studentRef"].validate(valid => {
    if (valid) {
      if (form.value.studentId != null) {
        updateStudent(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addStudent(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _studentIds = row.studentId || ids.value;
  proxy.$modal.confirm('是否确认删除学生信息编号为"' + _studentIds + '"的数据项？').then(function() {
    return delStudent(_studentIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/student/export', {
    ...queryParams.value
  }, `student_${new Date().getTime()}.xlsx`)
}

getList();
</script>
