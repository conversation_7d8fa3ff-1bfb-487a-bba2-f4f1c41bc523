<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="专业方向编号" prop="majorDirectionCode">
        <el-input
          v-model="queryParams.majorDirectionCode"
          placeholder="请输入专业方向编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专业方向名称" prop="majorDirectionName">
        <el-input
          v-model="queryParams.majorDirectionName"
          placeholder="请输入专业方向名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专业名称" prop="majorName">
        <el-input
          v-model="queryParams.majorName"
          placeholder="请输入专业名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:directions:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:directions:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:directions:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:directions:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="directionsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="专业方向编号" align="center" prop="majorDirectionCode" />
      <el-table-column label="专业方向名称" align="center" prop="majorDirectionName" />
      <el-table-column label="年级" align="center" prop="year" />
      <el-table-column label="院系" align="center" prop="college" />
      <el-table-column label="专业编号" align="center" prop="majorCode" />
      <el-table-column label="专业名称" align="center" prop="majorName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:directions:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:directions:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改专业方向数据对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="directionsRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="专业方向名称" prop="majorDirectionName">
          <el-input v-model="form.majorDirectionName" placeholder="请输入专业方向名称" />
        </el-form-item>
        <el-form-item label="专业名称" prop="majorName">
          <el-input v-model="form.majorName" placeholder="请输入专业名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Directions">
import { listDirections, getDirections, delDirections, addDirections, updateDirections } from "@/api/system/directions";

const { proxy } = getCurrentInstance();

const directionsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    majorDirectionCode: null,
    majorDirectionName: null,
    year: null,
    college: null,
    majorCode: null,
    majorName: null
  },
  rules: {
    majorDirectionCode: [
      { required: true, message: "专业方向编号不能为空", trigger: "blur" }
    ],
    majorDirectionName: [
      { required: true, message: "专业方向名称不能为空", trigger: "blur" }
    ],
    year: [
      { required: true, message: "年级不能为空", trigger: "change" }
    ],
    college: [
      { required: true, message: "院系不能为空", trigger: "change" }
    ],
    majorCode: [
      { required: true, message: "专业编号不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询专业方向数据列表 */
function getList() {
  loading.value = true;
  listDirections(queryParams.value).then(response => {
    directionsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    majorDirectionCode: null,
    majorDirectionName: null,
    year: null,
    college: null,
    majorCode: null,
    majorName: null
  };
  proxy.resetForm("directionsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.majorDirectionCode);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加专业方向数据";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _majorDirectionCode = row.majorDirectionCode || ids.value
  getDirections(_majorDirectionCode).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改专业方向数据";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["directionsRef"].validate(valid => {
    if (valid) {
      if (form.value.majorDirectionCode != null) {
        updateDirections(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addDirections(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _majorDirectionCodes = row.majorDirectionCode || ids.value;
  proxy.$modal.confirm('是否确认删除专业方向数据编号为"' + _majorDirectionCodes + '"的数据项？').then(function() {
    return delDirections(_majorDirectionCodes);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/directions/export', {
    ...queryParams.value
  }, `directions_${new Date().getTime()}.xlsx`)
}

getList();
</script>
