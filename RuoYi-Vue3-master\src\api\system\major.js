import request from '@/utils/request'

// 查询专业信息列表
export function listMajor(query) {
  return request({
    url: '/system/major/list',
    method: 'get',
    params: query
  })
}

// 查询专业信息详细
export function getMajor(majorCode) {
  return request({
    url: '/system/major/' + majorCode,
    method: 'get'
  })
}

// 新增专业信息
export function addMajor(data) {
  return request({
    url: '/system/major',
    method: 'post',
    data: data
  })
}

// 修改专业信息
export function updateMajor(data) {
  return request({
    url: '/system/major',
    method: 'put',
    data: data
  })
}

// 删除专业信息
export function delMajor(majorCode) {
  return request({
    url: '/system/major/' + majorCode,
    method: 'delete'
  })
}
