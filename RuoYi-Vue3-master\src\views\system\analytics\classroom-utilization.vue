<!-- ClassroomUtilization.vue -->
<template>
  <div class="classroom-utilization-container">
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>教室利用率分析</span>
              <div class="filter-actions">
                <el-select v-model="filterOptions.building" placeholder="选择教学楼" clearable>
                  <el-option v-for="item in buildingOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="filterOptions.semester" placeholder="选择学期">
                  <el-option label="2024-2025学年第一学期" value="2024-1" />
                  <el-option label="2024-2025学年第二学期" value="2024-2" />
                </el-select>
                <el-button type="primary" @click="refreshData">刷新数据</el-button>
              </div>
            </div>
          </template>

          <!-- 图表控制区域 -->
          <el-radio-group v-model="chartType" size="large" style="margin-bottom: 20px;">
            <el-radio-button label="weekday">按星期分析</el-radio-button>
            <el-radio-button label="timeSlot">按时段分析</el-radio-button>
            <el-radio-button label="building">按楼栋分析</el-radio-button>
            <el-radio-button label="type">按类型分析</el-radio-button>
          </el-radio-group>

          <!-- 图表区域 -->
          <div class="chart-container">
            <div ref="chartRef" style="width: 100%; height: 100%;"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card shadow="hover" class="data-table-card">
      <template #header>
        <div class="card-header">
          <span>详细数据</span>
          <div class="table-actions">
            <el-button type="primary" @click="exportData">导出数据</el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="使用率总览" name="overview">
          <el-table :data="utilizationData" border style="width: 100%">
            <el-table-column type="index" width="50" />
            <el-table-column prop="name" label="教室名称" width="120" />
            <el-table-column prop="building" label="所属楼栋" width="120" />
            <el-table-column prop="type" label="教室类型" width="120" />
            <el-table-column prop="capacity" label="容量" width="80" />
            <el-table-column prop="utilizationRate" label="使用率" width="180">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.utilizationRate"
                  :color="getUtilizationColor(scope.row.utilizationRate)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="scheduledHours" label="已排课时数" width="100" />
            <el-table-column prop="totalHours" label="总课时数" width="100" />
            <el-table-column prop="courseCount" label="课程数" width="80" />
            <el-table-column label="详情" width="100">
              <template #default="scope">
                <el-button size="small" @click="viewDetail(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="时段分析" name="timeSlot">
          <el-table :data="timeSlotData" border style="width: 100%">
            <el-table-column type="index" width="50" />
            <el-table-column prop="timeSlot" label="时间段" width="120" />
            <el-table-column prop="monday" label="周一" width="80" />
            <el-table-column prop="tuesday" label="周二" width="80" />
            <el-table-column prop="wednesday" label="周三" width="80" />
            <el-table-column prop="thursday" label="周四" width="80" />
            <el-table-column prop="friday" label="周五" width="80" />
            <el-table-column prop="average" label="平均使用率" width="180">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.average"
                  :color="getUtilizationColor(scope.row.average)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="peak" label="高峰日" width="100" />
            <el-table-column prop="recommendation" label="建议" />
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="楼栋分析" name="building">
          <el-table :data="buildingData" border style="width: 100%">
            <el-table-column type="index" width="50" />
            <el-table-column prop="building" label="教学楼" width="120" />
            <el-table-column prop="totalRooms" label="教室总数" width="100" />
            <el-table-column prop="utilizationRate" label="平均使用率" width="180">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.utilizationRate"
                  :color="getUtilizationColor(scope.row.utilizationRate)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="peakTime" label="使用高峰" width="120" />
            <el-table-column prop="idleTime" label="空闲时段" width="120" />
            <el-table-column prop="recommendation" label="优化建议" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="教室利用详情"
      width="80%"
    >
      <template v-if="selectedRoom">
        <el-descriptions title="基本信息" :column="3" border>
          <el-descriptions-item label="教室名称">{{ selectedRoom.name }}</el-descriptions-item>
          <el-descriptions-item label="所属楼栋">{{ selectedRoom.building }}</el-descriptions-item>
          <el-descriptions-item label="教室类型">{{ selectedRoom.type }}</el-descriptions-item>
          <el-descriptions-item label="容量">{{ selectedRoom.capacity }}人</el-descriptions-item>
          <el-descriptions-item label="使用率">{{ selectedRoom.utilizationRate }}%</el-descriptions-item>
          <el-descriptions-item label="已排课时数">{{ selectedRoom.scheduledHours }}课时</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="center">使用时段分析</el-divider>

        <div class="detail-charts">
          <el-row :gutter="20">
            <el-col :span="12">
              <h4>周使用率分布</h4>
              <div class="chart-placeholder">周使用率图表</div>
            </el-col>
            <el-col :span="12">
              <h4>日内使用率分布</h4>
              <div class="chart-placeholder">日内使用率图表</div>
            </el-col>
          </el-row>
        </div>

        <el-divider content-position="center">使用详情</el-divider>

        <el-table :data="selectedRoomSchedule" border style="width: 100%">
          <el-table-column prop="day" label="星期" width="80" />
          <el-table-column prop="timeSlot" label="时间段" width="100" />
          <el-table-column prop="courseName" label="课程名称" width="150" />
          <el-table-column prop="teacher" label="教师" width="100" />
          <el-table-column prop="class" label="班级" width="150" />
          <el-table-column prop="weeks" label="周次" />
        </el-table>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 图表类型
const chartType = ref('weekday')
const activeTab = ref('overview')

// 过滤条件
const filterOptions = reactive({
  building: '',
  semester: '2024-1'
})

// 所属楼栋选项
const buildingOptions = [
  { value: 'teaching1', label: '第一教学楼' },
  { value: 'teaching2', label: '第二教学楼' },
  { value: 'teaching3', label: '第三教学楼' },
  { value: 'science', label: '理科大楼' },
  { value: 'arts', label: '文科大楼' },
  { value: 'lab', label: '实验大楼' }
]

// 模拟数据 - 教室使用率
const utilizationData = ref([
  {
    id: '101',
    name: '一教101',
    building: '第一教学楼',
    type: '多媒体教室',
    capacity: 120,
    utilizationRate: 85,
    scheduledHours: 34,
    totalHours: 40,
    courseCount: 6
  },
  {
    id: '102',
    name: '一教102',
    building: '第一教学楼',
    type: '普通教室',
    capacity: 60,
    utilizationRate: 70,
    scheduledHours: 28,
    totalHours: 40,
    courseCount: 5
  },
  {
    id: '201',
    name: '二教201',
    building: '第二教学楼',
    type: '实验室',
    capacity: 30,
    utilizationRate: 60,
    scheduledHours: 24,
    totalHours: 40,
    courseCount: 4
  },
  {
    id: '202',
    name: '二教202',
    building: '第二教学楼',
    type: '阶梯教室',
    capacity: 150,
    utilizationRate: 75,
    scheduledHours: 30,
    totalHours: 40,
    courseCount: 5
  },
  {
    id: '301',
    name: '理科301',
    building: '理科大楼',
    type: '多媒体教室',
    capacity: 90,
    utilizationRate: 92,
    scheduledHours: 37,
    totalHours: 40,
    courseCount: 7
  },
  {
    id: '302',
    name: '理科302',
    building: '理科大楼',
    type: '实验室',
    capacity: 40,
    utilizationRate: 45,
    scheduledHours: 18,
    totalHours: 40,
    courseCount: 3
  }
])

// 模拟数据 - 时段分析
const timeSlotData = ref([
  {
    timeSlot: '1-2节',
    monday: 85,
    tuesday: 92,
    wednesday: 78,
    thursday: 90,
    friday: 65,
    average: 82,
    peak: '周二',
    recommendation: '周五可增加课程安排'
  },
  {
    timeSlot: '3-4节',
    monday: 95,
    tuesday: 88,
    wednesday: 90,
    thursday: 85,
    friday: 92,
    average: 90,
    peak: '周一',
    recommendation: '已接近饱和，建议避免新增课程'
  },
  {
    timeSlot: '5-6节',
    monday: 75,
    tuesday: 80,
    wednesday: 70,
    thursday: 82,
    friday: 60,
    average: 73,
    peak: '周四',
    recommendation: '周五有较多空闲教室'
  },
  {
    timeSlot: '7-8节',
    monday: 60,
    tuesday: 65,
    wednesday: 70,
    thursday: 55,
    friday: 40,
    average: 58,
    peak: '周三',
    recommendation: '下午课程安排有较大空间'
  },
  {
    timeSlot: '9-10节',
    monday: 30,
    tuesday: 35,
    wednesday: 40,
    thursday: 45,
    friday: 20,
    average: 34,
    peak: '周四',
    recommendation: '晚上时间段利用率低，可考虑调整'
  }
])

// 模拟数据 - 楼栋分析
const buildingData = ref([
  {
    building: '第一教学楼',
    totalRooms: 42,
    utilizationRate: 78,
    peakTime: '周二3-4节',
    idleTime: '周五9-10节',
    recommendation: '晚上时段使用率较低，可考虑减少晚上安排'
  },
  {
    building: '第二教学楼',
    totalRooms: 38,
    utilizationRate: 82,
    peakTime: '周一3-4节',
    idleTime: '周五9-10节',
    recommendation: '部分小教室使用率低，可考虑合班授课'
  },
  {
    building: '理科大楼',
    totalRooms: 26,
    utilizationRate: 75,
    peakTime: '周三5-6节',
    idleTime: '周五7-8节',
    recommendation: '优先安排理科课程，避免学生往返校区'
  },
  {
    building: '文科大楼',
    totalRooms: 20,
    utilizationRate: 68,
    peakTime: '周四1-2节',
    idleTime: '周五9-10节',
    recommendation: '可增加部分课程安排，利用率有提升空间'
  },
  {
    building: '实验大楼',
    totalRooms: 30,
    utilizationRate: 55,
    peakTime: '周二5-6节',
    idleTime: '周一7-8节',
    recommendation: '使用率较低，建议优化实验课程安排'
  }
])

// 教室详情相关
const detailDialogVisible = ref(false)
const selectedRoom = ref(null)
const selectedRoomSchedule = ref([
  {
    day: '周一',
    timeSlot: '1-2节',
    courseName: '高等数学',
    teacher: '张三',
    class: '计算机2101班',
    weeks: '1-16周'
  },
  {
    day: '周三',
    timeSlot: '3-4节',
    courseName: '线性代数',
    teacher: '李四',
    class: '计算机2101班',
    weeks: '1-16周'
  },
  {
    day: '周四',
    timeSlot: '5-6节',
    courseName: '大学物理',
    teacher: '王五',
    class: '计算机2101班',
    weeks: '1-16周'
  },
  {
    day: '周五',
    timeSlot: '1-2节',
    courseName: '程序设计',
    teacher: '赵六',
    class: '计算机2101班',
    weeks: '1-16周'
  },
  {
    day: '周二',
    timeSlot: '7-8节',
    courseName: '大学英语',
    teacher: '钱七',
    class: '计算机2101班',
    weeks: '1-16周'
  }
])

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 图表数据
const chartData = reactive({
  weekday: {
    xAxis: ['周一', '周二', '周三', '周四', '周五'],
    data: [75, 82, 78, 85, 70]
  },
  timeSlot: {
    xAxis: ['1-2节', '3-4节', '5-6节', '7-8节', '9-10节'],
    data: [82, 90, 73, 58, 34]
  },
  building: {
    xAxis: ['第一教学楼', '第二教学楼', '理科大楼', '文科大楼', '实验大楼'],
    data: [78, 82, 75, 68, 55]
  },
  type: {
    xAxis: ['多媒体教室', '普通教室', '实验室', '阶梯教室', '语音室'],
    data: [85, 75, 60, 80, 70]
  }
})

// 初始化图表
const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  chartInstance = echarts.init(chartRef.value)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) {
    initChart()
  }

  const currentData = chartData[chartType.value]
  const option = {
    title: {
      text: getChartTitle(),
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}%'
    },
    xAxis: {
      type: 'category',
      data: currentData.xAxis,
      axisLabel: {
        interval: 0,
        rotate: chartType.value === 'building' ? 30 : 0
      }
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        type: 'bar',
        data: currentData.data,
        itemStyle: {
          color: function(params) {
            return getUtilizationColor(params.value)
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%'
        }
      }
    ],
    grid: {
      containLabel: true,
      left: '3%',
      right: '4%',
      bottom: '3%'
    }
  }

  chartInstance.setOption(option)
}

// 获取图表标题
const getChartTitle = () => {
  const titles = {
    weekday: '教室周使用率分布',
    timeSlot: '教室时段使用率分布',
    building: '教室楼栋使用率分布',
    type: '教室类型使用率分布'
  }
  return titles[chartType.value]
}

// 监听图表类型变化
watch(chartType, () => {
  updateChart()
})

// 监听窗口大小变化
window.addEventListener('resize', () => {
  chartInstance?.resize()
})

// 方法：根据使用率获取对应的颜色
const getUtilizationColor = (percentage) => {
  if (percentage < 30) return '#909399'
  if (percentage < 70) return '#409eff'
  if (percentage < 90) return '#e6a23c'
  return '#f56c6c'
}

// 方法：刷新数据
const refreshData = () => {
  ElMessage.success('数据已刷新')
  // 实际应用中应该从后端获取数据
}

// 方法：查看详情
const viewDetail = (row) => {
  selectedRoom.value = row
  detailDialogVisible.value = true
}

// 方法：导出数据
const exportData = () => {
  ElMessage.success('数据导出成功')
  // 实际应用中应该调用导出API
}

// 生命周期钩子
onMounted(() => {
  initChart()
  updateChart()
})
</script>

<style scoped>
.classroom-utilization-container {
  padding: 20px;
}

.chart-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.chart-container {
  height: 400px;
  margin-top: 20px;
  background-color: #fff;
}

.data-table-card {
  margin-bottom: 20px;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.chart-placeholder {
  width: 100%;
  height: 300px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  font-style: italic;
}

.detail-charts {
  margin: 20px 0;
}
</style>
