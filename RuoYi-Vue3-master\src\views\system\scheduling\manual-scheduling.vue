<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, ArrowDown, Plus } from '@element-plus/icons-vue'  // 添加图标导入

// 导入类型定义
import type { Course, ClassRoom, TimeSlot, ScheduleItem, Class } from '@/types/scheduling'

// 导入排课相关的API接口
import { useSchedulingApi } from '@/api/system/scheduling'

// TODO: 后期改为接口
const schedulingApi = useSchedulingApi()
// 课程列表
const courses = ref<Course[]>([])
// 教室列表
const classrooms = ref<ClassRoom[]>([])
// 时间槽
const timeSlots = ref<TimeSlot[]>([])
// 已排课程
const scheduledCourses = ref<ScheduleItem[]>([])
// 当前选中的周次
const currentWeek = ref(1)
// 可选的周次列表
const weekList = ref<{ id: number, name: string }[]>([])
// 添加班级和教室筛选
const selectedClass = ref<string>('')
// 添加教室筛选
const selectedClassroom = ref<number>(1)
// 添加班级筛选
const classes = ref<Class[]>([
  {
    id: '1',
    name: '1班',
    grade: '一年级',
    majorName: '计算机科学与技术',
    studentCount: 30
  },
  {
    id: '2',
    name: '2班',
    grade: '二年级',
    majorName: '软件工程',
    studentCount: 25
  },
  {
    id: 3,
    name: '3班',
    grade: '三年级',
    majorName: '网络工程',
    studentCount: 35
  }
])
// 当前拖拽的课程
const draggingCourse = ref<Course | null>(null)
// 显示冲突提示
const showConflictTip = ref(false)
// 冲突信息
const conflictInfo = ref('')
// 拖拽状态
const isDragging = ref(false)
// 当前排课模式（教室/班级）
const schedulingMode = ref<'classroom' | 'class'>('classroom')

// 添加星期常量
const WEEKDAYS = [
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' }
]
const mockCourses: Course[] = [
  {
    id: 1,
    name: '高等数学',
    teacher: '张教授',
    hours: 48,
    courseType: '必修',
    studentCount: 60,
    classId: '1',
    classroomId: '1',
    timeSlotId: '1'
  },
  {
    id: 2,
    name: '大学物理',
    teacher: '李教授',
    hours: 32,
    courseType: '必修',
    studentCount: 45,
    classId: '1',
    classroomId: '2',
    timeSlotId: '2'
  },
  {
    id: 3,
    name: '计算机基础',
    teacher: '王教授',
    hours: 64,
    courseType: '必修',
    studentCount: 50,
    classId: '2',
    classroomId: '3',
    timeSlotId: '3'
  },
  {
    id: 4,
    name: '英语口语',
    teacher: '史密斯',
    hours: 32,
    courseType: '选修',
    studentCount: 30,
    classId: '2',
    classroomId: '4',
    timeSlotId: '4'
  },
  {
    id: 5,
    name: '数据结构',
    teacher: '刘教授',
    hours: 48,
    courseType: '必修',
    studentCount: 55,
    classId: '3',
    classroomId: '1',
    timeSlotId: '5'
  }
]

// 模拟教室数据
const mockClassrooms: ClassRoom[] = [
  {
    id: 1,
    name: '教101',
    capacity: 80,
    building: '第一教学楼',
    floor: '1',
    equipment: ['多媒体', '空调'],
    classId: '1'
  },
  {
    id: 2,
    name: '教102',
    capacity: 60,
    building: '第一教学楼',
    floor: '1',
    equipment: ['多媒体', '空调'],
    classId: '1'
  },
  {
    id: 3,
    name: '教201',
    capacity: 120,
    building: '第一教学楼',
    floor: '2',
    equipment: ['多媒体', '空调', '电脑'],
    classId: '2'
  },
  {
    id: 4,
    name: '实验室301',
    capacity: 50,
    building: '第一教学楼',
    floor: '3',
    equipment: ['多媒体', '空调', '电脑', '实验设备'],
    classId: '3'
  }
]

// 模拟时间槽数据
const mockTimeSlots: any[] = [
  { id: 1, time: '8:00-9:40', period: 1, dayOfWeek: 1 },
  { id: 2, time: '10:00-11:40', period: 2, dayOfWeek: 1 },
  { id: 3, time: '14:00-15:40', period: 3, dayOfWeek: 1 },
  { id: 4, time: '16:00-17:40', period: 4, dayOfWeek: 1 },
  { id: 5, time: '19:00-20:40', period: 5, dayOfWeek: 1 }
]

// 模拟已排课程数据
const mockScheduledCourses: any[] = [
  {
    id: 1,
    courseId: 1,
    classroomId: 1,
    timeSlotId: 1,
    week: 1,
    course: mockCourses[0],
    classroom: mockClassrooms[0],
    timeSlot: mockTimeSlots[0]
  },
  {
    id: 2,
    courseId: 2,
    classroomId: 2,
    timeSlotId: 2,
    week: 1,
    course: mockCourses[1],
    classroom: mockClassrooms[1],
    timeSlot: mockTimeSlots[1]
  }
]

// 模拟班级数据
const mockClasses: any[] = [
  {
    id: 1,
    name: '1班',
    grade: '一年级',
    majorName: '计算机科学与技术',
    studentCount: 30
  },
  {
    id: 2,
    name: '2班',
    grade: '二年级',
    majorName: '软件工程',
    studentCount: 25
  },
  {
    id: 3,
    name: '3班',
    grade: '三年级',
    majorName: '网络工程',
    studentCount: 35
  }
]

// 添加临时排课数组
const tempScheduledCourses = ref<ScheduleItem[]>([])
// 添加是否有未保存的更改标志
const hasUnsavedChanges = ref(false)

// 添加冲突检测结果
const conflicts = ref<any[]>([])
// 添加高级搜索条件
const searchCriteria = ref({
  courseName: '',
  teacherName: '',
  buildingName: ''
})
// 添加导出排课表的格式选项
const exportFormat = ref('xlsx')
// 添加排课统计信息
const schedulingStats = ref({
  totalCourses: 0,
  scheduledCourses: 0,
  completionRate: '0%'
})
// 批量操作模式
const batchMode = ref(false)
// 选中的排课项
const selectedScheduleItems = ref<ScheduleItem[]>([])
// 添加高亮显示的单元格位置
const highlightCell = ref({
  timeSlotId: 0,
  weekday: 0
})

// 获取课程列表
const fetchCourses = async () => {
  try {
    courses.value = await schedulingApi.getCourses()
    // 更新统计信息
    updateSchedulingStats()
  } catch (error) {
    ElMessage.error('获取课程列表失败')
    // 使用mock数据
    courses.value = mockCourses
  }
}

// 获取教室列表
const fetchClassrooms = async () => {
  try {
    classrooms.value = await schedulingApi.getClassrooms()
  } catch (error) {
    ElMessage.error('获取教室列表失败')
    // 使用mock数据
    classrooms.value = mockClassrooms
  }
}

// 获取时间槽
const fetchTimeSlots = async () => {
  try {
    timeSlots.value = await schedulingApi.getTimeSlots()
  } catch (error) {
    ElMessage.error('获取时间槽失败')
    // 使用mock数据
    timeSlots.value = mockTimeSlots
  }
}

// 获取已排课程
const fetchScheduledCourses = async () => {
  try {
    scheduledCourses.value = await schedulingApi.getScheduledCourses(currentWeek.value)
    // 更新统计信息
    updateSchedulingStats()
  } catch (error) {
    ElMessage.error('获取已排课程失败')
    // 使用mock数据
    scheduledCourses.value = mockScheduledCourses
  }
}

// 获取班级列表
const fetchClasses = async () => {
  try {
    classes.value = await schedulingApi.getClasses()
  } catch (error) {
    ElMessage.error('获取班级列表失败')
    // 使用mock数据
    classes.value = mockClasses
  }
}

// 获取周次列表
const fetchWeekList = async () => {
  try {
    weekList.value = await schedulingApi.getWeekList()
  } catch (error) {
    ElMessage.error('获取周次列表失败')
    // 使用mock数据
    weekList.value = Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      name: `第${i + 1}周`
    }))
  }
}

// 获取冲突检测结果
const fetchConflicts = async () => {
  try {
    conflicts.value = await schedulingApi.getConflicts(currentWeek.value)
    if (conflicts.value.length > 0) {
      ElMessage.warning(`检测到${conflicts.value.length}个排课冲突，请检查!`)
    }
  } catch (error) {
    ElMessage.error('获取冲突检测结果失败')
  }
}

// 添加排课统计信息更新方法
const updateSchedulingStats = () => {
  const total = courses.value.length
  const scheduled = new Set(scheduledCourses.value.map(item => item.courseId)).size
  schedulingStats.value = {
    totalCourses: total,
    scheduledCourses: scheduled,
    completionRate: total > 0 ? `${Math.round((scheduled / total) * 100)}%` : '0%'
  }
}

// 添加导出排课表方法
const exportScheduleTable = async () => {
  try {
    const blob = await schedulingApi.exportSchedule(currentWeek.value, exportFormat.value)
    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.href = url
    link.download = `排课表_第${currentWeek.value}周.${exportFormat.value}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 添加切换批量模式方法
const toggleBatchMode = () => {
  batchMode.value = !batchMode.value
  if (!batchMode.value) {
    selectedScheduleItems.value = []
  }
}

// 添加选中排课项方法
const toggleSelectScheduleItem = (item: ScheduleItem) => {
  if (!batchMode.value) return

  const index = selectedScheduleItems.value.findIndex(
      selected =>
          selected.classroomId === item.classroomId &&
          selected.timeSlotId === item.timeSlotId &&
          selected.week === item.week &&
          selected.weekday === item.weekday
  )

  if (index === -1) {
    selectedScheduleItems.value.push(item)
  } else {
    selectedScheduleItems.value.splice(index, 1)
  }
}

// 添加批量删除方法
const batchDeleteScheduleItems = async () => {
  if (selectedScheduleItems.value.length === 0) {
    ElMessage.warning('请先选择要删除的课程')
    return
  }

  try {
    for (const item of selectedScheduleItems.value) {
      // 从本地状态中移除课程
      scheduledCourses.value = scheduledCourses.value.filter(
          (sch) =>
              !(sch.classroomId === item.classroomId &&
                  sch.timeSlotId === item.timeSlotId &&
                  sch.week === item.week &&
                  sch.weekday === item.weekday)
      )

      // 从临时数组中也移除
      tempScheduledCourses.value = tempScheduledCourses.value.filter(
          (sch) =>
              !(sch.classroomId === item.classroomId &&
                  sch.timeSlotId === item.timeSlotId &&
                  sch.week === item.week &&
                  sch.weekday === item.weekday)
      )
    }

    ElMessage.success(`已移除${selectedScheduleItems.value.length}个课程`)
    selectedScheduleItems.value = []
    hasUnsavedChanges.value = true
  } catch (error) {
    ElMessage.error('批量删除失败')
  }
}

// 添加高级筛选方法
const applyAdvancedFilter = () => {
  // 实现高级筛选逻辑
  if (searchCriteria.value.courseName || searchCriteria.value.teacherName) {
    ElMessage.success('已应用高级筛选')
  }
}

// 添加单元格高亮方法
const highlightScheduleCell = (timeSlotId: number, weekday: number) => {
  highlightCell.value = {
    timeSlotId,
    weekday
  }
}

// 添加课程冲突的视觉提示
const isCellConflicted = (classroomId: number, timeSlotId: number, weekday: number): boolean => {
  return conflicts.value.some(
      conflict =>
          conflict.classroomId === classroomId &&
          conflict.timeSlotId === timeSlotId &&
          conflict.weekday === weekday
  )
}

// 点击单元格的处理方法
const handleCellClick = (timeSlot: TimeSlot, weekday: number) => {
  highlightScheduleCell(timeSlot.id, weekday)
}

// 切换排课模式
const toggleSchedulingMode = () => {
  // 切换模式
  schedulingMode.value = schedulingMode.value === 'classroom' ? 'class' : 'classroom'
  // 清空选择
  selectedClassroom.value = 1
  selectedClass.value = ''
  // 重新获取课程数据
  fetchScheduledCourses()
}

// 过滤显示的课程
const filteredCourses = computed(() => {
  if (schedulingMode.value === 'classroom') {
    // 教室模式下显示所有课程
    return courses.value
  } else {
    // 班级模式下只显示选中班级的课程
    if (!selectedClass.value) return []
    return courses.value.filter((course: Course) => course.classId === selectedClass.value)
  }
})

// 获取当前选中的教室信息
const selectedClassroomInfo = computed(() => {
  if (!selectedClassroom.value) return null
  return classrooms.value.find((classroom: ClassRoom) => classroom.id === selectedClassroom.value)
})

// 获取当前选中的班级信息
const selectedClassInfo = computed(() => {
  if (!selectedClass.value) return null
  return classes.value.find((cls: Class) => cls.id === selectedClass.value)
})

// 获取指定位置的已排课程
const getScheduledCourse = computed(() => {
  return (classroomId: number, timeSlotId: number, weekday: number): Course | undefined => {
    const scheduledItem = scheduledCourses.value.find(
        (item: ScheduleItem) =>
            item.classroomId === classroomId &&
            item.timeSlotId === timeSlotId &&
            item.week === currentWeek.value &&
            item.weekday === weekday
    )
    return scheduledItem?.course
  }
})

// 检查课程冲突
const checkConflict = (course: Course, classroom: ClassRoom, timeSlot: TimeSlot, weekday: number): boolean => {
  // 检查教室容量
  if (course.studentCount > classroom.capacity) {
    conflictInfo.value = '教室容量不足'
    return true
  }

  // 检查时间冲突
  const existingCourse = getScheduledCourse.value(classroom.id, timeSlot.id, weekday)
  if (existingCourse) {
    conflictInfo.value = `该时间段已安排${existingCourse.name}课程`
    return true
  }

  // 检查班级在该时间段是否已有其他课程
  const classHasOtherCourse = scheduledCourses.value.some(
      (item: ScheduleItem) =>
          item.course.classId === course.classId &&
          item.timeSlotId === timeSlot.id &&
          item.week === currentWeek.value &&
          item.weekday === weekday
  )

  if (classHasOtherCourse) {
    conflictInfo.value = '该班级在此时间段已有其他课程'
    return true
  }

  return false
}

// 处理课程拖拽开始
const handleDragStart = (course: Course, ev: DragEvent) => {
  draggingCourse.value = course
  isDragging.value = true
  console.log('开始拖拽课程:', course.name)
}

// 处理拖拽进入目标区域
const handleDragEnter = (ev: DragEvent, timeSlot: TimeSlot) => {
  if (!draggingCourse.value || !selectedClassroomInfo.value) return
  // 仅添加视觉反馈，不做冲突检查
}

// 处理拖拽离开目标区域
const handleDragLeave = () => {
  // 不再需要处理冲突提示
}

// 处理拖拽结束
const handleDragEnd = () => {
  isDragging.value = false
  draggingCourse.value = null
  showConflictTip.value = false
}

// 处理课程拖放
const handleDrop = async (timeSlot: TimeSlot, weekday: number) => {
  if (!draggingCourse.value || !selectedClassroomInfo.value) return

  // 检查冲突
  if (checkConflict(draggingCourse.value, selectedClassroomInfo.value, timeSlot, weekday)) {
    showConflictTip.value = true
    return
  }

  // 创建新的排课项
  const newScheduleItem: ScheduleItem = {
    courseId: draggingCourse.value.id,
    classroomId: selectedClassroomInfo.value.id,
    timeSlotId: timeSlot.id,
    week: currentWeek.value,
    weekday,
    course: draggingCourse.value
  }

  // 添加到临时数组
  tempScheduledCourses.value.push(newScheduleItem)
  // 更新本地显示的排课数据
  scheduledCourses.value.push(newScheduleItem)

  hasUnsavedChanges.value = true
  ElMessage.success('课程已添加到临时排课表')
  draggingCourse.value = null
  showConflictTip.value = false
}

// 添加批量保存功能
const saveAllSchedules = async () => {
  if (!tempScheduledCourses.value.length) {
    ElMessage.warning('没有需要保存的排课数据')
    return
  }

  try {
    // 调用后端批量保存接口
    await schedulingApi.saveScheduleBatch(tempScheduledCourses.value)

    ElMessage.success('排课保存成功')
    // 清空临时数组
    tempScheduledCourses.value = []
    hasUnsavedChanges.value = false
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  }
}

// 修改删除课程的逻辑
const deleteScheduledCourse = async (classroomId: number, timeSlotId: number, weekday: number) => {
  // 从本地状态中移除课程
  scheduledCourses.value = scheduledCourses.value.filter(
      (item: ScheduleItem) =>
          !(item.classroomId === classroomId &&
              item.timeSlotId === timeSlotId &&
              item.week === currentWeek.value &&
              item.weekday === weekday)
  )

  // 从临时数组中也移除
  tempScheduledCourses.value = tempScheduledCourses.value.filter(
      (item: ScheduleItem) =>
          !(item.classroomId === classroomId &&
              item.timeSlotId === timeSlotId &&
              item.week === currentWeek.value &&
              item.weekday === weekday)
  )

  hasUnsavedChanges.value = true
  ElMessage.success('课程已移除')
}

// 获取智能建议
const getSmartSuggestions = async () => {
  try {
    const suggestions = await schedulingApi.getSmartSuggestions(currentWeek.value)
    // 处理建议结果
    ElMessage.success('已获取智能建议')
  } catch (error) {
    ElMessage.error('获取智能建议失败')
  }
}

// 修改监听周次变化
watch(currentWeek, async () => {
  await fetchScheduledCourses()
  await fetchConflicts()
})

// 挂载
onMounted(async () => {
  await Promise.all([
    fetchCourses(),
    fetchClassrooms(),
    fetchTimeSlots(),
    fetchScheduledCourses(),
    fetchClasses(),
    fetchWeekList(),
    fetchConflicts()
  ])
})
</script>

<template>
  <div class="manual-scheduling-container">
    <el-container>
      <!-- 左侧课程列表 -->
      <el-aside width="300px">
        <el-card class="course-list">
          <template #header>
            <div class="card-header">
              <span>待排课程</span>
              <el-space>
                <el-button
                    type="primary"
                    size="small"
                    @click="getSmartSuggestions"
                >
                  智能建议
                </el-button>
              </el-space>
            </div>
          </template>

          <!-- 添加高级搜索折叠面板 -->
          <el-collapse>
            <el-collapse-item
                title="高级搜索"
                name="1"
            >
              <el-form
                  :model="searchCriteria"
                  label-width="80px"
                  size="small"
              >
                <el-form-item label="课程名称">
                  <el-input
                      v-model="searchCriteria.courseName"
                      placeholder="输入课程名称"
                  ></el-input>
                </el-form-item>
                <el-form-item label="教师">
                  <el-input
                      v-model="searchCriteria.teacherName"
                      placeholder="输入教师姓名"
                  ></el-input>
                </el-form-item>
                <el-form-item label="教学楼">
                  <el-input
                      v-model="searchCriteria.buildingName"
                      placeholder="输入教学楼"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button
                      size="small"
                      style="margin-right: 10px; float: right;"
                      type="primary"
                      @click="applyAdvancedFilter"
                  >应用筛选</el-button>
                  <el-button
                      size="small"
                      style="float: right;"
                      @click="searchCriteria = { courseName: '', teacherName: '', buildingName: '' }"
                  >重置</el-button>
                </el-form-item>
              </el-form>
            </el-collapse-item>
          </el-collapse>

          <!-- 课程列表 -->
          <el-scrollbar height="calc(100vh - 350px)">
            <div
                v-for="course in filteredCourses"
                :key="course.id"
                class="course-item"
                draggable="true"
                @dragstart="handleDragStart(course, $event)"
                @dragend="handleDragEnd"
            >
              <el-card
                  shadow="hover"
                  :class="{ 'is-dragging': isDragging && draggingCourse?.id === course.id }"
                  class="course-card"
              >
                <div class="course-header">
                  <h4 class="course-title">{{ course.name }}</h4>
                  <p class="course-type-tag">{{ course.courseType }}</p>
                </div>
                <p class="teacher">教师：{{ course.teacher }}</p>
                <p class="course-details">
                  <span class="time">学时：{{ course.hours }}</span>　
                  <span class="number">人数：{{ course.studentCount }}</span>
                </p>
              </el-card>
            </div>
          </el-scrollbar>
        </el-card>
      </el-aside>

      <!-- 右侧排课表格 -->
      <el-main>
        <div class="schedule-header">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-space wrap>
                <!-- 教室选择 -->
                <template v-if="schedulingMode === 'classroom'">
                  <el-select
                      v-model="selectedClassroom"
                      clearable
                      placeholder="选择教室"
                      style="width: 200px;"
                  >
                    <el-option
                        v-for="classroom in classrooms"
                        :key="classroom.id"
                        :label="classroom.name"
                        :value="classroom.id"
                    >
                      <div class="classroom-option">
                        <span>{{ classroom.name }}</span>
                        <span class="classroom-option-info">
                                                    容量: {{ classroom.capacity }} | {{ classroom.building }}
                                                </span>
                      </div>
                    </el-option>
                  </el-select>
                </template>

                <!-- 班级选择 -->
                <template v-else>
                  <el-select
                      v-model="selectedClass"
                      clearable
                      placeholder="选择班级"
                      style="width: 200px;"
                  >
                    <el-option
                        v-for="cls in classes"
                        :key="cls.id"
                        :label="cls.name"
                        :value="cls.id"
                    />
                  </el-select>
                </template>

                <!-- 切换按钮 -->
                <el-button
                    type="primary"
                    @click="toggleSchedulingMode"
                >
                  切换为{{ schedulingMode === 'classroom' ? '班级' : '教室' }}排课
                </el-button>

                <!-- 添加保存按钮 -->
                <el-button
                    type="success"
                    :disabled="!hasUnsavedChanges"
                    @click="saveAllSchedules"
                >
                  保存排课
                  <template v-if="tempScheduledCourses.length">
                    ({{ tempScheduledCourses.length }})
                  </template>
                </el-button>

                <!-- 添加批量操作按钮 -->
                <template v-if="batchMode">
                  <el-button
                      type="danger"
                      @click="batchDeleteScheduleItems"
                  >
                    批量删除 ({{ selectedScheduleItems.length }})
                  </el-button>
                </template>

                <!-- 添加导出按钮 -->
                <el-dropdown @command="exportScheduleTable">
                  <el-button type="info">
                    导出排课表
                    <el-icon class="el-icon--right">
                      <arrow-down />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="xlsx">Excel格式(xlsx)</el-dropdown-item>
                      <el-dropdown-item command="pdf">PDF格式</el-dropdown-item>
                      <el-dropdown-item command="csv">CSV格式</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-space>
            </el-col>
          </el-row>
          <!-- 添加未保存提示 -->
          <div
              v-if="hasUnsavedChanges"
              class="unsaved-tip"
          >
            <el-alert
                title="有未保存的排课更改"
                type="warning"
                :closable="false"
                show-icon
            />
          </div>
        </div>

        <!-- 没有选择教室或班级时显示 -->
        <el-empty
            v-if="(schedulingMode === 'classroom' && !selectedClassroom) || (schedulingMode === 'class' && !selectedClass)"
            :description="schedulingMode === 'classroom' ? '请先选择教室' : '请先选择班级'"
        />

        <template v-else>
          <!-- 教室信息展示 -->
          <div
              v-if="schedulingMode === 'classroom'"
              class="classroom-info-header"
          >
            <!-- <h3>{{ selectedClassroomInfo?.name }}</h3> -->
            <div class="classroom-detail">
              <span>容量: {{ selectedClassroomInfo?.capacity }}人</span>
              <span>位置: {{ selectedClassroomInfo?.building }} {{ selectedClassroomInfo?.floor }}层</span>
              <span>设备: {{ selectedClassroomInfo?.equipment.join(', ') }}</span>
            </div>
          </div>

          <!-- 班级信息展示 -->
          <div
              v-else
              class="class-info-header"
          >
            <template v-if="selectedClassInfo">
              <h3>{{ selectedClassInfo.name }}</h3>
              <div class="class-detail">
                <span>年级: {{ selectedClassInfo.grade }}</span>
                <span>专业: {{ selectedClassInfo.majorName }}</span>
                <span>人数: {{ selectedClassInfo.studentCount }}人</span>
              </div>
            </template>
          </div>

          <!-- 添加冲突警告提示 -->
          <div
              v-if="conflicts.length > 0"
              class="conflict-warning"
          >
            <el-alert
                :title="`检测到 ${conflicts.length} 个排课冲突，请检查红色标记处`"
                type="error"
                show-icon
                :closable="false"
            />
          </div>

          <!-- 时间槽表格 -->
          <el-table
              :data="timeSlots"
              border
              style="width: 100%"
          >
            <el-table-column
                label="时间"
                width="100"
            >
              <template #default="scope">
                <div class="time-period">第{{ scope.row.period }}节</div>
                {{ scope.row.time }}
              </template>
            </el-table-column>

            <el-table-column
                v-for="day in WEEKDAYS"
                :key="day.value"
                :label="day.label"
            >
              <template #default="scope">
                <div
                    class="schedule-cell"
                    :class="{
                                        'can-drop': isDragging,
                                        'is-highlighted': highlightCell.timeSlotId === scope.row.id && highlightCell.weekday === day.value,
                                        'has-conflict': selectedClassroom && isCellConflicted(selectedClassroom, scope.row.id, day.value)
                                    }"
                    @dragenter="handleDragEnter($event, scope.row)"
                    @dragleave="handleDragLeave"
                    @dragover.prevent
                    @drop="handleDrop(scope.row, day.value)"
                    @click="handleCellClick(scope.row, day.value)"
                >
                  <!-- 显示已排课程 -->
                  <template
                      v-if="selectedClassroom && getScheduledCourse(selectedClassroom, scope.row.id, day.value)"
                  >
                    <el-card
                        shadow="hover"
                        class="scheduled-course"
                        :class="{
                                                'is-selectable': batchMode,
                                                'is-selected': batchMode && selectedScheduleItems.some(
                                                    item =>
                                                        item.classroomId === selectedClassroom &&
                                                        item.timeSlotId === scope.row.id &&
                                                        item.weekday === day.value
                                                )
                                            }"
                        @click.stop="toggleSelectScheduleItem({
                                                courseId: getScheduledCourse(selectedClassroom, scope.row.id, day.value)?.id || 0,
                                                classroomId: selectedClassroom,
                                                timeSlotId: scope.row.id,
                                                week: currentWeek,
                                                weekday: day.value,
                                                course: getScheduledCourse(selectedClassroom, scope.row.id, day.value)
                                            })"
                    >
                      <div class="scheduled-course-header">
                        <h4>{{ getScheduledCourse(selectedClassroom, scope.row.id,
                            day.value)?.name }}</h4>
                      </div>
                      <span
                          class="delete-course"
                          @click.stop="deleteScheduledCourse(selectedClassroom, scope.row.id, day.value)"
                      >
                                                <el-icon>
                                                    <Delete />
                                                </el-icon>
                                            </span>
                      <p>教师：{{ getScheduledCourse(selectedClassroom, scope.row.id,
                          day.value)?.teacher }}</p>
                      <!-- 课时 -->
                      <p>课时：{{ getScheduledCourse(selectedClassroom, scope.row.id,
                          day.value)?.hours }}</p>
                      <!-- 添加班级信息 -->
                      <p
                          v-if="getScheduledCourse(selectedClassroom, scope.row.id, day.value)?.classId">
                        班级：{{classes.find(c => c.id === getScheduledCourse(selectedClassroom,
                          scope.row.id, day.value)?.classId)?.name}}
                      </p>
                    </el-card>
                  </template>
                  <!-- 添加空单元格的悬停提示 -->
                  <template v-else>
                    <div
                        class="empty-cell-hint"
                        v-if="isDragging"
                    >
                      <el-icon>
                        <Plus />
                      </el-icon>
                      <span>拖放此处</span>
                    </div>
                  </template>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </template>

        <!-- 添加操作历史记录 -->
        <div
            class="operation-history"
            v-if="hasUnsavedChanges"
        >
          <el-divider content-position="center">未保存操作</el-divider>
          <el-timeline>
            <el-timeline-item
                v-for="(item, index) in tempScheduledCourses"
                :key="index"
                :timestamp="'待保存'"
                type="primary"
            >
              添加 {{ item.course?.name }} 到
              {{WEEKDAYS.find(day => day.value === item.weekday)?.label}}
              {{timeSlots.find(time => time.id === item.timeSlotId)?.time}}
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style lang="less" scoped>
.manual-scheduling-container {
  padding: 20px;
  height: 100vh;
}

.course-list {
  height: 100%;
}

.delete-course {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  color: red;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-item {
  width: 180px;
  margin-bottom: 8px;
  cursor: move;
  // background-color: #303133;

  .el-card {
    // width: 180px;
    // border-radius: 8px;
    // padding: 15px 0 0 15px !important;
  }
}


// 修改
// 课程卡片样式
.course-card {
  // height: 120px;
  // padding: 8px;

  .course-header {
    display: flex;
    // justify-content: space-between;
    align-items: center;

    h4.course-title {
      padding: 0;
      font-size: 14px;
      margin: 0;
      // background: greenyellow;
      display: inline-block;
    }

    .course-type-tag {
      margin-left: 1%;
      display: inline-block;
      border-radius: 4px;
      padding: 2px 4px;
      font-size: 10px;
      line-height: normal;
    }
  }

  p.teacher {
    padding: 0;
    // background: pink;
    font-size: 12px;
    // margin-bottom: -8px;
    line-height: 0;
    // display: inline-block;
  }

  // 学时和人数
  p.course-details {
    line-height: 0;

    span.time {
      display: inline-block;
      font-size: 12px;
    }

    span.number {
      display: inline-block;
      font-size: 12px;
    }
  }
}



/* .course-details p {
    margin: 2px 0;
    font-size: 12px;
    line-height: 1.2;
} */
//  修改待排课程样式
// course-details



.course-item .el-card {
  transition: all 0.3s;
}

.course-item .el-card.is-dragging {
  transform: scale(1.05);
  opacity: 0.8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.schedule-header {
  margin-bottom: 20px;
}

.schedule-cell {
  min-height: 120px;
  border: 1px dashed #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s;
  padding: 8px;
  position: relative;
}

.schedule-cell.can-drop {
  background-color: rgba(64, 158, 255, 0.1);
  border: 2px dashed #409EFF;
}

.schedule-cell.is-highlighted {
  background-color: rgba(64, 158, 255, 0.15);
  border: 2px solid #409EFF;
}

.schedule-cell.has-conflict {
  background-color: rgba(245, 108, 108, 0.1);
  border: 2px solid #F56C6C;
}

.scheduled-course {
  width: 100%;
  height: 100%;
  position: relative;
  transition: all 0.3s;
}

.scheduled-course:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.scheduled-course h4 {
  margin: 0 0 8px 0;
}

.scheduled-course p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.time-period {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.classroom-header {
  text-align: center;
}

.classroom-info {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.course-type {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 12px;
  color: #fff;
  background-color: #409EFF;
  padding: 2px 6px;
  border-radius: 4px;
}

.course-type-tag {
  display: inline-block;
  padding: 2px 8px;
  background-color: #409EFF;
  color: #fff;
  border-radius: 2px;
  font-size: 10px;
  /* margin-top: 2px; */
}

/* 拖拽时的动画效果 */
.sortable-ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.sortable-drag {
  opacity: 0.8;
  background: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.scheduled-course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.scheduled-course-header h4 {
  margin: 0;
}

.filter-section {
  margin-bottom: 15px;
}

.filter-section .el-select {
  width: 100%;
}

.el-space {
  display: flex;
  gap: 15px;
}

.classroom-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.classroom-option-info {
  font-size: 12px;
  color: #666;
}

.classroom-info-header {
  // width: auto;
  margin-bottom: 20px;
  padding: 15px;
  // background-color: #f5f7fa;
  // background-color: #2664c2;
  border-radius: 4px;
}

.classroom-info-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.class-info-header,
.classroom-info-header {
  // border-left: 2px solid #1890ff;
  // border-right: 2px solid #1890ff;

  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 0px 0px;
  // box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

  h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #303133;
  }

  .classroom-detail,
  .class-detail {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    span {
      font-size: 14px;
      color: #606266;
      background-color: #ecf5ff;
      padding: 4px 10px;
      border-radius: 4px;
      white-space: nowrap;

      @media (max-width: 768px) {
        width: 100%;
      }
    }
  }

  @media (max-width: 992px) {
    width: 100%;
  }
}

.classroom-detail {
  display: flex;
  gap: 20px;
  color: #606266;
  font-size: 14px;

  span {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    color: #555;
    background-color: white;
    // padding: 6px 12px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:nth-child(1)::before {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 6px;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231890ff"><path d="M16 11c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 3-1.34 3-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V18h14v-1.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C16.19 13.89 17 15.02 17 16.5V18h6v-1.5c0-2.33-4.67-3.5-7-3.5z"/></svg>') center/contain no-repeat;
    }

    &:nth-child(2)::before {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 6px;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231890ff"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>') center/contain no-repeat;
    }

    &:nth-child(3)::before {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 6px;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231890ff"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-5 14H4v-4h11v4zm0-5H4V9h11v4zm5 5h-4V9h4v9z"/></svg>') center/contain no-repeat;
    }
  }
}

.class-info-header {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.class-info-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.class-detail {
  display: flex;
  gap: 20px;
  color: #606266;
  font-size: 14px;
}

.class-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.class-option-info {
  font-size: 12px;
  color: #666;
}

.unsaved-tip {
  margin-top: 15px;
}

.empty-cell-hint {
  color: #909399;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.empty-cell-hint .el-icon {
  font-size: 24px;
}

.stats-section {
  margin: 15px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.stats-text {
  text-align: center;
  margin-top: 5px;
  font-size: 12px;
  color: #606266;
}

.conflict-warning {
  margin-bottom: 15px;
}

.is-selectable {
  cursor: pointer;
}

.is-selected {
  box-shadow: 0 0 0 2px #67c23a !important;
  border: 1px solid #67c23a;
}

.is-active {
  background-color: #409EFF;
  color: white;
}

.operation-history {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

::v-deep(.el-table__header th) {
  background-color: #ecf5ff !important;
  color: #606266;
  text-align: center;
  font-weight: 500;
}
</style>
