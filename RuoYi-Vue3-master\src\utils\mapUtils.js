/**
 * 地图工具函数
 */

// 腾讯地图API密钥
export const TMap_KEY = "P6DBZ-WVYCJ-A4HFW-FQ443-AP7XS-KIFSV";

/**
 * 获取腾讯地图API密钥
 * @returns {string} API密钥
 */
export function getMapAPI() {
  return TMap_KEY;
}

/**
 * 动态加载腾讯地图API脚本
 * @param {Function} callback 加载完成后的回调函数
 */
export function loadTMapScript(callback) {
  // 防止重复加载
  if (window.TMap) {
    console.log('腾讯地图API已加载，直接调用回调函数');
    callback && callback();
    return;
  }

  console.log('开始加载腾讯地图API脚本...');
  
  // 检查先前可能存在的加载失败的脚本
  const existingScript = document.querySelector('script[src*="map.qq.com/api/gljs"]');
  if (existingScript) {
    console.log('移除先前可能加载失败的脚本');
    existingScript.parentNode.removeChild(existingScript);
  }

  const script = document.createElement('script');
  script.type = 'text/javascript';
  // 去掉libraries参数，使用基础URL
  script.src = `https://map.qq.com/api/gljs?v=1.exp&key=${TMap_KEY}`;
  
  // 设置超时处理
  let timeoutId = setTimeout(() => {
    console.error('腾讯地图脚本加载超时');
    if (script.parentNode) {
      script.parentNode.removeChild(script);
    }
    alert('地图加载超时，请检查网络连接并刷新页面');
  }, 10000); // 10秒超时
  
  script.onload = () => {
    console.log('腾讯地图API脚本加载成功');
    clearTimeout(timeoutId);
    
    // 延迟一下确保TMap对象真的初始化完毕
    setTimeout(() => {
      // 确保TMap对象真的存在
      if (window.TMap) {
        console.log('TMap对象已存在，调用回调函数');
        callback && callback();
      } else {
        console.error('腾讯地图API脚本加载完成但TMap对象不存在');
        alert('地图API加载异常，请刷新页面重试');
      }
    }, 100);
  };
  
  script.onerror = (error) => {
    console.error('腾讯地图脚本加载失败', error);
    clearTimeout(timeoutId);
    alert('地图加载失败，请检查网络连接并刷新页面');
  };
  
  document.head.appendChild(script);
  console.log('腾讯地图API脚本已添加到页面');
}

/**
 * 根据经纬度计算两点之间的距离（单位：米）
 * @param {number} lat1 第一个点的纬度
 * @param {number} lng1 第一个点的经度
 * @param {number} lat2 第二个点的纬度
 * @param {number} lng2 第二个点的经度
 * @returns {number} 两点之间的距离（米）
 */
export function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c;
  return distance;
}

/**
 * 生成地图标记点样式
 * @param {string} color 标记点颜色，默认为红色
 * @returns {Object} 标记点样式对象
 */
export function createMarkerStyle(color = '#FF0000') {
  // 使用基本样式，不指定图片URL (让腾讯地图使用默认图标)
  return {
    width: 35,
    height: 35,
    anchor: { x: 16, y: 32 },
    // 不指定src，使用API自带图标
    fillColor: color,
    strokeColor: '#ffffff',
    // 为不同类型的点设置不同的属性
    ...(color === '#0000FF' ? { scale: 1.2 } : {}),              // 选中的点略大
    ...(color === '#00AA00' ? { strokeWidth: 2 } : {})           // 办公楼点边框更粗
  };
}

export default {
  getMapAPI,
  loadTMapScript,
  calculateDistance,
  createMarkerStyle
}; 