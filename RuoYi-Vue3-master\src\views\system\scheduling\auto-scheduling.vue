<script setup>
import {onMounted, ref} from "vue"
import {
  ElMessage,
  ElMessageBox,
  ElSelect,
  ElOption,
  ElButton,
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElAlert,
  ElTable,
  ElTableColumn,
  ElEmpty,
  ElScrollbar,
  ElCollapse,
  ElCollapseItem,
  ElTooltip,
  ElStatistic,
  ElRadioGroup,
  ElRadio,
  ElProgress,
  ElTag
} from 'element-plus'
import {getStrategies as getStrategiesApi} from '@/api/system/strategy'
// 排课模式
// 'class' 或 'classroom'
const schedulingMode = ref('class')

// 策略列表数据
const strategies = ref([])
const selectedStrategy = ref(null)
const loading = ref(false)
const isManualScheduling = ref(false)

// 班级和教室数据
const classes = ref([])
const classrooms = ref([])
const selectedClass = ref(null)
const selectedClassroom = ref(null)

// 模拟数据
const mockData = {
  classes: [
    {id: 1, name: '计算机科学与技术1班', studentCount: 45},
    {id: 2, name: '软件工程1班', studentCount: 40},
    {id: 3, name: '人工智能1班', studentCount: 35},
    {id: 4, name: '数据科学1班', studentCount: 38}
  ],
  classrooms: [
    {id: 1, name: 'A101', capacity: 50, type: 'normal'},
    {id: 2, name: 'A102', capacity: 45, type: 'normal'},
    {id: 3, name: 'B201', capacity: 60, type: 'normal'},
    {id: 4, name: 'C301', capacity: 40, type: 'lab'},
    {id: 5, name: '体育馆', capacity: 100, type: 'pe'}
  ],
  courses: [
    {
      id: 1,
      name: '高等数学',
      teacher: '张教授',
      type: 'theory',
      studentCount: 45,
      duration: 2,
      requireLab: false
    },
    {
      id: 2,
      name: '程序设计基础',
      teacher: '李教授',
      type: 'lab',
      studentCount: 40,
      duration: 2,
      requireLab: true
    },
    {
      id: 3,
      name: '体育',
      teacher: '王教练',
      type: 'PE',
      studentCount: 35,
      duration: 2,
      requireLab: false
    },
    {
      id: 4,
      name: '数据结构',
      teacher: '刘教授',
      type: 'theory',
      studentCount: 38,
      duration: 2,
      requireLab: false
    }
  ],
  strategies: [
    {
      id: 1,
      name: '标准排课策略',
      description: '适用于常规课程排课，优先考虑教师时间和教室容量',
      status: 'active',
      updateTime: '2024-03-10',
      config: {
        clearPreviousSchedule: true,
        autoAssignLocation: true,
        teacherConstraints: {
          maxDailyPeriods: 4,
          maxWeeklyPeriods: 16
        }
      }
    },
    {
      id: 2,
      name: '实验课程优先',
      description: '优先安排实验课程，确保实验室资源最优配置',
      status: 'active',
      updateTime: '2024-03-09',
      config: {
        clearPreviousSchedule: true,
        autoAssignLocation: true,
        specialRules: {
          labClassEveningOnly: true
        }
      }
    },
    {
      id: 3,
      name: '体育课优先',
      description: '优先安排体育课，确保体育馆资源最优配置',
      status: 'active',
      updateTime: '2024-03-08',
      config: {
        clearPreviousSchedule: true,
        autoAssignLocation: true,
        specialRules: {
          peClassAfternoonOnly: true
        }
      }
    },
    {
      id: 4,
      name: '实验课程优先',
      description: '优先安排实验课程，确保实验室资源最优配置',
      status: 'active',
      updateTime: '2024-03-07',
      config: {
        clearPreviousSchedule: true,
        autoAssignLocation: true,
        specialRules: {
          labClassEveningOnly: true
        }
      }
    }
  ]
}

// 获取策略列表
const loadStrategies = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const res = await getStrategiesApi()

    strategies.value = res.rows
  } catch (error) {
    console.error('加载策略失败:', error)
    ElMessage.error('加载策略列表失败')
  } finally {
    loading.value = false
  }
}

// 加载班级和教室数据
const loadClassesAndClassrooms = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    classes.value = mockData.classes
    classrooms.value = mockData.classrooms
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载班级和教室数据失败')
  }
}

// 选择策略时的处理
const handleStrategySelect = (strategy) => {
  selectedStrategy.value = strategy
  if (strategy) {
    strategyConfig.value = {
      ...strategyConfig.value,
      ...strategy,
      clearPreviousSchedule: strategyConfig.value.clearPreviousSchedule,
      autoAssignLocation: strategyConfig.value.autoAssignLocation
    }
  }
}

// 判断策略是否被选中
const isStrategySelected = (strategy) => {
  return selectedStrategy.value && selectedStrategy.value.id === strategy.id
}

// 在组件挂载时加载数据
onMounted(() => {
  loadStrategies()
  loadClassesAndClassrooms()
})

// 排课策略配置
const strategyConfig = ref({
  // 基础配置
  clearPreviousSchedule: false,
  autoAssignLocation: true,
  priorityCourses: [],

  // 教师约束
  teacherConstraints: {
    maxDailyPeriods: 6,
    maxWeeklyPeriods: 20,
    maxMorningPeriods: 4,
    maxAfternoonPeriods: 4,
    maxEveningPeriods: 2
  },

  // 教室约束
  classroomConstraints: {
    useFixedClassroom: false,
    centralizedArrangement: true,
    checkCapacity: true
  },

  // 特殊规则
  specialRules: {
    peClassAfternoonOnly: true,
    allowClassAfterPE: false,
    eveningClassEnabled: false,
    labClassEveningOnly: true,
    continuousArrangement: true,
    oddWeekSplit: false
  },

  // 优先级设置
  priorities: {
    departmentFirst: true,
    courseTypeFirst: false,
    creditHoursFirst: false
  }
})

// 排课进度监控
const scheduleProgress = ref({
  totalTasks: 0,
  completedTasks: 0,
  uncompletedTasks: 0,
  status: "idle",
  currentStep: "",
  errorMessage: ""
})

// 排课结果
const scheduleResult = ref({
  success: false,
  unscheduledCourses: [],
  scheduleData: null,
  conflicts: [], // 新增：记录冲突信息
  aiSuggestions: [], // 新增：AI建议
  statistics: {
    totalClasses: 0,
    scheduledClasses: 0,
    conflictCount: 0,
    resourceUtilization: 0
  }
})

// 课表数据结构 - 调整为适合表格展示的格式
const timeSlots = [
  {label: '第1-2节', id: '1-2', time: '8:00-9:40'},
  {label: '第3-4节', id: '3-4', time: '10:00-11:40'},
  {label: '第5-6节', id: '5-6', time: '14:00-15:40'},
  {label: '第7-8节', id: '7-8', time: '16:00-17:40'},
  {label: '第9-10节', id: '9-10', time: '19:00-20:40'}
]

const weekDays = [
  {label: '周一', id: 'monday'},
  {label: '周二', id: 'tuesday'},
  {label: '周三', id: 'wednesday'},
  {label: '周四', id: 'thursday'},
  {label: '周五', id: 'friday'}
]

// 课表数据 - 二维数组格式，用于表格展示
const scheduleTableData = ref(
    timeSlots.map(slot => {
      const row = {timeSlot: slot.label, timeRange: slot.time};
      weekDays.forEach(day => {
        row[day.id] = [];
      });
      return row;
    })
)

// 原树形结构保留，用于兼容旧代码
const scheduleTreeData = ref([
  {
    label: '周一',
    id: 'monday',
    children: [
      {
        label: '上午',
        id: 'monday-morning',
        children: [
          {
            label: '第1-2节',
            id: 'monday-1-2',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[0] // 默认教室
          },
          {
            label: '第3-4节',
            id: 'monday-3-4',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[0]
          }
        ]
      },
      {
        label: '下午',
        id: 'monday-afternoon',
        children: [
          {
            label: '第5-6节',
            id: 'monday-5-6',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[0]
          },
          {
            label: '第7-8节',
            id: 'monday-7-8',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[0]
          }
        ]
      },
      {
        label: '晚上',
        id: 'monday-evening',
        children: [
          {
            label: '第9-10节',
            id: 'monday-9-10',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[0]
          }
        ]
      }
    ]
  },
  {
    label: '周二',
    id: 'tuesday',
    children: [
      {
        label: '上午',
        id: 'tuesday-morning',
        children: [
          {
            label: '第1-2节',
            id: 'tuesday-1-2',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[1]
          },
          {
            label: '第3-4节',
            id: 'tuesday-3-4',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[1]
          }
        ]
      },
      {
        label: '下午',
        id: 'tuesday-afternoon',
        children: [
          {
            label: '第5-6节',
            id: 'tuesday-5-6',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[1]
          },
          {
            label: '第7-8节',
            id: 'tuesday-7-8',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[1]
          }
        ]
      },
      {
        label: '晚上',
        id: 'tuesday-evening',
        children: [
          {
            label: '第9-10节',
            id: 'tuesday-9-10',
            type: 'period',
            courses: [],
            classroom: mockData.classrooms[1]
          }
        ]
      }
    ]
  }
])

// 检查课程冲突
const checkConflicts = (course, targetPeriod) => {
  const conflicts = []

  // 检查教师时间冲突
  const teacherConflict = targetPeriod.courses.find(c => c.teacher === course.teacher)
  if (teacherConflict) {
    conflicts.push({
      type: 'teacher',
      message: `教师 ${course.teacher} 在该时段已有课程安排`
    })
  }

  // 检查教室容量
  if (course.studentCount > targetPeriod.classroom?.capacity) {
    conflicts.push({
      type: 'capacity',
      message: `教室容量不足（需要: ${course.studentCount}, 容量: ${targetPeriod.classroom?.capacity}）`
    })
  }

  // 检查特殊规则
  if (course.type === 'PE' && !isPETimeSlotValid(targetPeriod)) {
    conflicts.push({
      type: 'rule',
      message: '体育课只能安排在下午时段'
    })
  }

  return conflicts
}

// 检查时间段是否适合体育课
const isPETimeSlotValid = (period) => {
  const periodId = period.id
  return periodId.includes('afternoon')
}

// 获取AI建议
const getAISuggestions = async (conflicts) => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 1000))

  const suggestions = []
  conflicts.forEach(conflict => {
    switch (conflict.type) {
      case 'teacher':
        suggestions.push('建议将课程调整到该教师其他空闲时段，或与其他教师协调互换课程')
        break
      case 'capacity':
        suggestions.push('建议更换到更大容量的教室，或考虑将班级分成两个小班')
        break
      case 'rule':
        if (conflict.message.includes('体育课')) {
          suggestions.push('建议将体育课调整到下午时段，可以与其他下午的课程互换')
        }
        break
    }
  })
  return suggestions
}

// 添加拖拽相关的状态
const draggingCourse = ref(null)
const isDragging = ref(false)

// 处理课程拖拽开始
const handleDragStart = (course) => {
  draggingCourse.value = course
  isDragging.value = true
}

// 处理拖拽结束
const handleDragEnd = () => {
  isDragging.value = false
  draggingCourse.value = null
}

// 拖放相关处理 - 适配表格形式
const handleCellDrop = async (event, day, timeSlotIndex) => {
  if (!draggingCourse.value) return

  try {
    // 获取时间段ID
    const timeSlotId = timeSlots[timeSlotIndex].id
    // 查找对应的period节点
    const dayNode = scheduleTreeData.value.find(d => d.id === day)
    let periodNode = null

    if (dayNode) {
      dayNode.children.forEach(timeGroup => {
        timeGroup.children.forEach(period => {
          if (period.id.includes(timeSlotId)) {
            periodNode = period
          }
        })
      })
    }

    if (!periodNode) return

    // 检查冲突
    const conflicts = checkConflicts(draggingCourse.value, periodNode)

    if (conflicts.length > 0) {
      // 显示冲突提示
      ElMessage.warning({
        message: '检测到课程冲突，正在获取AI建议...',
        duration: 3000
      })

      // 获取AI建议
      const suggestions = await getAISuggestions(conflicts)

      // 显示建议对话框
      await ElMessageBox.confirm(
          `发现以下冲突：\n${conflicts.map(c => c.message).join('\n')}\n\nAI建议：\n${suggestions.join('\n')}`,
          '课程冲突提示',
          {
            confirmButtonText: '仍然安排',
            cancelButtonText: '取消',
            type: 'warning'
          }
      )
    }

    // 添加到新位置
    if (addCourseToPeriod(periodNode, draggingCourse.value)) {
      // 同步更新表格数据
      const tableRow = scheduleTableData.value[timeSlotIndex]
      tableRow[day].push(draggingCourse.value)

      ElMessage.success('课程调整成功')
    }
  } catch (error) {
    if (error?.message !== 'cancel') {
      ElMessage.error('课程调整失败')
    }
  } finally {
    draggingCourse.value = null
  }
}

// 从表格中删除课程
const deleteScheduledCourseFromTable = async (course, day, timeSlotIndex) => {
  try {
    // 获取时间段ID
    const timeSlotId = timeSlots[timeSlotIndex].id
    // 查找对应的period节点
    const dayNode = scheduleTreeData.value.find(d => d.id === day)
    let periodNode = null

    if (dayNode) {
      dayNode.children.forEach(timeGroup => {
        timeGroup.children.forEach(period => {
          if (period.id.includes(timeSlotId)) {
            periodNode = period
          }
        })
      })
    }

    if (periodNode) {
      // 从树形结构中删除
      const index = periodNode.courses.findIndex(c => c.id === course.id)
      if (index !== -1) {
        periodNode.courses.splice(index, 1)
      }
    }

    // 从表格数据中删除
    const tableRow = scheduleTableData.value[timeSlotIndex]
    const courseIndex = tableRow[day].findIndex(c => c.id === course.id)
    if (courseIndex !== -1) {
      tableRow[day].splice(courseIndex, 1)
    }

    ElMessage.success('删除课程成功')
  } catch (error) {
    ElMessage.error('删除课程失败')
  }
}

// 开始排课
async function startScheduling() {
  if (!selectedStrategy.value) {
    ElMessage.warning('请先选择排课策略')
    return
  }

  if (schedulingMode.value === 'class' && !selectedClass.value) {
    ElMessage.warning('请选择要排课的班级')
    return
  }

  if (schedulingMode.value === 'classroom' && !selectedClassroom.value) {
    ElMessage.warning('请选择要排课的教室')
    return
  }

  try {
    await ElMessageBox.confirm("确认开始自动排课？此操作可能需要几分钟时间", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
    // 隐藏 前俩个卡片
    isManualScheduling.value = true
    scheduleProgress.value.status = "running"
    scheduleProgress.value.currentStep = "初始化排课参数"
    // 模拟排课过程
    const totalSteps = 5
    for (let i = 1; i <= totalSteps; i++) {
      await new Promise(resolve => setTimeout(resolve, 1500))
      scheduleProgress.value.totalTasks = totalSteps
      scheduleProgress.value.completedTasks = i
      scheduleProgress.value.uncompletedTasks = totalSteps - i

      switch (i) {
        case 1:
          scheduleProgress.value.currentStep = "加载课程数据"
          break
        case 2:
          scheduleProgress.value.currentStep = "检查时间冲突"
          break
        case 3:
          scheduleProgress.value.currentStep = "优化教室分配"
          break
        case 4:
          scheduleProgress.value.currentStep = "处理特殊规则"
          break
        case 5:
          scheduleProgress.value.currentStep = "生成最终课表"
          break
      }
    }

    // 模拟排课结果
    scheduleProgress.value.status = "completed"
    scheduleResult.value = {
      success: true,
      conflicts: [
        {
          type: 'teacher',
          message: '李教授在周一上午已有课程安排',
          suggestion: '建议调整到周二下午空闲时段'
        },
        {
          type: 'rule',
          message: '体育课安排在上午时段不符合规则',
          suggestion: '建议调整到下午时段'
        }
      ],
      unscheduledCourses: [
        {
          courseCode: 'CS102',
          courseName: '程序设计基础实验',
          teacher: '李教授',
          reason: '实验室已满'
        }
      ],
      statistics: {
        totalClasses: mockData.courses.length,
        scheduledClasses: mockData.courses.length - 1,
        conflictCount: 2,
        resourceUtilization: 85.5
      }
    }

    // 模拟课表数据
    const sampleSchedule = [
      {
        dayOfWeek: 'monday',
        timeSlot: '1-2',
        course: mockData.courses[0]
      },
      {
        dayOfWeek: 'monday',
        timeSlot: '5-6',
        course: mockData.courses[2]
      },
      {
        dayOfWeek: 'tuesday',
        timeSlot: '3-4',
        course: mockData.courses[3]
      }
    ]

    convertScheduleResultToTable(sampleSchedule)
    ElMessage.success("排课完成！")


  } catch (error) {
    scheduleProgress.value.status = "failed"
    scheduleProgress.value.errorMessage = error.message
    ElMessage.error(error.message || "操作取消")
  }
}

// 监控排课进度
function startProgressMonitoring() {
  const progressCheck = setInterval(async () => {
    try {
      const response = await fetch('/api/scheduling/progress')
      const progress = await response.json()

      scheduleProgress.value = {
        ...scheduleProgress.value,
        ...progress
      }

      if (progress.status === 'completed' || progress.status === 'failed') {
        clearInterval(progressCheck)
        handleScheduleComplete()
      }
    } catch (error) {
      console.error('进度查询失败:', error)
    }
  }, 2000)
}

// 导出排课结果
async function exportSchedule() {
  try {
    const response = await fetch('/api/scheduling/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(scheduleResult.value)
    })

    if (!response.ok) {
      throw new Error('导出失败')
    }

    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `课表_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)

    ElMessage.success("导出成功！")
  } catch (error) {
    ElMessage.error("导出失败：" + error.message)
  }
}

// 更新处理排课结束函数
const handleScheduleComplete = async () => {
  if (scheduleProgress.value.status === 'completed') {
    try {
      // 模拟获取排课结果
      const sampleSchedule = [
        {
          dayOfWeek: 'monday',
          timeSlot: '1-2',
          course: mockData.courses[0]
        },
        {
          dayOfWeek: 'monday',
          timeSlot: '5-6',
          course: mockData.courses[2]
        },
        {
          dayOfWeek: 'tuesday',
          timeSlot: '3-4',
          course: mockData.courses[3]
        }
      ]

      // 将排课结果转换为表格数据格式
      convertScheduleResultToTable(sampleSchedule)
      ElMessage.success("排课完成！")
    } catch (error) {
      ElMessage.error("获取排课结果失败")
    }
  } else {
    ElMessage.error("排课失败：" + scheduleProgress.value.errorMessage)
  }
}

// 将排课结果转换为表格结构
const convertScheduleResultToTable = (scheduleData) => {
  if (!scheduleData) return

  // 清空现有课程数据（表格形式）
  scheduleTableData.value.forEach(row => {
    weekDays.forEach(day => {
      row[day.id] = []
    })
  })

  // 填充新的课程数据
  scheduleData.forEach(schedule => {
    const {dayOfWeek, timeSlot, course} = schedule

    // 找到对应的时间段行
    const timeSlotIndex = timeSlots.findIndex(slot => slot.id === timeSlot)
    if (timeSlotIndex >= 0) {
      // 添加课程到表格数据中
      const row = scheduleTableData.value[timeSlotIndex]
      row[dayOfWeek].push({
        id: course.id,
        name: course.name,
        teacher: course.teacher,
        type: course.type,
        classroom: course.classroom || '未分配'
      })
    }
  })

  // 同时更新树形结构数据（兼容性考虑）
  convertScheduleResultToTree(scheduleData)
}

// 原树形结构转换函数保留
const convertScheduleResultToTree = (scheduleData) => {
  if (!scheduleData) return

  // 清空现有课程
  scheduleTreeData.value.forEach(day => {
    day.children.forEach(timeSlot => {
      timeSlot.children.forEach(period => {
        period.courses = []
      })
    })
  })

  // 填充新的课程数据
  scheduleData.forEach(schedule => {
    const {dayOfWeek, timeSlot, course} = schedule
    const dayNode = scheduleTreeData.value.find(d => d.id === dayOfWeek.toLowerCase())
    if (dayNode) {
      const periodNode = findPeriodNode(dayNode, timeSlot)
      if (periodNode) {
        addCourseToPeriod(periodNode, {
          id: course.id,
          name: course.name,
          teacher: course.teacher,
          type: course.type,
          classroom: course.classroom || '未分配'
        })
      }
    }
  })
}

// 查找对应的时间段节点
const findPeriodNode = (dayNode, timeSlot) => {
  let periodNode = null
  dayNode.children.forEach(slot => {
    slot.children.forEach(period => {
      if (period.id.includes(timeSlot)) {
        periodNode = period
      }
    })
  })
  return periodNode
}

// 添加课程到时间段
const addCourseToPeriod = (periodNode, course) => {
  // 检查是否已存在
  if (periodNode.courses.some(c => c.id === course.id)) {
    ElMessage.warning('该课程已在此时间段中')
    return false
  }

  // 检查时间段限制
  if (periodNode.courses.length >= 2) {
    ElMessage.warning('该时间段课程已满')
    return false
  }

  periodNode.courses.push(course)
  return true
}

// 手动排课处理
const handleManualSchedule = (course) => {
  ElMessage.info(`准备手动安排课程: ${course.courseName}`)
  // 这里可以添加手动排课的逻辑
}
</script>

<template>
  <div class="auto-scheduling">
    <!-- 排课配置区域 - 将模式选择和策略选择放在同一行 -->
    <transition
        name="config-fade"
        mode="out-in"
    >
      <el-row
          :gutter="20"
          class="scheduling-config"
          v-if="!isManualScheduling"
      >
        <!-- 排课模式选择 -->
        <el-col :span="8">
          <el-card class="mode-select card-with-shadow">
            <template #header>
              <div class="card-header card-header-primary">
                <span><i class="el-icon-sort mr-2"></i>排课模式</span>
              </div>
            </template>

            <!-- 排课模式选择 -->
            <div class="mode-content">
              <el-radio-group
                  v-model="schedulingMode"
                  class="mode-options"
              >
                <el-radio
                    label="class"
                    border
                >
                  <div class="mode-radio-content">
                    <i class="el-icon-user-solid"></i>
                    <span>按班级排课</span>
                  </div>
                </el-radio>
                <el-radio
                    label="classroom"
                    border
                >
                  <div class="mode-radio-content">
                    <i class="el-icon-office-building"></i>
                    <span>按教室排课</span>
                  </div>
                </el-radio>
              </el-radio-group>

              <!-- 按班级排课 -->
              <div
                  class="target-select"
                  v-if="schedulingMode === 'class'"
              >
                <div class="select-label">
                  <i class="el-icon-school mr-1"></i>
                  <span>选择班级</span>
                </div>
                <el-select
                    v-model="selectedClass"
                    placeholder="请选择班级"
                    style="width: 100%"
                >
                  <el-option
                      v-for="item in classes"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  >
                    <div class="option-with-count">
                      <span>{{ item.name }}</span>
                      <el-tag
                          size="small"
                          type="info"
                      >{{ item.studentCount }}人
                      </el-tag>
                    </div>
                  </el-option>
                </el-select>
              </div>

              <!-- 按教室排课 -->
              <div
                  class="target-select"
                  v-if="schedulingMode === 'classroom'"
              >
                <div class="select-label">
                  <i class="el-icon-office-building mr-1"></i>
                  <span>选择教室</span>
                </div>
                <el-select
                    v-model="selectedClassroom"
                    placeholder="请选择教室"
                    style="width: 100%"
                >
                  <el-option
                      v-for="item in classrooms"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  >
                    <div class="option-with-count">
                      <span>{{ item.name }}</span>
                      <div class="option-tags">
                        <el-tag
                            size="small"
                            type="success"
                        >{{ item.capacity }}人
                        </el-tag>
                        <el-tag
                            size="small"
                            :type="item.type === 'normal' ? 'info' : item.type === 'lab' ? 'primary' : 'warning'"
                        >
                          {{
                            item.type === 'normal' ? '普通' : item.type === 'lab' ? '实验室' :
                                '体育馆'
                          }}
                        </el-tag>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </div>

              <!-- 开始排课 -->
              <el-button
                  class="start-btn"
                  type="primary"
                  @click="startScheduling"
                  :disabled="scheduleProgress.status === 'running' || !selectedStrategy"
              >
                <i class="el-icon-video-play mr-1"></i>开始排课
              </el-button>
            </div>
          </el-card>
          <!-- 历史记录区域 -->
          <el-card class="left-card">
            <template #header>
              <div class="card-header">
                <span>最近排课记录</span>
                <el-button
                    type="text"
                    @click="clearHistory"
                >清空
                </el-button>
              </div>
            </template>

            <el-timeline class="history-list">
              <el-timeline-item
                  v-for="(record, index) in historyRecords"
                  :key="index"
                  :timestamp="record.time"
                  :type="getTimelineType(index)"
              >
                <h4>{{ record.title }}</h4>
                <p>{{ record.description }}</p>
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </el-col>

        <!-- 策略选择卡片 -->
        <el-col :span="16">
          <el-card
              class="strategy-select card-with-shadow"
              v-loading="loading"
          >
            <template #header>
              <div class="card-header card-header-primary">
                <span><i class="el-icon-setting mr-2"></i>选择排课策略</span>
                <el-button
                    type="text"
                    icon="el-icon-refresh-right"
                    @click="loadStrategies"
                    :disabled="loading"
                >刷新
                </el-button>
              </div>
            </template>

            <el-empty
                v-if="strategies.length === 0"
                description="暂无可用策略"
            >
              <el-button
                  type="primary"
                  @click="$router.push('/scheduling/strategy')"
              >
                <i class="el-icon-plus mr-1"></i>创建策略
              </el-button>
            </el-empty>

            <div
                v-else
                class="strategy-container"
            >
              <el-scrollbar height="350px">
                <div
                    v-for="strategy in strategies"
                    :key="strategy.id"
                    class="strategy-item"
                    :class="{ 'strategy-item-selected': isStrategySelected(strategy) }"
                    @click="selectedStrategy = strategy; handleStrategySelect(strategy)"
                >
                  <div class="strategy-radio">
                    <div class="radio-indicator">
                      <div
                          class="radio-inner"
                          v-if="isStrategySelected(strategy)"
                      ></div>
                    </div>
                    <div class="strategy-info">
                      <div class="strategy-header">
                        <div class="strategy-name">{{ strategy.name }}</div>
                        <el-tag
                            size="small"
                            effect="dark"
                            :type="strategy.status === 'active' ? 'success' : 'info'"
                        >
                          {{ strategy.status === 'active' ? '启用' : '停用' }}
                        </el-tag>
                      </div>
                      <div class="strategy-desc">{{ strategy.description }}</div>
                      <div class="strategy-meta">
                                                <span class="update-time">
                                                    <i class="el-icon-time mr-1"></i>更新时间: {{ strategy.updateTime }}
                                                </span>
                        <span
                            class="strategy-config"
                            v-if="strategy.config"
                        >
                                                    <el-tooltip
                                                        v-if="strategy.config.specialRules?.peClassAfternoonOnly"
                                                        content="体育课仅安排在下午"
                                                        placement="top"
                                                    >
                                                        <i class="el-icon-medal mr-1"></i>
                                                    </el-tooltip>
                                                    <el-tooltip
                                                        v-if="strategy.config.specialRules?.labClassEveningOnly"
                                                        content="实验课优先安排在晚上"
                                                        placement="top"
                                                    >
                                                        <i class="el-icon-moon mr-1"></i>
                                                    </el-tooltip>
                                                </span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </transition>

    <!-- 排课进度 -->
    <el-card
        class="schedule-progress card-with-shadow"
        v-if="scheduleProgress.status !== 'idle'"
        v-motion-fade
    >
      <template #header>
        <div class="card-header card-header-gradient">
          <span><i class="el-icon-loading mr-2"></i>排课进度</span>
        </div>
      </template>

      <div class="progress-info">
        <div class="progress-container">
          <el-progress
              class="progress-bar"
              :percentage="Math.floor((scheduleProgress.completedTasks / scheduleProgress.totalTasks) * 100)"
              :status="scheduleProgress.status === 'completed' ? 'success' : scheduleProgress.status === 'failed' ? 'exception' : ''"
              :stroke-width="20"
              :show-text="false"
          />
          <div class="progress-percentage">
            {{ Math.floor((scheduleProgress.completedTasks / scheduleProgress.totalTasks) * 100) }}%
          </div>
        </div>

        <div class="current-step-container">
          <div class="step-indicator">
            <div class="step-icon">
              <i class="el-icon-set-up"></i>
            </div>
          </div>
          <div class="current-step">
            <span class="step-label">当前步骤：</span>
            <span class="step-value">{{ scheduleProgress.currentStep }}</span>
          </div>
        </div>

        <div class="progress-stats">
          <div class="stat-item">
            <div class="stat-icon">
              <i class="el-icon-notebook-1"></i>
            </div>
            <div class="stat-content">
              <span class="stat-label">总任务数</span>
              <span class="stat-value">{{ scheduleProgress.totalTasks }}</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon completed">
              <i class="el-icon-finished"></i>
            </div>
            <div class="stat-content">
              <span class="stat-label">已完成任务</span>
              <span class="stat-value completed">{{ scheduleProgress.completedTasks }}</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon pending">
              <i class="el-icon-timer"></i>
            </div>
            <div class="stat-content">
              <span class="stat-label">未完成任务</span>
              <span class="stat-value pending">{{ scheduleProgress.uncompletedTasks }}</span>
            </div>
          </div>
        </div>

        <div
            class="error-message"
            v-if="scheduleProgress.status === 'failed'"
        >
          <el-alert
              :title="scheduleProgress.errorMessage"
              type="error"
              :closable="false"
              show-icon
          />
        </div>
      </div>
    </el-card>

    <!-- 排课结果 -->
    <el-card
        class="schedule-result card-with-shadow"
        v-if="scheduleProgress.status === 'completed'"
        v-motion-slide-bottom
    >
      <template #header>
        <div class="card-header card-header-gradient">
          <span><i class="el-icon-document-checked mr-2"></i>排课结果</span>
          <el-button
              type="primary"
              @click="exportSchedule"
              class="export-btn"
          >
            <i class="el-icon-download mr-1"></i>导出结果
          </el-button>
        </div>
      </template>

      <div class="result-stats">
        <el-row
            :gutter="20"
            class="stat-cards"
        >
          <el-col :span="6">
            <div class="stat-card total-courses">
              <el-statistic
                  title="总课程数"
                  :value="scheduleResult.statistics.totalClasses"
                  class="statistic-item"
              >
                <template #prefix>
                  <i class="el-icon-notebook-1"></i>
                </template>
              </el-statistic>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card scheduled-courses">
              <el-statistic
                  title="已排课程"
                  :value="scheduleResult.statistics.scheduledClasses"
                  class="statistic-item"
              >
                <template #prefix>
                  <i class="el-icon-finished"></i>
                </template>
              </el-statistic>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card conflicts">
              <el-statistic
                  title="冲突数量"
                  :value="scheduleResult.statistics.conflictCount"
                  class="statistic-item"
              >
                <template #prefix>
                  <i class="el-icon-warning"></i>
                </template>
              </el-statistic>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card utilization">
              <el-statistic
                  title="资源利用率"
                  :value="scheduleResult.statistics.resourceUtilization"
                  :precision="2"
                  suffix="%"
                  class="statistic-item"
              >
                <template #prefix>
                  <i class="el-icon-data-analysis"></i>
                </template>
              </el-statistic>
            </div>
          </el-col>
        </el-row>

        <!-- 冲突提示 -->
        <el-alert
            v-if="scheduleResult.conflicts.length > 0"
            title="存在课程冲突"
            type="warning"
            :closable="false"
            show-icon
            class="conflict-alert"
        >
          <template #default>
            <div class="conflicts-container">
              <div
                  v-for="(conflict, index) in scheduleResult.conflicts"
                  :key="index"
                  class="conflict-item"
              >
                <div class="conflict-icon">
                  <i class="el-icon-warning-outline"></i>
                </div>
                <div class="conflict-details">
                  <p class="conflict-message">{{ conflict.message }}</p>
                  <p
                      class="ai-suggestion"
                      v-if="conflict.suggestion"
                  >
                    <i class="el-icon-magic-stick mr-1"></i>AI建议：{{ conflict.suggestion }}
                  </p>
                </div>
              </div>
            </div>
          </template>
        </el-alert>

        <!-- 未排课程列表 -->
        <el-alert
            v-if="scheduleResult.unscheduledCourses.length > 0"
            title="存在未能排课的课程"
            type="warning"
            :closable="false"
            show-icon
            class="unscheduled-alert"
        >
          <div>未排课程数：<span class="unscheduled-count">{{ scheduleResult.unscheduledCourses.length }}</span>
          </div>
        </el-alert>

        <transition name="fade">
          <el-table
              v-if="scheduleResult.unscheduledCourses.length > 0"
              :data="scheduleResult.unscheduledCourses"
              style="margin-top: 20px"
              class="unscheduled-table"
              stripe
              border
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column
                prop="courseCode"
                label="课程代码"
                width="140"
            />
            <el-table-column
                prop="courseName"
                label="课程名称"
                width="200"
            />
            <el-table-column
                prop="teacher"
                label="教师"
                width="120"
            />
            <el-table-column
                prop="reason"
                label="未排原因"
            />
            <el-table-column
                label="操作"
                width="120"
            >
              <template #default="{ row }">
                <el-tooltip
                    content="尝试手动安排"
                    placement="top"
                >
                  <el-button
                      type="primary"
                      size="small"
                      circle
                      @click="handleManualSchedule(row)"
                  >
                    <i class="el-icon-edit"></i>
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </transition>
      </div>
    </el-card>

    <!-- 课表展示（可拖拽调整） -->
    <el-card
        class="schedule-table card-with-shadow"
        v-if="scheduleProgress.status === 'completed'"
        v-motion-slide-bottom
    >
      <template #header>
        <div class="card-header card-header-gradient">
          <span><i class="el-icon-date mr-2"></i>课表预览（可拖拽调整）</span>
        </div>
      </template>

      <div class="timetable-container">
        <div class="timetable-wrapper">
          <el-scrollbar>
            <table class="timetable">
              <thead>
              <tr>
                <th class="time-header">时段</th>
                <th
                    v-for="day in weekDays"
                    :key="day.id"
                >{{ day.label }}
                </th>
              </tr>
              </thead>
              <tbody>
              <tr
                  v-for="(row, timeIndex) in scheduleTableData"
                  :key="timeIndex"
                  class="time-row"
              >
                <td class="time-cell">
                  <div class="time-info">
                    <div class="time-slot">{{ row.timeSlot }}</div>
                    <div class="time-range">{{ row.timeRange }}</div>
                  </div>
                </td>
                <td
                    v-for="day in weekDays"
                    :key="day.id"
                    class="course-cell"
                    @dragover.prevent
                    @drop="handleCellDrop($event, day.id, timeIndex)"
                >
                  <div class="course-cell-content">
                    <transition-group
                        name="list"
                        tag="div"
                        class="course-list"
                    >
                      <el-tag
                          v-for="course in row[day.id]"
                          :key="course.id"
                          class="course-card"
                          :draggable="true"
                          @dragstart="handleDragStart(course)"
                          @dragend="handleDragEnd"
                          effect="light"
                          :color="course.type === 'PE' ? '#67C23A' : course.type === 'lab' ? '#409EFF' : '#F5F7FA'"
                          :type="course.type === 'PE' ? 'success' : course.type === 'lab' ? 'primary' : 'info'"
                          closable
                          @close="deleteScheduledCourseFromTable(course, day.id, timeIndex)"
                      >
                        <div class="course-content">
                          <div class="course-name">{{ course.name }}</div>
                          <div class="course-info">
                            <span class="teacher">{{ course.teacher }}</span>
                            <span
                                class="classroom"
                                v-if="course.classroom"
                            >| {{
                                course.classroom
                              }}</span>
                          </div>
                        </div>
                      </el-tag>
                    </transition-group>
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </el-scrollbar>
        </div>

        <!-- 课程图例 -->
        <div class="course-legend">
          <div class="legend-title">课程类型</div>
          <div class="legend-items">
            <div class="legend-item">
              <div
                  class="legend-color"
                  style="background-color: #F5F7FA;"
              ></div>
              <div class="legend-label">理论课</div>
            </div>
            <div class="legend-item">
              <div
                  class="legend-color"
                  style="background-color: #409EFF;"
              ></div>
              <div class="legend-label">实验课</div>
            </div>
            <div class="legend-item">
              <div
                  class="legend-color"
                  style="background-color: #67C23A;"
              ></div>
              <div class="legend-label">体育课</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.auto-scheduling {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.scheduling-config {
  margin-bottom: 20px;
}

/* 排课模式卡片 */
.mode-select {
  height: 100%;
}

.card-header-primary {
  background: linear-gradient(90deg, #1e88e5 0%, #42a5f5 100%);
  border-radius: 8px 8px 0 0;
  padding: 12px 20px;
  color: #fff;
  font-weight: bold;
}

.mode-content {
  padding: 20px 0;
}

.mode-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 25px;
  width: 100%;
}

.mode-radio-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 4px;
}

.target-select {
  margin: 25px 0;
}

.select-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.option-with-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.option-tags {
  display: flex;
  gap: 5px;
}

.start-btn {
  margin-top: 20px;
  width: 100%;
  height: 44px;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.start-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.35);
}

/* 策略选择卡片 */
.strategy-select {
  height: 100%;
}

.strategy-container {
  padding: 10px 0;
}

.strategy-item {
  padding: 15px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  margin-bottom: 15px;
  transition: all 0.3s;
  display: flex;
  align-items: flex-start;
  cursor: pointer;
}

.strategy-item:hover {
  border-color: var(--el-color-primary-light-5);
  background-color: var(--el-color-primary-light-9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.strategy-item-selected {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.strategy-radio {
  width: 100%;
  display: flex;
  align-items: flex-start;
}

.radio-indicator {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 1px solid #DCDFE6;
  margin-right: 15px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.strategy-item-selected .radio-indicator {
  border-color: var(--el-color-primary);
}

.radio-inner {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
}

.strategy-info {
  width: 100%;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.strategy-name {
  font-weight: bold;
  font-size: 16px;
}

.strategy-desc {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 10px;
  line-height: 1.5;
}

.strategy-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.update-time {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  display: flex;
  align-items: center;
}

.strategy-config {
  display: flex;
  align-items: center;
  gap: 5px;
}

.el-radio {
  width: 100%;
  margin-right: 0;
}

.el-radio :deep(.el-radio__label) {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 卡片样式 */
.card-with-shadow {
  margin-bottom: 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-with-shadow:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header-gradient {
  background: linear-gradient(90deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px 8px 0 0;
  padding: 12px 20px;
  color: #333;
  font-weight: bold;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

/* 排课进度 */
.progress-info {
  padding: 20px;
}

.progress-container {
  position: relative;
  margin-bottom: 30px;
}

.progress-bar {
  margin-bottom: 5px;
}

.progress-percentage {
  position: absolute;
  top: -10px;
  right: 0;
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.current-step-container {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.step-indicator {
  margin-right: 15px;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.step-label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.step-value {
  color: #409EFF;
  font-weight: bold;
  font-size: 16px;
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.stat-item {
  padding: 15px;
  background: #f9f9f9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-item:hover {
  background: #f0f0f0;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin-right: 15px;
}

.stat-icon.completed {
  background: #67C23A;
}

.stat-icon.pending {
  background: #E6A23C;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.stat-value.completed {
  color: #67C23A;
}

.stat-value.pending {
  color: #E6A23C;
}

.error-message {
  margin-top: 20px;
}

/* 排课结果 */
.result-stats {
  padding: 20px;
}

.stat-cards {
  margin-bottom: 30px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;
  height: 100%;
  transition: all 0.3s ease;
  border-top: 4px solid #909399;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.total-courses {
  border-top-color: #409EFF;
}

.stat-card.scheduled-courses {
  border-top-color: #67C23A;
}

.stat-card.conflicts {
  border-top-color: #E6A23C;
}

.stat-card.utilization {
  border-top-color: #F56C6C;
}

.stat-card.total-courses :deep(.el-icon-notebook-1) {
  color: #409EFF;
  font-size: 22px;
}

.stat-card.scheduled-courses :deep(.el-icon-finished) {
  color: #67C23A;
  font-size: 22px;
}

.stat-card.conflicts :deep(.el-icon-warning) {
  color: #E6A23C;
  font-size: 22px;
}

.stat-card.utilization :deep(.el-icon-data-analysis) {
  color: #F56C6C;
  font-size: 22px;
}

.statistic-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.statistic-item :deep(.el-statistic__content) {
  font-size: 28px;
  color: #303133;
}

.statistic-item :deep(.el-statistic__title) {
  font-size: 15px;
  color: #606266;
}

.conflict-alert,
.unscheduled-alert {
  margin-bottom: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.conflicts-container {
  padding: 5px 0;
}

.conflict-item {
  padding: 10px 0;
  border-bottom: 1px dashed #ebeef5;
  display: flex;
  align-items: flex-start;
}

.conflict-item:last-child {
  border-bottom: none;
}

.conflict-icon {
  margin-right: 10px;
  color: #E6A23C;
  font-size: 18px;
}

.conflict-details {
  flex: 1;
}

.conflict-message {
  margin-bottom: 6px;
  color: #606266;
}

.ai-suggestion {
  color: #409EFF;
  font-style: italic;
  margin-top: 6px;
  padding-left: 20px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.ai-suggestion:hover {
  color: #66b1ff;
  transform: translateX(4px);
}

.unscheduled-count {
  color: #F56C6C;
  font-weight: bold;
}

.unscheduled-table {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
}

/* 课表样式 - 表格版 */
.timetable-container {
  padding: 16px;
}

.timetable-wrapper {
  margin-bottom: 20px;
  max-height: 600px;
  overflow: auto;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.timetable {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
}

.timetable thead {
  position: sticky;
  top: 0;
  z-index: 2;
}

.timetable th {
  background: linear-gradient(to bottom, #f5f7fa, #e4e7ed);
  color: #606266;
  font-weight: bold;
  padding: 16px 12px;
  text-align: center;
  border: 1px solid #EBEEF5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.time-header {
  width: 120px;
}

.time-cell {
  background-color: #F5F7FA;
  text-align: center;
  vertical-align: middle;
  border: 1px solid #EBEEF5;
  padding: 12px 8px;
  width: 120px;
  position: sticky;
  left: 0;
  z-index: 1;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.time-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time-slot {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.time-range {
  font-size: 12px;
  color: #909399;
}

.course-cell {
  border: 1px solid #EBEEF5;
  padding: 6px;
  vertical-align: top;
  height: 120px;
  transition: all 0.3s;
  position: relative;
}

.course-cell:hover {
  background-color: rgba(64, 158, 255, 0.05);
}

.course-cell-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.course-card {
  display: block;
  margin-bottom: 8px;
  cursor: move;
  transition: all 0.3s;
  width: 100%;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.course-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.course-content {
  padding: 6px;
  text-align: center;
}

.course-name {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 6px;
  color: #303133;
}

.course-info {
  font-size: 12px;
  color: #606266;
  display: flex;
  justify-content: center;
  gap: 4px;
}

.teacher {
  margin-right: 4px;
}

.classroom {
  margin-left: 4px;
}

/* 课程图例样式 */
.course-legend {
  display: flex;
  align-items: center;
  gap: 24px;
  background: #F5F7FA;
  padding: 12px 20px;
  border-radius: 6px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.legend-title {
  font-weight: bold;
  color: #606266;
}

.legend-items {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
}

.legend-label {
  font-size: 13px;
  color: #606266;
}

/* 增强动画效果 */
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

.list-move {
  transition: transform 0.5s ease;
}

/* Export按钮动画 */
.export-btn {
  transition: all 0.3s;
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.5);
}

/* 配置卡片动画 */
.config-fade-enter-active,
.config-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.config-fade-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.config-fade-leave-to {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.config-fade-enter-to,
.config-fade-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .timetable {
    min-width: 900px;
  }

  .scheduling-config .el-col {
    width: 100%;
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .progress-stats {
    grid-template-columns: 1fr;
  }

  .stat-cards .el-col {
    width: 100%;
    margin-bottom: 16px;
  }

  .course-legend {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

/* 鼠标悬停在时间段效果 */
.time-row:hover .time-cell {
  background-color: #e6e9ed;
}

/* 时间段高亮 */
.time-row:nth-child(odd) .time-cell {
  background-color: #f0f2f5;
}

/* 为拖放添加虚线指示 */
.course-cell.drag-over {
  background-color: rgba(64, 158, 255, 0.1);
  border: 2px dashed #409EFF;
}

/* 未排课程列表 */
.unscheduled-table :deep(.el-table__row .el-button) {
  transition: all 0.3s;
}

.unscheduled-table :deep(.el-table__row .el-button:hover) {
  transform: rotate(15deg);
}
</style>
