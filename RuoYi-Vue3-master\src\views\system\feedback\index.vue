<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryRef" :inline="true">
        <el-form-item label="反馈状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="待处理" value="0" />
            <el-option label="处理中" value="1" />
            <el-option label="已完成" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈类型">
          <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
            <el-option label="功能建议" value="0" />
            <el-option label="问题反馈" value="1" />
            <el-option label="其他" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 反馈列表 -->
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">反馈列表</span>
          <el-button type="primary" icon="Plus" @click="handleAdd">新增反馈</el-button>
        </div>
      </template>

      <el-table v-loading="loading" :data="feedbackList" stripe>
        <el-table-column label="反馈标题" prop="title" show-overflow-tooltip />
        <el-table-column label="反馈类型" prop="type" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTag(scope.row.type)">
              {{ getTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="提交时间" prop="createTime" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">回复</el-button>
            <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
          v-if="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改反馈对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="feedbackRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="反馈标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入反馈标题" />
        </el-form-item>
        <el-form-item label="反馈类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择反馈类型">
            <el-option label="功能建议" value="0" />
            <el-option label="问题反馈" value="1" />
            <el-option label="其他" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈内容" prop="content">
          <el-input
              v-model="form.content"
              type="textarea"
              placeholder="请输入反馈内容"
              :rows="4"
          />
        </el-form-item>
        <el-form-item label="附件">
          <el-upload
              class="upload-demo"
              action="/upload"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              multiple
              :limit="3"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 jpg/png/pdf 文件，且不超过 5MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  status: '',
  type: ''
})

// 总条数
const total = ref(0)
// 遮罩层
const loading = ref(false)
// 弹出层标题
const title = ref('')
// 是否显示弹出层
const open = ref(false)
// 表单参数
const form = reactive({
  id: undefined,
  title: '',
  type: '',
  content: '',
  status: '0'
})

// 表单校验规则
const rules = {
  title: [{ required: true, message: '请输入反馈标题', trigger: 'blur' }],
  type: [{ required: true, message: '请选择反馈类型', trigger: 'change' }],
  content: [{ required: true, message: '请输入反馈内容', trigger: 'blur' }]
}

// 模拟数据
const feedbackList = ref([
  {
    id: 1,
    title: '系统功能建议',
    type: '0',
    status: '0',
    createTime: '2024-01-20 10:00:00'
  }
])

// 获取类型标签样式
const getTypeTag = (type) => {
  const types = {
    '0': 'success',
    '1': 'warning',
    '2': 'info'
  }
  return types[type]
}

// 获取类型名称
const getTypeName = (type) => {
  const types = {
    '0': '功能建议',
    '1': '问题反馈',
    '2': '其他'
  }
  return types[type]
}

// 获取状态标签样式
const getStatusTag = (status) => {
  const statuses = {
    '0': 'info',
    '1': 'warning',
    '2': 'success'
  }
  return statuses[status]
}

// 获取状态名称
const getStatusName = (status) => {
  const statuses = {
    '0': '待处理',
    '1': '处理中',
    '2': '已完成'
  }
  return statuses[status]
}

// 查询列表
const getList = () => {
  loading.value = true
  // 这里添加实际的API调用
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 表单重置
const reset = () => {
  form.id = undefined
  form.title = ''
  form.type = ''
  form.content = ''
  form.status = '0'
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.status = ''
  queryParams.type = ''
  handleQuery()
}

// 新增按钮操作
const handleAdd = () => {
  reset()
  open.value = true
  title.value = '添加反馈'
}

// 修改按钮操作
const handleUpdate = (row) => {
  reset()
  open.value = true
  title.value = '回复反馈'
  // 这里添加获取详情的API调用
}

// 提交按钮操作
const submitForm = () => {
  // 这里添加提交的API调用
  ElMessage.success('操作成功')
  open.value = false
  getList()
}

// 删除按钮操作
const handleDelete = (row) => {
  ElMessageBox.confirm('是否确认删除该反馈?', '警告', {
    type: 'warning'
  }).then(() => {
    // 这里添加删除的API调用
    ElMessage.success('删除成功')
    getList()
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .search-card {
    margin-bottom: 20px;
    .el-form {
      margin-bottom: -18px;
    }
  }

  .list-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  :deep(.el-card__header) {
    padding: 15px 20px;
  }

  :deep(.el-card__body) {
    padding: 20px;
  }

  .el-table {
    margin: 15px 0;
  }

  .dialog-footer {
    text-align: center;
    padding-top: 20px;
  }
}
</style>