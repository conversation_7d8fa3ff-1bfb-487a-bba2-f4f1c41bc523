<template>
  <div class="birds-container" ref="birdsContainer">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'VantaBirds',
  props: {
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      vantaEffect: null,
      loading: true,
      error: null,
      defaultOptions: {
        mouseControls: true,
        touchControls: true,
        gyroControls: false,
        minHeight: 200.00,
        minWidth: 200.00,
        scale: 1.00,
        scaleMobile: 1.00,
        backgroundColor: 0x000000,
        color1: 0xff0000,
        color2: 0x00ffff
      }
    }
  },
  computed: {
    mergedOptions() {
      return { ...this.defaultOptions, ...this.options };
    }
  },
  mounted() {
    // 确保THREE和VANTA已经加载
    this.loadScripts().then(() => {
      this.initVantaEffect();
      this.loading = false;
    }).catch(error => {
      console.error('加载VANTA脚本失败:', error);
      this.error = '加载背景动画失败，请刷新页面重试';
      this.loading = false;
    });
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 组件卸载前清理资源
    if (this.vantaEffect) {
      this.vantaEffect.destroy();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      // 当窗口大小变化时，重新初始化效果以适应新尺寸
      if (this.vantaEffect && window.VANTA) {
        this.vantaEffect.resize();
      }
    },
    loadScripts() {
      return new Promise((resolve, reject) => {
        // 设置加载超时
        const timeout = setTimeout(() => {
          reject(new Error('加载脚本超时'));
        }, 10000);
        
        // 检查THREE.js是否已加载
        if (window.THREE) {
          this.loadVanta().then(() => {
            clearTimeout(timeout);
            resolve();
          }).catch(reject);
        } else {
          // 加载THREE.js
          const threeScript = document.createElement('script');
          threeScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r121/three.min.js';
          threeScript.onload = () => {
            this.loadVanta().then(() => {
              clearTimeout(timeout);
              resolve();
            }).catch(reject);
          };
          threeScript.onerror = reject;
          document.head.appendChild(threeScript);
        }
      });
    },
    loadVanta() {
      return new Promise((resolve, reject) => {
        // 检查VANTA是否已加载
        if (window.VANTA) {
          resolve();
        } else {
          // 加载VANTA Birds
          const vantaScript = document.createElement('script');
          vantaScript.src = 'https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.birds.min.js';
          vantaScript.onload = resolve;
          vantaScript.onerror = reject;
          document.head.appendChild(vantaScript);
        }
      });
    },
    initVantaEffect() {
      try {
        const options = {
          ...this.mergedOptions,
          el: this.$refs.birdsContainer
        };
        
        this.vantaEffect = window.VANTA.BIRDS(options);
      } catch (err) {
        console.error('初始化VANTA效果失败:', err);
        this.error = '初始化背景动画失败';
      }
    }
  },
  watch: {
    // 监听options变化，重新初始化效果
    options: {
      deep: true,
      handler() {
        if (this.vantaEffect) {
          this.vantaEffect.destroy();
          this.initVantaEffect();
        }
      }
    }
  }
}
</script>

<style scoped>
.birds-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-height: 200px;
  overflow: hidden;
  background: linear-gradient(to bottom right, #0a192f, #172a45);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(10, 25, 47, 0.8);
  z-index: 5;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(100, 255, 218, 0.3);
  border-radius: 50%;
  border-top-color: #64ffda;
  animation: spin 1s linear infinite;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff6b6b;
  background-color: rgba(10, 25, 47, 0.9);
  padding: 1.5rem;
  border-radius: 8px;
  z-index: 5;
  font-size: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>