import request from '@/utils/request'

// 查询教学楼信息列表
export function listBuilding(query) {
  return request({
    url: '/system/building/list',
    method: 'get',
    params: query
  })
}

// 查询教学楼信息详细
export function getBuilding(buildingCode) {
  return request({
    url: '/system/building/' + buildingCode,
    method: 'get'
  })
}

// 新增教学楼信息
export function addBuilding(data) {
  return request({
    url: '/system/building',
    method: 'post',
    data: data
  })
}

// 修改教学楼信息
export function updateBuilding(data) {
  return request({
    url: '/system/building',
    method: 'put',
    data: data
  })
}

// 删除教学楼信息
export function delBuilding(buildingCode) {
  return request({
    url: '/system/building/' + buildingCode,
    method: 'delete'
  })
}
