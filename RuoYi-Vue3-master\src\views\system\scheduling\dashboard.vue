<script setup>
import * as echarts from "echarts"
import { onMounted, ref, watch, computed, nextTick, onUnmounted } from "vue"
import { ElMessage } from "element-plus"

// API接口预留
// import { fetchDashboardStats, fetchWeeklySchedule, fetchCourseDistribution, fetchClassroomUsage, fetchTeacherHours } from '@/api/scheduling'

// 修改
// 图表实例
let courseDistributionChart = null
let classroomUsageChart = null
let teacherHoursChart = null
let usageProgressChart = null
// 课程分布图当前激活的图表类型
const activeChartType = ref('pie')
// 教室使用情况图当前激活的图表类型
const classroomChartType = ref('bar')
// 教师课时图当前激活的图表类型
const teacherChartType = ref('line')

// 基础统计数据
const totalCourses = ref(150)
const scheduledCourses = ref(145)
const classroomUtilization = ref(85.5)
const averageTeacherHours = ref(16)

// 视图控制
const activeView = ref("class")
const analysisView = ref("analysis")
const weekDays = ["周一", "周二", "周三", "周四", "周五", "周六"]

// 选择框数据
const selectedClass = ref(1)
const selectedClassroom = ref(1)

// 模拟班级和教室数据
const classes = ref([{
  id: 1,
  name: '计算机科学1班'
}, {
  id: 2,
  name: '计算机科学2班'
}, {
  id: 3,
  name: '软件工程1班'
}, {
  id: 4,
  name: '软件工程2班'
}])

const classrooms = ref([{
  id: 1,
  name: '教学楼101'
}, {
  id: 2,
  name: '教学楼102'
}, {
  id: 3,
  name: '实验楼301'
}, {
  id: 4,
  name: '实验楼302'
}])

// 课程分布数据
const courseDistributionData = [{
  value: 45,
  name: "公共基础课"
}, {
  value: 40,
  name: "专业必修课"
}, {
  value: 35,
  name: "专业选修课"
}, {
  value: 20,
  name: "通识选修课"
}, {
  value: 10,
  name: "实验实践课"
}]

// 教室使用情况数据
const classroomUsageData = {
  buildings: ["教学楼A", "教学楼B", "实验楼A", "实验楼B", "图书馆", "综合楼"],
  usage: [92, 85, 78, 88, 65, 73]
}

// 教师课时分布数据
const teacherHoursData = {
  teachers: ["张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十", "郑十一"],
  hours: [18, 16, 14, 20, 12, 15, 17, 13, 16]
}

// 班级课表数据
const classSchedule = ref([
  {
    time: `<p>第一节</p><p>(8:00-9:40)</p>`,
    monday: [{ id: 1, name: "高等数学A", teacher: "张三", room: "教学楼101" }],
    tuesday: [{ id: 2, name: "大学物理", teacher: "李四", room: "教学楼202" }],
    wednesday: [{ id: 3, name: "C语言程序设计", teacher: "王五", room: "实验楼303" }],
    thursday: [{ id: 4, name: "线性代数", teacher: "赵六", room: "教学楼104" }],
    friday: [{ id: 5, name: "大学英语(1)", teacher: "钱七", room: "语音室205" }]
  },
  {
    time: `<p>第二节</p><p>(10:00-11:40)</p>`,
    monday: [{ id: 6, name: "数据结构", teacher: "孙八", room: "教学楼106" }],
    tuesday: [{ id: 7, name: "计算机网络", teacher: "周九", room: "实验楼307" }],
    wednesday: [{ id: 16, name: "概率论", teacher: "郑十一", room: "教学楼205" }],
    thursday: [{ id: 8, name: "操作系统", teacher: "吴十", room: "教学楼108" }],
    friday: [{ id: 9, name: "软件工程", teacher: "郑十一", room: "教学楼109" }]
  },
  {
    time: `<p>第三节</p><p>(14:00-15:40)</p>`,
    monday: [{ id: 10, name: "数据库原理", teacher: "王五", room: "实验楼305" }],
    tuesday: [{ id: 17, name: "计算机组成原理", teacher: "张三", room: "教学楼110" }],
    wednesday: [{ id: 11, name: "离散数学", teacher: "李四", room: "教学楼110" }],
    thursday: [{ id: 12, name: "编译原理", teacher: "李四", room: "教学楼111" }],
    friday: [{ id: 18, name: "高等数学B", teacher: "张三", room: "教学楼102" }]
  },
  {
    time: `<p>第四节</p><p>(16:00-17:40)</p>`,
    monday: [{ id: 19, name: "数字电路", teacher: "周九", room: "实验楼301" }],
    tuesday: [{ id: 13, name: "人工智能导论", teacher: "赵六", room: "实验楼306" }],
    wednesday: [{ id: 14, name: "机器学习", teacher: "钱七", room: "教学楼112" }],
    thursday: [{ id: 20, name: "算法设计", teacher: "孙八", room: "教学楼107" }],
    friday: [{ id: 15, name: "深度学习", teacher: "孙八", room: "实验楼308" }]
  },
  {
    time: `<p>第五节</p><p>(19:00-20:40)</p>`,
    monday: [{ id: 21, name: "计算机网络", teacher: "周九", room: "实验楼301" }],
    tuesday: [{ id: 22, name: "操作系统", teacher: "赵六", room: "教学楼102" }],
    wednesday: [{ id: 23, name: "数据库原理", teacher: "钱七", room: "实验楼305" }],
    thursday: [{ id: 24, name: "计算机网络", teacher: "孙八", room: "教学楼106" }],
    friday: [{ id: 25, name: "操作系统", teacher: "孙八", room: "教学楼108" }]
  },
  {
    time: `<p>第六节</p><p>(21:00-22:40)</p>`,
    monday: [{ id: 26, name: "计算机网络", teacher: "周九", room: "实验楼301" }],
    tuesday: [{ id: 27, name: "操作系统", teacher: "赵六", room: "教学楼102" }],
    wednesday: [{ id: 28, name: "数据库原理", teacher: "钱七", room: "实验楼305" }],
    thursday: [{ id: 29, name: "计算机网络", teacher: "孙八", room: "教学楼106" }],
    friday: [{ id: 30, name: "操作系统", teacher: "孙八", room: "教学楼108" }]
  },
  {
    time: `<p>第七节</p><p>(23:00-24:40)</p>`,
    monday: [{ id: 31, name: "计算机网络", teacher: "周九", room: "实验楼301" }],
    tuesday: [{ id: 32, name: "操作系统", teacher: "赵六", room: "教学楼102" }],
    wednesday: [{ id: 33, name: "数据库原理", teacher: "钱七", room: "实验楼305" }],
    thursday: [{ id: 34, name: "计算机网络", teacher: "孙八", room: "教学楼106" }],
    friday: []
  }
])

// 教室课表数据
const roomSchedule = ref([
  {
    time: `<p>第一节</p><p>(8:00-9:40)</p>`,
    monday: [{ id: 1, name: "高等数学A", teacher: "张三", className: "计算机科学1班" }],
    tuesday: [{ id: 2, name: "大学物理", teacher: "李四", className: "软件工程1班" }],
    wednesday: [{ id: 3, name: "C语言程序设计", teacher: "王五", className: "计算机科学2班" }],
    thursday: [{ id: 4, name: "线性代数", teacher: "赵六", className: "软件工程2班" }],
    friday: [{ id: 5, name: "大学英语(1)", teacher: "钱七", className: "计算机科学1班" }]
  },
  {
    time: `<p>第二节</p><p>(10:00-11:40)</p>`,
    monday: [{ id: 6, name: "数据结构", teacher: "孙八", className: "软件工程1班" }],
    tuesday: [{ id: 7, name: "计算机网络", teacher: "周九", className: "计算机科学2班" }],
    wednesday: [{ id: 16, name: "概率论", teacher: "郑十一", className: "软件工程2班" }],
    thursday: [{ id: 8, name: "操作系统", teacher: "吴十", className: "计算机科学1班" }],
    friday: [{ id: 9, name: "软件工程", teacher: "郑十一", className: "软件工程1班" }]
  },
  {
    time: `<p>第三节</p><p>(14:00-15:40)</p>`,
    monday: [{ id: 10, name: "数据库原理", teacher: "王五", className: "计算机科学2班" }],
    tuesday: [{ id: 17, name: "计算机组成原理", teacher: "张三", className: "软件工程2班" }],
    wednesday: [{ id: 11, name: "离散数学", teacher: "李四", className: "计算机科学1班" }],
    thursday: [{ id: 12, name: "编译原理", teacher: "李四", className: "软件工程1班" }],
    friday: [{ id: 18, name: "高等数学B", teacher: "张三", className: "计算机科学2班" }]
  },
  {
    time: `<p>第四节</p><p>(16:00-17:40)</p>`,
    monday: [{ id: 19, name: "数字电路", teacher: "周九", className: "软件工程2班" }],
    tuesday: [{ id: 13, name: "人工智能导论", teacher: "赵六", className: "计算机科学1班" }],
    wednesday: [{ id: 14, name: "机器学习", teacher: "钱七", className: "软件工程1班" }],
    thursday: [{ id: 20, name: "算法设计", teacher: "孙八", className: "计算机科学2班" }],
    friday: [{ id: 15, name: "深度学习", teacher: "孙八", className: "软件工程2班" }]
  },
  {
    time: `<p>第五节</p><p>(19:00-20:40)</p>`,
    monday: [{ id: 21, name: "计算机网络", teacher: "周九", className: "软件工程2班" }],
    tuesday: [{ id: 22, name: "操作系统", teacher: "赵六", className: "计算机科学1班" }],
    wednesday: [{ id: 23, name: "数据库原理", teacher: "钱七", className: "软件工程1班" }],
    thursday: [{ id: 24, name: "计算机网络", teacher: "孙八", className: "计算机科学2班" }],
    friday: [{ id: 25, name: "操作系统", teacher: "孙八", className: "软件工程2班" }]
  },
  {
    time: `<p>第六节</p><p>(21:00-22:40)</p>`,
    monday: [{ id: 26, name: "计算机网络", teacher: "周九", className: "软件工程2班" }],
    tuesday: [{ id: 27, name: "操作系统", teacher: "赵六", className: "计算机科学1班" }],
    wednesday: [{ id: 28, name: "数据库原理", teacher: "钱七", className: "软件工程1班" }],
    thursday: [{ id: 29, name: "计算机网络", teacher: "孙八", className: "计算机科学2班" }],
    friday: [{ id: 30, name: "操作系统", teacher: "孙八", className: "软件工程2班" }]
  },
  {
    time: `<p>第七节</p><p>(23:00-24:40)</p>`,
    monday: [{ id: 31, name: "计算机网络", teacher: "周九", className: "软件工程2班" }],
    tuesday: [{ id: 32, name: "操作系统", teacher: "赵六", className: "计算机科学1班" }],
    wednesday: [{ id: 33, name: "数据库原理", teacher: "钱七", className: "软件工程1班" }],
    thursday: [{ id: 34, name: "计算机网络", teacher: "孙八", className: "计算机科学2班" }],
  }
])

// 计算当前显示的课表数据
const currentSchedule = computed(() => {
  const schedule = activeView.value === 'class' ? classSchedule.value : roomSchedule.value

  // 根据选择的班级或教室过滤数据
  if (activeView.value === 'class' && selectedClass.value) {
    // 这里应该根据实际需求过滤班级课表数据
    return schedule
  } else if (activeView.value === 'classroom' && selectedClassroom.value) {
    // 这里应该根据实际需求过滤教室课表数据
    return schedule
  }
  return schedule
})

// 监听选择变化
watch([selectedClass, selectedClassroom], async ([newClass, newClassroom], [oldClass, oldClassroom]) => {
  if (newClass !== oldClass || newClassroom !== oldClassroom) {
    // await updateDashboardData()
  }
}, { immediate: true })

// 监听标签页变化
watch(analysisView, (newVal) => {
  if (newVal === 'analysis') {
    nextTick(() => {
      initCharts()
    })
  }
})

// 更新仪表盘数据的方法
// const updateDashboardData = async () => {
// try {
// 这里预留API调用接口
/* 实际项目中应该这样调用：
const params = {
  classId: selectedClass.value,
  classroomId: selectedClassroom.value
}

const [
  scheduleData,
  statsData,
  distributionData,
  usageData,
  hoursData
] = await Promise.all([
  fetchWeeklySchedule(params),
  fetchDashboardStats(params),
  fetchCourseDistribution(params),
  fetchClassroomUsage(params),
  fetchTeacherHours(params)
])

// 更新各个数据
weeklySchedule.value = scheduleData
totalCourses.value = statsData.totalCourses
scheduledCourses.value = statsData.scheduledCourses
classroomUtilization.value = statsData.utilization
averageTeacherHours.value = statsData.averageHours

// 更新图表数据
updateChartData({
  courseDistribution: distributionData,
天气按钮      classroomUsage: usageData,
  teacherHours: hoursData
})
*/

// 临时模拟数据更新
//         console.log('更新数据:', {
//             classId: selectedClass.value,
//             classroomId: selectedClassroom.value
//         })
//     } catch (error) {
//         console.error('获取数据失败:', error)
//         ElMessage.error('数据更新失败')
//     }
// }

// 更新图表数据的方法
const updateChartData = (data) => {
  // 更新课程分布图表
  if (courseDistributionChart) {
    courseDistributionChart.setOption({
      series: [{
        data: data.courseDistribution
      }]
    })
  }

  // 更新教室使用率图表
  if (classroomUsageChart) {
    classroomUsageChart.setOption({
      xAxis: {
        data: data.classroomUsage.buildings
      },
      series: [{
        data: data.classroomUsage.usage
      }]
    })
  }

  // 更新教师课时图表
  if (teacherHoursChart) {
    teacherHoursChart.setOption({
      xAxis: {
        data: data.teacherHours.teachers
      },
      series: [{
        data: data.teacherHours.hours
      }]
    })
  }
}


// 图表相关
// let courseDistributionChart = null
// let classroomUsageChart = null
// let teacherHoursChart = null

// // 更新课程分布数据使其更合理
// const courseDistributionData = [
//     { value: 45, name: "公共基础课" },
//     { value: 40, name: "专业必修课" },
//     { value: 35, name: "专业选修课" },
//     { value: 20, name: "通识选修课" },
//     { value: 10, name: "实验实践课" }
// ]

// // 更新教室使用情况数据
// const classroomUsageData = {
//     buildings: ["教学楼A", "教学楼B", "实验楼A", "实验楼B", "图书馆", "综合楼"],
//     usage: [92, 85, 78, 88, 65, 73]
// }

// // 更新教师课时分布数据
// const teacherHoursData = {
//     teachers: ["张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十", "郑十一"],
//     hours: [18, 16, 14, 20, 12, 15, 17, 13, 16]
// }


// 切换班级课表还是教室课表
const toggleView = () => {
  // 切换视图前先重置选择
  selectedClass.value = 1
  selectedClassroom.value = 1

  // 切换视图
  activeView.value = activeView.value === 'class' ? 'classroom' : 'class'
}

// 修改
// 切换图表类型的方法
const switchChartType = (type) => {
  activeChartType.value = type

  if (courseDistributionChart) {
    courseDistributionChart.clear()
    courseDistributionChart.setOption(getCourseDistributionOption(type))
  }
}
// 切换教室使用情况图表类型
const switchClassroomChartType = (type) => {
  classroomChartType.value = type

  if (classroomUsageChart) {
    classroomUsageChart.clear()
    classroomUsageChart.setOption(getClassroomUsageOption(type))
  }
}

// 切换教师课时图表类型
const switchTeacherChartType = (type) => {
  teacherChartType.value = type

  if (teacherHoursChart) {
    teacherHoursChart.clear()
    teacherHoursChart.setOption(getTeacherHoursOption(type))
  }
}
// 获取课程分布图表配置的方法
const getCourseDistributionOption = (type) => {
  // 所有图表类型的通用配置
  const commonOption = {
    tooltip: {},
    legend: {
      orient: 'horizontal',
      bottom: 0,
      left: 'center',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12
      }
    }
  }

  // 特定类型的配置
  if (type === 'pie') {
    return {
      ...commonOption,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}门 ({d}%)'
      },
      series: [{
        name: '课程分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 4
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c}门',
          // offset: [0, -100] // [x轴移量, y轴偏移量]
        },
        labelLine: {
          show: true
        },
        data: courseDistributionData
      }]
    }
  } else if (type === 'bar') {
    return {
      ...commonOption,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        top: '10%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: courseDistributionData.map(item => item.name),
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}门'
        }
      },
      series: [{
        name: '课程数量',
        type: 'bar',
        data: courseDistributionData.map(item => item.value),
        itemStyle: {
          color: '#409EFF'
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}门'
        }
      }]
    }
  } else if (type === 'line') {
    return {
      ...commonOption,
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        top: '10%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: courseDistributionData.map(item => item.name),
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}门'
        }
      },
      series: [{
        name: '课程数量',
        type: 'line',
        data: courseDistributionData.map(item => item.value),
        itemStyle: {
          color: '#409EFF'
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}门'
        },
        smooth: true
      }]
    }
  }
}
// 下方echarts
// 获取教室使用情况图表配置的方法
const getClassroomUsageOption = (type) => {
  // 通用配置
  const commonOption = {
    title: {
      // text: "教室使用率分布",
      left: "center",
      top: 10
    },
    tooltip: {},
    grid: {
      top: '15%',
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }

  if (type === 'bar') {
    return {
      ...commonOption,
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "shadow" }
      },
      xAxis: {
        type: "category",
        data: classroomUsageData.buildings,
        axisLabel: { interval: 0, rotate: 30 }
      },
      yAxis: {
        type: "value",
        max: 100,
        axisLabel: { formatter: "{value}%" }
      },
      series: [{
        name: "使用率",
        type: "bar",
        data: classroomUsageData.usage,
        itemStyle: { color: "#409EFF" },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%'
        }
      }]
    }
  } else if (type === 'line') {
    return {
      ...commonOption,
      tooltip: {
        trigger: "axis"
      },
      xAxis: {
        type: "category",
        data: classroomUsageData.buildings,
        axisLabel: { interval: 0, rotate: 30 }
      },
      yAxis: {
        type: "value",
        max: 100,
        axisLabel: { formatter: "{value}%" }
      },
      series: [{
        name: "使用率",
        type: "line",
        data: classroomUsageData.usage,
        itemStyle: { color: "#409EFF" },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%'
        },
        smooth: true
      }]
    }
  } else if (type === 'pie') {
    return {
      ...commonOption,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}% ({d}%)'
      },
      series: [{
        name: '使用率',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 4
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c}%'
        },
        labelLine: {
          show: true
        },
        data: classroomUsageData.buildings.map((building, index) => {
          return {
            name: building,
            value: classroomUsageData.usage[index]
          }
        })
      }]
    }
  }
}

// 获取教师课时图表配置的方法
const getTeacherHoursOption = (type) => {
  // 通用配置
  const commonOption = {
    title: {
      // text: "教师周课时分布",
      left: "center",
      // top: 10
    },
    tooltip: {},
    grid: {
      top: '15%',
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }

  if (type === 'line') {
    return {
      ...commonOption,
      tooltip: {
        trigger: "axis"
      },
      xAxis: {
        type: "category",
        data: teacherHoursData.teachers,
        axisLabel: { interval: 0, rotate: 30 }
      },
      yAxis: {
        type: "value",
        axisLabel: { formatter: "{value}课时" }
      },
      series: [{
        name: "周课时",
        type: "line",
        data: teacherHoursData.hours,
        label: {
          // color: "#409EFF",
          show: true,
          position: 'top'
        },
        markLine: {
          data: [{
            type: "average",
            name: "平均值"
          }]
        }
      }]
    }
  } else if (type === 'bar') {
    return {
      ...commonOption,
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "shadow" }
      },
      xAxis: {
        type: "category",
        data: teacherHoursData.teachers,
        axisLabel: { interval: 0, rotate: 30 }
      },
      yAxis: {
        type: "value",
        axisLabel: { formatter: "{value}课时" }
      },
      series: [{
        name: "周课时",
        type: "bar",
        data: teacherHoursData.hours,
        itemStyle: { color: "#409EFF" },
        label: {
          show: true,
          position: 'top'
        }
      }]
    }
  } else if (type === 'pie') {
    return {
      ...commonOption,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}课时 ({d}%)'
      },
      series: [{
        name: '课时分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 4
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c}课时'
        },
        labelLine: {
          show: true
        },
        data: teacherHoursData.teachers.map((teacher, index) => {
          return {
            name: teacher,
            value: teacherHoursData.hours[index]
          }
        })
      }]
    }
  }
}
// 下方echarts

// 初始化所有图表
// 初始化所有图表
const initCharts = () => {
  // 课程分布图表
  const courseDistributionDom = document.getElementById('courseDistributionChart')
  if (courseDistributionDom) {
    if (courseDistributionChart) {
      courseDistributionChart.dispose()
    }
    courseDistributionChart = echarts.init(courseDistributionDom)
    courseDistributionChart.setOption(getCourseDistributionOption(activeChartType.value))
  }

  // 教室使用情况图表
  const classroomUsageDom = document.getElementById('classroomUsageChart')
  if (classroomUsageDom) {
    if (classroomUsageChart) {
      classroomUsageChart.dispose()
    }
    classroomUsageChart = echarts.init(classroomUsageDom)
    classroomUsageChart.setOption(getClassroomUsageOption(classroomChartType.value))
  }

  // 教师课时图表
  const teacherHoursDom = document.getElementById('teacherHoursChart')
  if (teacherHoursDom) {
    if (teacherHoursChart) {
      teacherHoursChart.dispose()
    }
    teacherHoursChart = echarts.init(teacherHoursDom)
    teacherHoursChart.setOption(getTeacherHoursOption(teacherChartType.value))
  }
}


// 窗口大小改变时重绘图表
function handleResize () {
  courseDistributionChart?.resize()
  classroomUsageChart?.resize()
  teacherHoursChart?.resize()
  usageProgressChart?.resize()
}


// 生命周期钩子
onMounted(() => {
  // 实际项目中这里应该调用接口获取数据
  /*
  const fetchDashboardData = async () => {
    try {
      const [
        statsData,
        scheduleData,
        distributionData,
        usageData,
        hoursData
      ] = await Promise.all([
        fetchDashboardStats(),
        fetchWeeklySchedule(),
        fetchCourseDistribution(),
        fetchClassroomUsage(),
        fetchTeacherHours()
      ])

      // 更新各个数据
      totalCourses.value = statsData.totalCourses
      scheduledCourses.value = statsData.scheduledCourses
      // ... 其他数据更新
    } catch (error) {
      console.error('获取数据失败:', error)
    }
  }

  fetchDashboardData()
  */

  // 组件挂载后初始化图表
  nextTick(() => {
    initCharts()

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)
  })
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener("resize", handleResize)
  // 销毁图表以防止内存泄漏
  courseDistributionChart?.dispose()
  classroomUsageChart?.dispose()
  teacherHoursChart?.dispose()
  usageProgressChart?.dispose()
})
</script>

<template>
  <div class="dashboard-container">
    <!-- 顶部统计卡片 -->
    <!-- !设置了el-row的高度 -->
    <el-row
        :gutter="20"
        style="height: 410px;"
    >
      <!-- Left Side: Course Distribution Chart -->
      <el-col :span="12">
        <el-card
            class="chart-panel echart-card"
            shadow="hover"
        >
          <div class="card-header">
            <span style="color: #409EFF;">课程类型分布</span>
            <div class="chart-type-buttons">
              <button
                  class="chart-type-btn"
                  :class="{ active: activeChartType === 'pie' }"
                  @click="switchChartType('pie')"
              >
                <!-- *饼状 -->
                <svg
                    t="1741914733616"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="5460"
                    width="20"
                    height="20"
                >
                  <path
                      d="M928 512h-128a288 288 0 0 0-288-288V96a416 416 0 0 1 416 416z"
                      fill="#4A8BFE"
                      p-id="5461"
                  ></path>
                  <path
                      d="M224 512H96A416 416 0 0 1 512 96v128a288 288 0 0 0-288 288z"
                      fill="#3BD5B3"
                      p-id="5462"
                  ></path>
                  <path
                      d="M512 928A416 416 0 0 1 96 512h128a288 288 0 0 0 288 288z"
                      fill="#4A8BFE"
                      p-id="5463"
                  ></path>
                  <path
                      d="M512 928v-128a288 288 0 0 0 288-288h128a416 416 0 0 1-416 416z"
                      fill="#3BD5B3"
                      p-id="5464"
                  ></path>
                </svg>
              </button>
              <button
                  class="chart-type-btn"
                  :class="{ active: activeChartType === 'bar' }"
                  @click="switchChartType('bar')"
              >
                <!-- *柱状 -->
                <svg
                    t="1741915357943"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="7800"
                    width="20"
                    height="20"
                >
                  <path
                      d="M121.5275 552.455l690.0440625-447.9375 45.2784375 69.751875-690.0440625 447.9375z"
                      fill="#A5E8D0"
                      p-id="7801"
                      data-spm-anchor-id="a313x.search_index.0.i16.78fb3a817zllBt"
                      class="selected"
                  ></path>
                  <path
                      d="M277.71875 461.75h-93.1875c-34.6875 0-62.90625 28.78125-62.90625 64.125v315.9375c0 35.34375 28.21875 64.125 62.90625 64.125h93.1875c34.6875 0 62.90625-28.78125 62.90625-64.125v-315.9375c0-35.34375-28.21875-64.125-62.90625-64.125z m-7.03125 372.9375h-79.21875V533h79.21875v301.6875zM544.15625 276.5h-80.71875c-38.15625 0-69.1875 31.59375-69.1875 70.5v488.4375c0 38.90625 31.03125 70.5 69.1875 70.5h80.71875c38.15625 0 69.1875-31.59375 69.1875-70.5V347c0-38.90625-31.03125-70.5-69.1875-70.5z m-0.75 558.1875l-79.21875 0.75-0.75-487.6875 79.96875-0.75v487.6875zM811.71875 95.9375h-70.40625c-40.96875 0-74.34375 33.9375-74.34375 75.75v658.5c0 41.8125 33.375 75.75 74.34375 75.75h70.40625c40.96875 0 74.34375-34.03125 74.34375-75.75V171.6875c0-41.71875-33.375-75.75-74.34375-75.75z m4.40625 734.25c0 2.4375-1.96875 4.5-4.40625 4.5h-70.40625c-2.4375 0-4.40625-2.0625-4.40625-4.5V171.6875c0-2.4375 1.96875-4.5 4.40625-4.5h70.40625c2.4375 0 4.40625 1.96875 4.40625 4.5v658.5z"
                      fill="#3fafea"
                      p-id="7802"
                      data-spm-anchor-id="a313x.search_index.0.i11.78fb3a817zllBt"
                      class=""
                  ></path>
                </svg>
              </button>
              <button
                  class="chart-type-btn"
                  :class="{ active: activeChartType === 'line' }"
                  @click="switchChartType('line')"
              >

                <!-- *折线 -->
                <svg
                    t="1741915155992"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="5201"
                    width="20"
                    height="20"
                >
                  <path
                      d="M358.4 153.6l92.16 327.68c5.12 10.24-5.12 25.6-15.36 30.72H291.84c-15.36 0-25.6-10.24-25.6-25.6v-5.12L358.4 153.6zM665.6 870.4l-92.16-327.68v-5.12c0-15.36 10.24-25.6 25.6-25.6H742.4c15.36 5.12 20.48 20.48 15.36 30.72L665.6 870.4z"
                      fill="#5AC8FA"
                      p-id="5202"
                  ></path>
                  <path
                      d="M25.6 537.6c-15.36 0-25.6-10.24-25.6-25.6s10.24-25.6 25.6-25.6h143.36c10.24 0 20.48-5.12 25.6-20.48L307.2 71.68c10.24-25.6 35.84-40.96 61.44-35.84 20.48 10.24 35.84 20.48 40.96 35.84l256 860.16 117.76-394.24c10.24-30.72 40.96-56.32 71.68-56.32h143.36c15.36 0 25.6 10.24 25.6 25.6s-10.24 25.6-25.6 25.6h-143.36c-10.24 0-20.48 5.12-25.6 20.48L716.8 947.2c-5.12 15.36-15.36 30.72-35.84 35.84-25.6 10.24-56.32-5.12-61.44-35.84L358.4 87.04 240.64 481.28c-10.24 30.72-40.96 56.32-71.68 56.32H25.6z"
                      fill=""
                      p-id="5203"
                  ></path>
                </svg>

              </button>
            </div>
          </div>
          <div
              class="chart-container"
              id="courseDistributionChart"
              style="height: 350px;"
          >
            <!-- Pie chart will be rendered here -->
          </div>

        </el-card>
      </el-col>

      <!-- Right Side: Statistics Cards in 2x2 Grid -->
      <el-col :span="12">
        <el-row :gutter="20">
          <!-- Top Row: Total Courses and Scheduled Courses -->
          <el-col :span="12">
            <el-card
                shadow="hover"
                class="stat-card"
            >
              <template #header>
                <div class="card-header">
                  <span>总课程数</span>
                </div>
              </template>
              <el-statistic
                  :value="150"
                  class="stat-content"
              >
                <template #suffix>
                  <span class="stat-unit">门</span>
                </template>
              </el-statistic>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card
                shadow="hover"
                class="stat-card"
            >
              <template #header>
                <div class="card-header">
                  <span>已排课程</span>
                </div>
              </template>
              <el-statistic
                  :value="145"
                  class="stat-content"
              >
                <template #suffix>
                  <span class="stat-unit">门</span>
                </template>
              </el-statistic>
            </el-card>
          </el-col>
        </el-row>

        <!-- Bottom Row: Classroom Utilization and Average Teacher Hours -->
        <el-row
            :gutter="20"
            style="margin-top: 20px;"
        >
          <el-col :span="12">
            <el-card
                shadow="hover"
                class="stat-card"
            >
              <template #header>
                <div class="card-header">
                  <span>教室使用率</span>
                </div>
              </template>
              <div class="usage-stat">
                <el-progress
                    type="dashboard"
                    :percentage="85.5"
                    :width="80"
                    class="usage-progress"
                />
                <div class="usage-value">
                  85.5%
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card
                shadow="hover"
                class="stat-card"
            >
              <template #header>
                <div class="card-header">
                  <span>教师平均课时</span>
                </div>
              </template>
              <el-statistic
                  :value="16"
                  class="stat-content"
              >
                <template #suffix>
                  <span class="stat-unit">课时/周</span>
                </template>
              </el-statistic>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <!-- 主要内容区 -->
    <el-container class="main-content">
      <!-- 左侧图表面板 -->
      <!-- todoEcharts宽度 -->
      <!-- <el-aside width="400px">
  <el-card class="chart-panel">
    <template #header>
      <div class="card-header">
        <span>课程分布</span>
      </div>
    </template> -->
      <!-- todoEcharts宽度 -->
      <!-- <div
      class="chart-container"
      style="width:250px" -->
      <!-- > -->
      <!-- 这里可以使用 ECharts 等图表库展示课程分布饼图 -->
      <!-- </div>
  </el-card>
</el-aside> -->

      <!-- 右侧课表显示区 -->
      <el-main class="schedule-main">


        <!-- 主要内容区域 -->
        <el-tabs
            v-model="analysisView"
            class="schedule-tabs"
            @tab-click="initCharts"
        >
          <!-- 统计分析 -->
          <el-tab-pane
              label="统计分析"
              name="analysis"
          >
            <el-row :gutter="20">
              <el-col :span="10">
                <!-- 教室使用情况图表 -->
                <el-card
                    class="analysis-card analysis-card-1"
                    shadow="hover"
                >
                  <template #header>
                    <div class="card-header">
                      <span>教室使用情况</span>
                      <div class="chart-type-buttons">
                        <button
                            class="chart-type-btn"
                            :class="{ active: classroomChartType === 'bar' }"
                            @click="switchClassroomChartType('bar')"
                        >
                          <!-- *柱状 -->
                          <svg
                              t="1741915357943"
                              class="icon"
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                              p-id="7800"
                              width="20"
                              height="20"
                          >
                            <path
                                d="M121.5275 552.455l690.0440625-447.9375 45.2784375 69.751875-690.0440625 447.9375z"
                                fill="#A5E8D0"
                                p-id="7801"
                                data-spm-anchor-id="a313x.search_index.0.i16.78fb3a817zllBt"
                                class="selected"
                            ></path>
                            <path
                                d="M277.71875 461.75h-93.1875c-34.6875 0-62.90625 28.78125-62.90625 64.125v315.9375c0 35.34375 28.21875 64.125 62.90625 64.125h93.1875c34.6875 0 62.90625-28.78125 62.90625-64.125v-315.9375c0-35.34375-28.21875-64.125-62.90625-64.125z m-7.03125 372.9375h-79.21875V533h79.21875v301.6875zM544.15625 276.5h-80.71875c-38.15625 0-69.1875 31.59375-69.1875 70.5v488.4375c0 38.90625 31.03125 70.5 69.1875 70.5h80.71875c38.15625 0 69.1875-31.59375 69.1875-70.5V347c0-38.90625-31.03125-70.5-69.1875-70.5z m-0.75 558.1875l-79.21875 0.75-0.75-487.6875 79.96875-0.75v487.6875zM811.71875 95.9375h-70.40625c-40.96875 0-74.34375 33.9375-74.34375 75.75v658.5c0 41.8125 33.375 75.75 74.34375 75.75h70.40625c40.96875 0 74.34375-34.03125 74.34375-75.75V171.6875c0-41.71875-33.375-75.75-74.34375-75.75z m4.40625 734.25c0 2.4375-1.96875 4.5-4.40625 4.5h-70.40625c-2.4375 0-4.40625-2.0625-4.40625-4.5V171.6875c0-2.4375 1.96875-4.5 4.40625-4.5h70.40625c2.4375 0 4.40625 1.96875 4.40625 4.5v658.5z"
                                fill="#3fafea"
                                p-id="7802"
                                data-spm-anchor-id="a313x.search_index.0.i11.78fb3a817zllBt"
                                class=""
                            ></path>
                          </svg>
                        </button>
                        <button
                            class="chart-type-btn"
                            :class="{ active: classroomChartType === 'line' }"
                            @click="switchClassroomChartType('line')"
                        >
                          <!-- *折线 -->
                          <svg
                              t="1741915155992"
                              class="icon"
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                              p-id="5201"
                              width="20"
                              height="20"
                          >
                            <path
                                d="M358.4 153.6l92.16 327.68c5.12 10.24-5.12 25.6-15.36 30.72H291.84c-15.36 0-25.6-10.24-25.6-25.6v-5.12L358.4 153.6zM665.6 870.4l-92.16-327.68v-5.12c0-15.36 10.24-25.6 25.6-25.6H742.4c15.36 5.12 20.48 20.48 15.36 30.72L665.6 870.4z"
                                fill="#5AC8FA"
                                p-id="5202"
                            ></path>
                            <path
                                d="M25.6 537.6c-15.36 0-25.6-10.24-25.6-25.6s10.24-25.6 25.6-25.6h143.36c10.24 0 20.48-5.12 25.6-20.48L307.2 71.68c10.24-25.6 35.84-40.96 61.44-35.84 20.48 10.24 35.84 20.48 40.96 35.84l256 860.16 117.76-394.24c10.24-30.72 40.96-56.32 71.68-56.32h143.36c15.36 0 25.6 10.24 25.6 25.6s-10.24 25.6-25.6 25.6h-143.36c-10.24 0-20.48 5.12-25.6 20.48L716.8 947.2c-5.12 15.36-15.36 30.72-35.84 35.84-25.6 10.24-56.32-5.12-61.44-35.84L358.4 87.04 240.64 481.28c-10.24 30.72-40.96 56.32-71.68 56.32H25.6z"
                                fill=""
                                p-id="5203"
                            ></path>
                          </svg>
                        </button>
                        <button
                            class="chart-type-btn"
                            :class="{ active: classroomChartType === 'pie' }"
                            @click="switchClassroomChartType('pie')"
                        >
                          <!-- *饼状 -->
                          <svg
                              t="1741914733616"
                              class="icon"
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                              p-id="5460"
                              width="20"
                              height="20"
                          >
                            <path
                                d="M928 512h-128a288 288 0 0 0-288-288V96a416 416 0 0 1 416 416z"
                                fill="#4A8BFE"
                                p-id="5461"
                            ></path>
                            <path
                                d="M224 512H96A416 416 0 0 1 512 96v128a288 288 0 0 0-288 288z"
                                fill="#3BD5B3"
                                p-id="5462"
                            ></path>
                            <path
                                d="M512 928A416 416 0 0 1 96 512h128a288 288 0 0 0 288 288z"
                                fill="#4A8BFE"
                                p-id="5463"
                            ></path>
                            <path
                                d="M512 928v-128a288 288 0 0 0 288-288h128a416 416 0 0 1-416 416z"
                                fill="#3BD5B3"
                                p-id="5464"
                            ></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </template>
                  <div
                      class="chart-container"
                      id="classroomUsageChart"
                  ></div>
                </el-card>
              </el-col>
              <el-col :span="14">
                <!-- 教师课时分布图表 -->
                <el-card
                    class="analysis-card analysis-card-2"
                    shadow="hover"
                >
                  <template #header>
                    <div class="card-header">
                      <span>教师课时分布</span>
                      <div class="chart-type-buttons">
                        <button
                            class="chart-type-btn"
                            :class="{ active: teacherChartType === 'line' }"
                            @click="switchTeacherChartType('line')"
                        >
                          <!-- *折线 -->
                          <svg
                              t="1741915155992"
                              class="icon"
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                              p-id="5201"
                              width="20"
                              height="20"
                          >
                            <path
                                d="M358.4 153.6l92.16 327.68c5.12 10.24-5.12 25.6-15.36 30.72H291.84c-15.36 0-25.6-10.24-25.6-25.6v-5.12L358.4 153.6zM665.6 870.4l-92.16-327.68v-5.12c0-15.36 10.24-25.6 25.6-25.6H742.4c15.36 5.12 20.48 20.48 15.36 30.72L665.6 870.4z"
                                fill="#5AC8FA"
                                p-id="5202"
                            ></path>
                            <path
                                d="M25.6 537.6c-15.36 0-25.6-10.24-25.6-25.6s10.24-25.6 25.6-25.6h143.36c10.24 0 20.48-5.12 25.6-20.48L307.2 71.68c10.24-25.6 35.84-40.96 61.44-35.84 20.48 10.24 35.84 20.48 40.96 35.84l256 860.16 117.76-394.24c10.24-30.72 40.96-56.32 71.68-56.32h143.36c15.36 0 25.6 10.24 25.6 25.6s-10.24 25.6-25.6 25.6h-143.36c-10.24 0-20.48 5.12-25.6 20.48L716.8 947.2c-5.12 15.36-15.36 30.72-35.84 35.84-25.6 10.24-56.32-5.12-61.44-35.84L358.4 87.04 240.64 481.28c-10.24 30.72-40.96 56.32-71.68 56.32H25.6z"
                                fill=""
                                p-id="5203"
                            ></path>
                          </svg>
                        </button>
                        <button
                            class="chart-type-btn"
                            :class="{ active: teacherChartType === 'bar' }"
                            @click="switchTeacherChartType('bar')"
                        >
                          <!-- *柱状 -->
                          <svg
                              t="1741915357943"
                              class="icon"
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                              p-id="7800"
                              width="20"
                              height="20"
                          >
                            <path
                                d="M121.5275 552.455l690.0440625-447.9375 45.2784375 69.751875-690.0440625 447.9375z"
                                fill="#A5E8D0"
                                p-id="7801"
                                data-spm-anchor-id="a313x.search_index.0.i16.78fb3a817zllBt"
                                class="selected"
                            ></path>
                            <path
                                d="M277.71875 461.75h-93.1875c-34.6875 0-62.90625 28.78125-62.90625 64.125v315.9375c0 35.34375 28.21875 64.125 62.90625 64.125h93.1875c34.6875 0 62.90625-28.78125 62.90625-64.125v-315.9375c0-35.34375-28.21875-64.125-62.90625-64.125z m-7.03125 372.9375h-79.21875V533h79.21875v301.6875zM544.15625 276.5h-80.71875c-38.15625 0-69.1875 31.59375-69.1875 70.5v488.4375c0 38.90625 31.03125 70.5 69.1875 70.5h80.71875c38.15625 0 69.1875-31.59375 69.1875-70.5V347c0-38.90625-31.03125-70.5-69.1875-70.5z m-0.75 558.1875l-79.21875 0.75-0.75-487.6875 79.96875-0.75v487.6875zM811.71875 95.9375h-70.40625c-40.96875 0-74.34375 33.9375-74.34375 75.75v658.5c0 41.8125 33.375 75.75 74.34375 75.75h70.40625c40.96875 0 74.34375-34.03125 74.34375-75.75V171.6875c0-41.71875-33.375-75.75-74.34375-75.75z m4.40625 734.25c0 2.4375-1.96875 4.5-4.40625 4.5h-70.40625c-2.4375 0-4.40625-2.0625-4.40625-4.5V171.6875c0-2.4375 1.96875-4.5 4.40625-4.5h70.40625c2.4375 0 4.40625 1.96875 4.40625 4.5v658.5z"
                                fill="#3fafea"
                                p-id="7802"
                                data-spm-anchor-id="a313x.search_index.0.i11.78fb3a817zllBt"
                                class=""
                            ></path>
                          </svg>
                        </button>
                        <button
                            class="chart-type-btn"
                            :class="{ active: teacherChartType === 'pie' }"
                            @click="switchTeacherChartType('pie')"
                        >
                          <!-- *饼状 -->
                          <svg
                              t="1741914733616"
                              class="icon"
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                              p-id="5460"
                              width="20"
                              height="20"
                          >
                            <path
                                d="M928 512h-128a288 288 0 0 0-288-288V96a416 416 0 0 1 416 416z"
                                fill="#4A8BFE"
                                p-id="5461"
                            ></path>
                            <path
                                d="M224 512H96A416 416 0 0 1 512 96v128a288 288 0 0 0-288 288z"
                                fill="#3BD5B3"
                                p-id="5462"
                            ></path>
                            <path
                                d="M512 928A416 416 0 0 1 96 512h128a288 288 0 0 0 288 288z"
                                fill="#4A8BFE"
                                p-id="5463"
                            ></path>
                            <path
                                d="M512 928v-128a288 288 0 0 0 288-288h128a416 416 0 0 1-416 416z"
                                fill="#3BD5B3"
                                p-id="5464"
                            ></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </template>
                  <div
                      class="chart-container"
                      id="teacherHoursChart"
                  ></div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>

        <!-- 周课表概览 -->
        <!-- 周课表概览 -->
        <div class="schedule-section">
          <div class="section-header">
            <div class="header-left">
              <h3 style="color: #409EFF;">周课表概览</h3>
            </div>
            <div class="header-right">
              <el-button
                  type="primary"
                  @click="toggleView"
              >
                切换为{{ activeView === 'class' ? '教室' : '班级' }}课表
              </el-button>
              <el-select
                  v-if="activeView === 'class'"
                  class="filter-select"
                  v-model="selectedClass"
                  placeholder="选择班级"
              >
                <el-option
                    v-for="cls in classes"
                    :key="cls.id"
                    :label="cls.name"
                    :value="cls.id"
                />
              </el-select>
              <el-select
                  v-if="activeView === 'classroom'"
                  class="filter-select"
                  v-model="selectedClassroom"
                  placeholder="选择教室"
              >
                <el-option
                    v-for="room in classrooms"
                    :key="room.id"
                    :label="room.name"
                    :value="room.id"
                />
              </el-select>
            </div>
          </div>
          <div class="schedule-grid">
            <el-table
                :data="currentSchedule"
                border
                class="schedule-table"
            >
              <!-- !原代码 -->
              <!-- <el-table-column
  prop="time"
  label="时间/星期"
  width="150"
/> -->
              <!-- *改为使用 v-html 的代码 -->
              <el-table-column
                  label="时间/星期"
                  width="150"
              >
                <template #default="scope">
                  <div v-html="scope.row.time"></div>
                </template>
              </el-table-column>
              <!-- *end -->
              <el-table-column
                  v-for="day in weekDays"
                  :key="day"
                  :label="day"
              >
                <template #default="scope">
                  <div
                      v-for="course in scope.row[day.toLowerCase()]"
                      :key="course.id"
                      class="course-item"
                  >
                    <div class="course-name">{{ course.name }}</div>
                    <div class="course-info">
                      <span>{{ course.teacher }}</span>
                      <span>{{ activeView === 'class' ? course.room : course.className }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.schedule-tabs {
  margin-top: -10px;
  height: 450px;
}

.filter-select {
  margin-right: 10px;
  width: 120px;
  /* height: 32px; */
  border-radius: 4px;
  /* border: 1px solid #dcdfe6; */
  font-size: 14px;
  color: #606266;
  background-color: #fff;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  /* height: 170px; */
  /* background-color: #303133; */
  margin-bottom: 11px;
}

.stat-card .el-card__header {
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
}

.stat-content {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content :deep(.el-statistic__number) {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

/* stat竖直居中 */
.usage-stat,
.el-statistic {
  /* background-color: #303133; */
  padding-top: 25px;
}

.stat-unit {
  margin-left: 4px;
  font-size: 14px;
  color: #909399;
}

.usage-stat {
  position: relative;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.usage-progress :deep(.el-progress__text) {
  display: none;
}

.usage-value {
  position: absolute;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.main-content {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-top: 20px;
  min-height: 600px;
}

.chart-panel {
  /* height: 89% !important; */
  /* background: #fff !important; */
}

.chart-panel .chart-container {
  height: 360px;
  /* background-color: red; */
  width: 100%;
  padding: 10px;
  /* 加了按钮之后 图表位置好像偏左了？ 加left偏移位置 */
  left: 1% !important;
  top: -10px;
}


.schedule-main {
  padding: 20px;
}

.schedule-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
}

.schedule-grid {
  margin-top: 20px;
}

.schedule-table {
  width: 100%;
  margin-top: 20px;
}

.schedule-table :deep(.el-table__row) {
  height: auto !important;
}

.schedule-table :deep(.el-table__cell) {
  padding: 12px !important;
  vertical-align: top;
}

.course-cell {
  padding: 10px;
  border-radius: 4px;
  background-color: #ecf5ff;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.course-cell:last-child {
  margin-bottom: 0;
}

.course-cell .course-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
}

.course-cell .course-info {
  font-size: 12px;
  color: #606266;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-cell .course-info .teacher,
.course-cell .course-info .room,
.course-cell .course-info .class-name {
  color: #909399;
}

.course-cell:hover {
  background-color: #e6f1fc;
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

.analysis-card {
  margin-bottom: 20px;
  height: 400px;
  /* 增加卡片高度 */
}

.analysis-card .chart-container {
  height: 350px;
  /* 调整图表容器高度 */
  width: 100%;


}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.filter-select {
  margin: 0 10px;
  width: 200px;
}

.schedule-main {
  padding: 20px;
}

.schedule-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.schedule-header .el-button {
  margin-right: 10px;
}

.chart-container {
  /* height: 400px !important; */
  width: 100%;
}

.schedule-section {
  margin-top: 30px;
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right .el-button {
  margin: 0;
}

.filter-select {
  width: 160px !important;
  margin: 0 !important;
}

.course-item {
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 6px;
}

.course-item:last-child {
  margin-bottom: 0;
}

.course-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.course-info {
  font-size: 12px;
  color: #909399;
  display: flex;
  justify-content: space-between;
}


/* 修改的部分 */
.dashboard-container {
  padding: 20px;
}

.chart-panel {
  /* background-color: #303133; */
  height: 97.3% !important;
  border-radius: 8px;

}

.stat-card {
  /* background-color: #303133; */
  height: 105%;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #303133;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px 10px 0;
}

.color-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.stat-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.stat-unit {
  font-size: 16px;
  color: #909399;
  margin-left: 5px;
}

.usage-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
}

.usage-value {
  margin-top: 5px;
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

.el-card__header {
  span {
    color: #409EFF;
  }
}

/* echarts切换按钮 */
.chart-type-buttons {
  display: flex;
  position: relative;
  gap: 5px;
  z-index: 10;
  /* 添加较高的z-index */
}

.chart-type-btn {
  padding: 4px 8px;
  font-size: 12px;
  border: none;
  border-color: #c6e2ff;
  background-color: transparent;
  color: #606266;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.chart-type-btn:hover {
  color: #409EFF;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
  /* border: 1px solid #409EFF; */
}

.chart-type-btn.active {
  color: #fff;
  background-color: #ecf5ff;
  border-color: #409EFF;
  box-shadow: rgba(64, 158, 255, 0.3) 0 0 2px;
}

.card-header {
  span {
    color: #409EFF;
  }
}

.echart-card {
  /* // background-color: aqua !important; */
  /* height: 89.5% !important; */
}

/* 课表概览 */
/* 表头 */
::v-deep(.el-table__header th) {
  background-color: #ecf5ff !important;
  /* color: #fff; */
  /* border-radius: 4px !important; */
  text-align: center;
}

::v-deep(.el-table .cell) {
  text-align: center !important;
}
</style>
<style>
.el-row {
  /* height: 42.6% !important; */
}
</style>