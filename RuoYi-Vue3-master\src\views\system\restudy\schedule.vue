<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">重修课程排课</span>
          <div class="header-buttons">
            <el-button 
              type="info" 
              plain 
              @click="goToList">
              <el-icon><List /></el-icon> 课程列表
            </el-button>
            <el-button 
              type="success" 
              plain 
              @click="goToDetails">
              <el-icon><Document /></el-icon> 课程详情
            </el-button>
            <el-button type="primary" @click="goBack">
              <el-icon><Back /></el-icon> 返回
            </el-button>
          </div>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-alert
            title="挂科重修排课说明"
            type="info"
            description="系统会根据所有选择该课程的学生的共同可用时间，推荐最佳的排课时间。您可以选择推荐时间或手动选择其他时间进行排课。"
            show-icon
            :closable="false"
            class="mb20"
          />
        </el-col>
      </el-row>

      <!-- 课程基本信息 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="course-info-box" v-loading="loading">
            <div class="section-title">课程基本信息</div>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="课程名称" :span="1">{{ courseInfo.courseName }}</el-descriptions-item>
              <el-descriptions-item label="课程代码" :span="1">{{ courseInfo.courseCode }}</el-descriptions-item>
              <el-descriptions-item label="教师姓名" :span="1">{{ courseInfo.teacherName }}</el-descriptions-item>
              <el-descriptions-item label="课时" :span="1">{{ courseInfo.hours }}</el-descriptions-item>
              <el-descriptions-item label="学分" :span="1">{{ courseInfo.credits }}</el-descriptions-item>
              <el-descriptions-item label="选课人数" :span="1">
                <el-tag :type="courseInfo.currentStudents >= courseInfo.minStudents ? 'success' : 'danger'">
                  {{ courseInfo.currentStudents }}/{{ courseInfo.maxStudents }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-col>
      </el-row>

      <!-- 学生列表和共同时间 -->
      <el-row :gutter="20" class="mt20">
        <el-col :span="12">
          <div class="box">
            <div class="section-title">已选学生列表</div>
            <el-table
              v-loading="studentsLoading"
              :data="studentList"
              border
              height="350"
              style="width: 100%">
              <el-table-column type="index" width="50" />
              <el-table-column label="学生ID" prop="studentId" min-width="100" />
              <el-table-column label="学生姓名" prop="studentName" min-width="120" />
              <el-table-column label="选课状态" prop="status" width="100">
                <template #default="scope">
                  <el-tag :type="getStudentStatusType(scope.row.status)">
                    {{ getStudentStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="box">
            <div class="section-title">学生共同可用时间</div>
            <div v-loading="timeLoading">
              <div v-if="commonTimes.length > 0" class="time-slots">
                <div 
                  v-for="(time, index) in commonTimes" 
                  :key="index" 
                  class="time-slot-item"
                  :class="{ 'selected': selectedTimeIndex === index }"
                  @click="selectTimeSlot(index)">
                  <div class="time-day">周{{ getDayText(time.weekday) }}</div>
                  <div class="time-section">第{{ time.startSection }}-{{ time.endSection }}节</div>
                  <div class="time-label">
                    <el-tag type="success" size="small" v-if="index === 0">推荐</el-tag>
                  </div>
                </div>
              </div>
              <el-empty v-else description="暂无共同可用时间" />
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 教室选择和排课 -->
      <el-row :gutter="20" class="mt20">
        <el-col :span="24">
          <div class="box">
            <div class="section-title">排课设置</div>
            <el-form :model="scheduleForm" ref="scheduleFormRef" :rules="scheduleRules" label-width="100px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="教室ID" prop="classroomId">
                    <el-select v-model="scheduleForm.classroomId" placeholder="请选择教室" style="width: 100%">
                      <el-option
                        v-for="item in classroomOptions"
                        :key="item.id"
                        :label="`${item.name} (容量:${item.capacity})`"
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="教室名称" prop="classroomName">
                    <el-input v-model="scheduleForm.classroomName" placeholder="自动填充" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="时间槽ID" prop="timeslotId">
                    <el-input v-model="scheduleForm.timeslotId" placeholder="根据所选时间自动填充" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="时间槽信息" prop="timeslotInfo">
                    <el-input v-model="scheduleForm.timeslotInfo" placeholder="自动填充" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item>
                <el-button type="primary" @click="submitSchedule" :loading="submitLoading" :disabled="!canSubmit">确认排课</el-button>
                <el-button @click="goBack">取消</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, List, Document } from '@element-plus/icons-vue'
import { getRestudyCourse, getCommonTime, scheduleCourse } from '@/api/system/restudy'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const courseId = ref(route.params.id)

// 加载状态
const loading = ref(false)
const studentsLoading = ref(false)
const timeLoading = ref(false)
const submitLoading = ref(false)

// 课程信息
const courseInfo = ref({})

// 学生列表
const studentList = ref([])

// 共同可用时间
const commonTimes = ref([])

// 选中的时间索引
const selectedTimeIndex = ref(-1)

// 教室选项（模拟数据，实际应从后端获取）
const classroomOptions = ref([
  { id: 1, name: '教学楼A101', capacity: 60 },
  { id: 2, name: '教学楼A102', capacity: 45 },
  { id: 3, name: '教学楼B201', capacity: 80 },
  { id: 4, name: '教学楼B202', capacity: 40 },
  { id: 5, name: '教学楼C301', capacity: 100 }
])

// 排课表单
const scheduleForm = reactive({
  id: null,
  classroomId: null,
  classroomName: '',
  timeslotId: null,
  timeslotInfo: '',
  status: 'arranged'
})

// 表单校验规则
const scheduleRules = reactive({
  classroomId: [
    { required: true, message: '请选择教室', trigger: 'change' }
  ]
})

const scheduleFormRef = ref()

// 是否可以提交
const canSubmit = computed(() => {
  return scheduleForm.classroomId && scheduleForm.timeslotId
})

// 获取课程信息
function getCourseInfo() {
  loading.value = true
  getRestudyCourse(courseId.value).then(response => {
    courseInfo.value = response.data
    scheduleForm.id = courseInfo.value.id
    loading.value = false
    
    // 获取共同时间
    getAvailableTimes()
  }).catch(() => {
    loading.value = false
  })
}

// 获取共同可用时间
function getAvailableTimes() {
  timeLoading.value = true
  studentsLoading.value = true
  
  getCommonTime(courseId.value).then(response => {
    const result = response.data
    studentList.value = result.students || []
    commonTimes.value = result.commonTimes || []
    
    // 默认选中第一个时间段（如果有）
    if (commonTimes.value.length > 0) {
      selectTimeSlot(0)
    }
    
    timeLoading.value = false
    studentsLoading.value = false
  }).catch(() => {
    timeLoading.value = false
    studentsLoading.value = false
  })
}

// 选择时间槽
function selectTimeSlot(index) {
  selectedTimeIndex.value = index
  const selectedTime = commonTimes.value[index]
  
  if (selectedTime) {
    // 设置时间槽信息
    scheduleForm.timeslotId = selectedTime.id
    scheduleForm.timeslotInfo = `周${getDayText(selectedTime.weekday)} 第${selectedTime.startSection}-${selectedTime.endSection}节`
  }
}

// 提交排课
function submitSchedule() {
  scheduleFormRef.value.validate(valid => {
    if (valid) {
      // 确认弹窗
      ElMessageBox.confirm('确认进行排课？排课后将通知所有已选学生。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        submitLoading.value = true
        
        // 填充教室名称
        const classroom = classroomOptions.value.find(item => item.id === scheduleForm.classroomId)
        if (classroom) {
          scheduleForm.classroomName = classroom.name
        }
        
        // 提交排课
        scheduleCourse(scheduleForm).then(response => {
          ElMessage.success('排课成功')
          submitLoading.value = false
          
          // 返回详情页
          router.push(`/system/restudy/detail/${courseId.value}`)
        }).catch(() => {
          submitLoading.value = false
        })
      }).catch(() => {})
    }
  })
}

// 监听教室ID变化，自动填充教室名称
watch(() => scheduleForm.classroomId, (newVal) => {
  const classroom = classroomOptions.value.find(item => item.id === newVal)
  scheduleForm.classroomName = classroom ? classroom.name : ''
})

// 获取星期几文本
function getDayText(day) {
  const days = ['', '一', '二', '三', '四', '五', '六', '日']
  return days[day] || day
}

// 获取学生状态类型
function getStudentStatusType(status) {
  switch (status) {
    case 'pending': return 'info'
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    default: return ''
  }
}

// 获取学生状态文本
function getStudentStatusText(status) {
  switch (status) {
    case 'pending': return '待处理'
    case 'approved': return '已批准'
    case 'rejected': return '已拒绝'
    default: return status
  }
}

// 返回详情页
function goBack() {
  router.go(-1) // 返回上一页，更灵活
}

/** 跳转到课程列表页面 */
function goToList() {
  router.push('/system/restudy/index')
}

/** 跳转到课程详情页面 */
function goToDetails() {
  router.push('/system/restudy/details')
}

onMounted(() => {
  getCourseInfo()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title {
  font-size: 18px;
  font-weight: bold;
}
.mb20 {
  margin-bottom: 20px;
}
.mt20 {
  margin-top: 20px;
}
.course-info-box {
  margin-bottom: 20px;
}
.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  padding-left: 8px;
  border-left: 4px solid #409EFF;
}
.box {
  padding: 16px;
  background: #f9f9f9;
  border-radius: 4px;
  height: 100%;
}
.time-slots {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  height: 300px;
  overflow-y: auto;
}
.time-slot-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  background: #fff;
}
.time-slot-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.time-slot-item.selected {
  border-color: #409EFF;
  background-color: #ecf5ff;
}
.time-day {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 16px;
}
.time-section {
  color: #606266;
}
.time-label {
  position: absolute;
  top: 8px;
  right: 8px;
}
.header-buttons {
  display: flex;
  gap: 10px;
}
</style> 