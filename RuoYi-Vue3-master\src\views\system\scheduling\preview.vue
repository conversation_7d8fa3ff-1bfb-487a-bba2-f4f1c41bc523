<!-- CourseScheduleViewer.vue -->
<script setup>
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
import { ElMessage } from "element-plus"
import { computed, onMounted, reactive, ref } from "vue"
import QRCode from 'qrcodejs2-fixes' // 导入二维码生成库

// 查询表单
const queryForm = reactive({
  viewType: "class",
  semester: "2024-1",
  targetId: ""
})

// 查询类型标签映射
const queryLabelMap = {
  student: "学生",
  teacher: "教师",
  classroom: "教室",
  class: "班级"
}

// 选项数据
const targetOptions = ref([])

// 加载选项数据
function loadTargetOptions() {
  // 根据当前查询类型加载对应的选项
  const options = {
    student: [
      { id: "s001", name: "张三" },
      { id: "s002", name: "李四" },
      { id: "s003", name: "王五" }
    ],
    teacher: [
      { id: "t001", name: "教师A" },
      { id: "t002", name: "教师B" },
      { id: "t003", name: "教师C" }
    ],
    classroom: [
      { id: "r001", name: "一教101" },
      { id: "r002", name: "一教102" },
      { id: "r003", name: "二教201" }
    ],
    class: [
      { id: "c001", name: "计算机2101班" },
      { id: "c002", name: "计算机2102班" },
      { id: "c003", name: "软件工程2101班" }
    ]
  }

  targetOptions.value = options[queryForm.viewType] || []
}

// 监听查询类型变化
function handleViewTypeChange() {
  queryForm.targetId = ""
  loadTargetOptions()
}

// 视图模式
const viewMode = ref("week")
const currentWeek = ref(1)

// 周视图数据
const weekDays = [
  { label: "周一", value: "monday" },
  { label: "周二", value: "tuesday" },
  { label: "周三", value: "wednesday" },
  { label: "周四", value: "thursday" },
  { label: "周五", value: "friday" },
  { label: "周六", value: "saturday" },
  { label: "周日", value: "sunday" }
]

const weeklySchedule = [
  { time: "第1-2节", value: "1-2" },
  { time: "第3-4节", value: "3-4" },
  { time: "第5-6节", value: "5-6" },
  { time: "第7-8节", value: "7-8" },
  { time: "第9-10节", value: "9-10" },
  { time: "第11-12节", value: "11-12" }
]

// 月视图数据
const currentMonth = ref(new Date().getMonth())
const currentYear = ref(new Date().getFullYear())

const currentYearMonth = computed(() => {
  return `${currentYear.value}年${currentMonth.value + 1}月`
})

// 月视图数据
const monthDays = computed(() => {
  const year = currentYear.value
  const month = currentMonth.value
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  const daysInMonth = lastDay.getDate()
  const firstDayOfWeek = firstDay.getDay() || 7 // 调整为周一为1，周日为7

  const days = []

  // 上个月的最后几天
  const prevMonthLastDay = new Date(year, month, 0).getDate()
  for (let i = firstDayOfWeek - 1; i > 0; i--) {
    days.push({
      date: `${year}-${month === 0 ? 12 : month}-${prevMonthLastDay - i + 1}`,
      dayNumber: prevMonthLastDay - i + 1,
      currentMonth: false,
      isToday: false,
      courses: []
    })
  }

  // 当前月的天数
  const today = new Date()
  for (let i = 1; i <= daysInMonth; i++) {
    const isToday = today.getDate() === i && today.getMonth() === month && today.getFullYear() === year
    days.push({
      date: `${year}-${month + 1}-${i}`,
      dayNumber: i,
      currentMonth: true,
      isToday,
      courses: getCoursesForDay(year, month + 1, i)
    })
  }

  // 下个月的前几天
  const remainingDays = 42 - days.length // 6行7列总共42个格子
  for (let i = 1; i <= remainingDays; i++) {
    days.push({
      date: `${year}-${month + 2 > 12 ? 1 : month + 2}-${i}`,
      dayNumber: i,
      currentMonth: false,
      isToday: false,
      courses: []
    })
  }

  return days
})

// 学期视图数据
const semesterViewMode = ref("byWeek")
const activeClass = ref("")
const activeWeeks = ref([1])

const classList = ref([
  { id: "c001", name: "计算机2101班" },
  { id: "c002", name: "计算机2102班" },
  { id: "c003", name: "软件工程2101班" }
])

// 课程详情
const courseDetailVisible = ref(false)
const selectedCourse = ref(null)

// 分享对话框
const shareDialogVisible = ref(false)
const qrcodeRef = ref(null)
const shareUrl = ref('')
const shareTitle = ref('')

// 模拟课程数据
const coursesData = ref([
  {
    id: "course001",
    name: "高等数学",
    teacher: "张教授",
    location: "一教101",
    dayOfWeek: "monday",
    timeSlot: "1-2",
    weeks: "1-16周",
    weekList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
    type: "必修课",
    credit: 4,
    className: "计算机2101班",
    classId: "c001",
    hasConflict: false
  },
  {
    id: "course002",
    name: "线性代数",
    teacher: "李教授",
    location: "一教102",
    dayOfWeek: "tuesday",
    timeSlot: "3-4",
    weeks: "1-16周",
    weekList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
    type: "必修课",
    credit: 3,
    className: "计算机2101班",
    classId: "c001",
    hasConflict: false
  },
  {
    id: "course003",
    name: "大学英语",
    teacher: "王教授",
    location: "二教201",
    dayOfWeek: "wednesday",
    timeSlot: "5-6",
    weeks: "1-16周单周",
    weekList: [1, 3, 5, 7, 9, 11, 13, 15],
    type: "必修课",
    credit: 2,
    className: "计算机2101班",
    classId: "c001",
    hasConflict: true,
    conflicts: ["与\"程序设计\"在5-6节存在冲突"]
  },
  {
    id: "course004",
    name: "程序设计",
    teacher: "赵教授",
    location: "计算机楼301",
    dayOfWeek: "wednesday",
    timeSlot: "5-6",
    weeks: "1-16周双周",
    weekList: [2, 4, 6, 8, 10, 12, 14, 16],
    type: "必修课",
    credit: 3,
    className: "计算机2101班",
    classId: "c001",
    hasConflict: true,
    conflicts: ["与\"大学英语\"在5-6节存在冲突"]
  }
])

// 方法
function handleQuery() {
  if (!queryForm.targetId) {
    ElMessage.warning(`请选择${queryLabelMap[queryForm.viewType]}`)
    return
  }
  // 模拟查询，实际应该从服务器获取数据
  ElMessage.success("查询成功")
}

function handleExport() {
  ElMessage.success("课表导出成功")
}

function handlePrint() {
  window.print()
}

// 分享功能
function handleShare() {
  const targetName = targetOptions.value.find(item => item.id === queryForm.targetId)?.name || ''

  if (!queryForm.targetId) {
    ElMessage.warning(`请先选择${queryLabelMap[queryForm.viewType]}后再分享`)
    return
  }

  // 生成分享标题
  shareTitle.value = `${targetName}的${queryForm.semester}学期课表`

  // 生成分享链接参数
  const params = new URLSearchParams()
  params.append('viewType', queryForm.viewType)
  params.append('semester', queryForm.semester)
  params.append('targetId', queryForm.targetId)
  params.append('mode', viewMode.value)
  params.append('week', currentWeek.value)

  // 指向分享页面的链接
  const baseUrl = window.location.origin + '/share'
  shareUrl.value = `${baseUrl}?${params.toString()}`

  // 显示分享对话框
  shareDialogVisible.value = true

  // 确保DOM已更新后再生成二维码
  setTimeout(() => {
    generateQRCode()
  }, 100)
}

// 生成二维码
function generateQRCode() {
  if (qrcodeRef.value) {
    // 清除已有的二维码
    qrcodeRef.value.innerHTML = ''

    // 生成新的二维码
    new QRCode(qrcodeRef.value, {
      text: shareUrl.value,
      width: 200,
      height: 200,
      colorDark: "#409eff",
      colorLight: "#ffffff",
      correctLevel: QRCode.CorrectLevel.H
    })
  }
}

// 复制分享链接
function copyShareLink() {
  navigator.clipboard.writeText(shareUrl.value)
    .then(() => {
      ElMessage.success('链接已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
}

// 下载二维码图片
function downloadQRCode() {
  if (!qrcodeRef.value) return

  const canvas = qrcodeRef.value.querySelector('canvas')
  if (canvas) {
    const link = document.createElement('a')
    link.download = `${shareTitle.value}.png`
    link.href = canvas.toDataURL('image/png')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('二维码已下载')
  }
}

// 获取课程
function getCoursesByTimeAndDay(timeSlot, dayOfWeek) {
  // 根据当前周次过滤课程
  return coursesData.value.filter((course) => {
    return course.timeSlot === timeSlot
      && course.dayOfWeek === dayOfWeek
      && course.weekList.includes(currentWeek.value)
  })
}
// 获取当天的课程
function getCoursesForDay(year, month, day) {
  // 计算当天是第几周的星期几
  const date = new Date(year, month - 1, day)
  const dayOfWeek = date.getDay() || 7 // 调整为周一为1，周日为7

  // 根据学期开始日期计算当前是第几周
  // 这里假设学期从2024年2月26日开始
  const semesterStart = new Date(2024, 1, 26) // 2月26日
  const diffDays = Math.floor((date - semesterStart) / (24 * 60 * 60 * 1000))
  const weekNumber = Math.floor(diffDays / 7) + 1

  if (weekNumber < 1 || weekNumber > 20) {
    return [] // 不在学期内
  }

  // 映射星期几到dayOfWeek
  const dayMapping = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]

  // 过滤出当天的课程
  return coursesData.value.filter((course) => {
    return course.dayOfWeek === dayMapping[dayOfWeek] && course.weekList.includes(weekNumber)
  }).map(course => ({
    ...course,
    time: `${course.timeSlot.replace("-", "-")}节`
  }))
}
// 获取班级在特定周的课表
function getWeeklyScheduleForClass(classId, weekNumber) {
  // 返回班级在特定周的课表
  return weeklySchedule
}

// 获取班级在特定周、特定时间段、特定星期的课程
function getCoursesByTimeAndDayForClass(classId, weekNumber, timeSlot, dayOfWeek) {
  // 返回班级在特定周、特定时间段、特定星期的课程
  return coursesData.value.filter((course) => {
    return course.classId === classId
      && course.timeSlot === timeSlot
      && course.dayOfWeek === dayOfWeek
      && course.weekList.includes(weekNumber)
  })
}

// 获取班级的所有课程
function getCoursesForClass(classId) {
  // 返回班级的所有课程
  return coursesData.value.filter(course => course.classId === classId)
}

// 显示课程详情
function showCourseDetail(course) {
  selectedCourse.value = course
  courseDetailVisible.value = true
}

function prevMonth() {
  if (currentMonth.value === 0) {
    currentMonth.value = 11
    currentYear.value--
  } else {
    currentMonth.value--
  }
}

function nextMonth() {
  if (currentMonth.value === 11) {
    currentMonth.value = 0
    currentYear.value++
  } else {
    currentMonth.value++
  }
}

// 生命周期
onMounted(() => {
  loadTargetOptions()
})
</script>

<template>
  <div class="schedule-viewer-container">
    <!-- 顶部查询条件区域 -->
    <el-card class="query-panel animate__animated animate__fadeIn">
      <el-form :model="queryForm" inline>
        <el-form-item label="查询类型">
          <el-radio-group v-model="queryForm.viewType" @change="handleViewTypeChange">
            <el-radio-button label="student">
              学生课表
            </el-radio-button>
            <el-radio-button label="teacher">
              教师课表
            </el-radio-button>
            <el-radio-button label="classroom">
              教室课表
            </el-radio-button>
            <el-radio-button label="class">
              班级课表
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="学年学期">
          <el-select v-model="queryForm.semester" placeholder="选择学期">
            <el-option label="2024-2025-1" value="2024-1" />
            <el-option label="2024-2025-2" value="2024-2" />
          </el-select>
        </el-form-item>
        <el-form-item :label="queryLabelMap[queryForm.viewType]">
          <el-select
            v-model="queryForm.targetId"
            filterable
            :placeholder="`请选择${queryLabelMap[queryForm.viewType]}`"
          >
            <el-option
              v-for="item in targetOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" class="btn-with-effect">
            <i class="el-icon-search mr-5"></i>查询
          </el-button>
          <el-button @click="handleExport" class="btn-with-effect">
            <i class="el-icon-download mr-5"></i>导出
          </el-button>
          <el-button @click="handlePrint" class="btn-with-effect">
            <i class="el-icon-printer mr-5"></i>打印
          </el-button>
          <!-- 分享课表 -->
          <el-button @click="handleShare" class="btn-with-effect">
            <i class="el-icon-share mr-5"></i>分享
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 视图切换 -->
    <el-card class="schedule-card animate__animated animate__fadeIn animate__delay-1s">
      <template #header>
        <div class="card-header">
          <span class="card-title">课表信息</span>
          <div class="view-controls">
            <el-radio-group v-model="viewMode" size="small" class="view-switcher">
              <el-radio-button label="week">
                周视图
              </el-radio-button>
              <el-radio-button label="month">
                月视图
              </el-radio-button>
              <el-radio-button label="semester">
                学期视图
              </el-radio-button>
            </el-radio-group>
            <el-select v-model="currentWeek" size="small" placeholder="周次" v-if="viewMode === 'week'" class="week-selector">
              <el-option
                v-for="i in 20"
                :key="i"
                :label="`第${i}周`"
                :value="i"
              />
            </el-select>
          </div>
        </div>
      </template>

      <!-- 周视图 -->
      <div v-if="viewMode === 'week'" class="week-view animate__animated" :class="{'animate__fadeIn': viewMode === 'week'}">
        <el-table :data="weeklySchedule" border class="schedule-table">
          <el-table-column prop="time" label="节次/星期" width="100" />
          <el-table-column
            v-for="day in weekDays"
            :key="day.value"
            :label="day.label"
          >
            <template #default="scope">
              <div
                v-for="course in getCoursesByTimeAndDay(scope.row.value, day.value)"
                :key="course.id"
                class="course-cell"
                :class="{ 'conflict-course': course.hasConflict }"
                @click="showCourseDetail(course)"
              >
                <div class="course-name">
                  {{ course.name }}
                </div>
                <div class="course-info">
                  <i class="el-icon-user mr-5"></i>{{ course.teacher }}
                </div>
                <div class="course-info">
                  <i class="el-icon-location mr-5"></i>{{ course.location }}
                </div>
                <div class="course-info" v-if="course.weeks">
                  <i class="el-icon-date mr-5"></i>{{ course.weeks }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 月视图 -->
      <div v-else-if="viewMode === 'month'" class="month-view animate__animated" :class="{'animate__fadeIn': viewMode === 'month'}">
        <div class="month-controls">
          <el-button-group class="month-navigator">
            <el-button @click="prevMonth" icon="ArrowLeft" class="nav-btn" />
            <el-button disabled class="month-display">
              {{ currentYearMonth }}
            </el-button>
            <el-button @click="nextMonth" icon="ArrowRight" class="nav-btn" />
          </el-button-group>
        </div>
        <div class="month-calendar">
          <div class="week-header">
            <div v-for="day in weekDays" :key="day.value" class="day-header">
              {{ day.label }}
            </div>
          </div>
          <div class="month-days">
            <div
              v-for="day in monthDays"
              :key="day.date"
              class="day-cell"
              :class="{ 'non-current-month': !day.currentMonth, 'today': day.isToday }"
            >
              <div class="day-number">
                {{ day.dayNumber }}
              </div>
              <div class="day-courses">
                <transition-group name="course-list">
                  <div
                    v-for="course in day.courses"
                    :key="course.id"
                    class="month-course-item"
                    :class="{ 'conflict-course': course.hasConflict }"
                    @click="showCourseDetail(course)"
                  >
                    {{ course.name }}
                  </div>
                </transition-group>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 学期视图 -->
      <div v-else class="semester-view animate__animated" :class="{'animate__fadeIn': viewMode === 'semester'}">
        <el-tabs v-model="activeClass" tab-position="left" class="semester-tabs">
          <el-tab-pane
            v-for="classItem in classList"
            :key="classItem.id"
            :label="classItem.name"
            :name="classItem.id"
          >
            <div class="semester-controls">
              <el-select v-model="semesterViewMode" size="small" class="view-mode-selector">
                <el-option label="按周次查看" value="byWeek" />
                <el-option label="按课程查看" value="byCourse" />
              </el-select>
            </div>

            <!-- 按周次查看 -->
            <div v-if="semesterViewMode === 'byWeek'" class="week-list animate__animated animate__fadeIn">
              <el-collapse v-model="activeWeeks" class="week-collapse">
                <el-collapse-item
                  v-for="i in 20"
                  :key="i"
                  :title="`第${i}周`"
                  :name="i"
                  class="week-item"
                >
                  <div class="week-courses">
                    <el-table :data="getWeeklyScheduleForClass(classItem.id, i)" border class="schedule-table-mini">
                      <el-table-column prop="time" label="节次/星期" width="100" />
                      <el-table-column
                        v-for="day in weekDays"
                        :key="day.value"
                        :label="day.label"
                      >
                        <template #default="scope">
                          <div
                            v-for="course in getCoursesByTimeAndDayForClass(classItem.id, i, scope.row.value, day.value)"
                            :key="course.id"
                            class="course-cell-small"
                            :class="{ 'conflict-course': course.hasConflict }"
                            @click="showCourseDetail(course)"
                          >
                            {{ course.name }}
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>

            <!-- 按课程查看 -->
            <div v-else class="course-list animate__animated animate__fadeIn">
              <el-table :data="getCoursesForClass(classItem.id)" border class="course-table" stripe>
                <el-table-column prop="name" label="课程名称" width="180" />
                <el-table-column prop="teacher" label="教师" width="120" />
                <el-table-column prop="location" label="教室" width="120" />
                <el-table-column prop="time" label="上课时间" />
                <el-table-column prop="weeks" label="周次" width="180" />
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button type="text" size="small" @click="showCourseDetail(scope.row)" class="detail-btn">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 课程详情对话框 -->
    <el-dialog
      v-model="courseDetailVisible"
      title="课程详情"
      width="50%"
      class="course-detail-dialog"
      destroy-on-close
    >
      <template v-if="selectedCourse">
        <el-descriptions :column="2" border class="course-descriptions">
          <el-descriptions-item label="课程名称">
            {{ selectedCourse.name }}
          </el-descriptions-item>
          <el-descriptions-item label="教师">
            {{ selectedCourse.teacher }}
          </el-descriptions-item>
          <el-descriptions-item label="教室">
            {{ selectedCourse.location }}
          </el-descriptions-item>
          <el-descriptions-item label="上课时间">
            {{ selectedCourse.time }}
          </el-descriptions-item>
          <el-descriptions-item label="周次">
            {{ selectedCourse.weeks }}
          </el-descriptions-item>
          <el-descriptions-item label="课程类型">
            {{ selectedCourse.type }}
          </el-descriptions-item>
          <el-descriptions-item label="学分">
            {{ selectedCourse.credit }}
          </el-descriptions-item>
          <el-descriptions-item label="授课班级">
            {{ selectedCourse.className }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedCourse.hasConflict" class="conflict-warning animate__animated animate__pulse animate__infinite">
          <el-alert
            title="课程冲突提示"
            type="warning"
            description="该课程与其他课程存在时间或地点冲突，请注意调整！"
            show-icon
            :closable="false"
            class="conflict-alert"
          />
          <div class="conflict-detail" v-if="selectedCourse.conflicts">
            <p>冲突详情：</p>
            <ul>
              <li v-for="(conflict, index) in selectedCourse.conflicts" :key="index">
                {{ conflict }}
              </li>
            </ul>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 分享对话框 -->
    <el-dialog
      v-model="shareDialogVisible"
      title="分享课表"
      width="30%"
      class="share-dialog"
      destroy-on-close
    >
      <div class="share-content">
        <h3 class="share-title">{{ shareTitle }}</h3>

        <div class="qrcode-container">
          <div ref="qrcodeRef" class="qrcode"></div>
          <p class="qrcode-tip">扫描二维码查看课表</p>
        </div>

        <el-divider content-position="center">或使用链接分享</el-divider>

        <div class="share-link-container">
          <el-input v-model="shareUrl" readonly class="share-link-input">
            <template #append>
              <el-button @click="copyShareLink">复制</el-button>
            </template>
          </el-input>
        </div>

        <div class="share-actions">
          <el-button type="primary" @click="downloadQRCode" class="btn-with-effect">
            <i class="el-icon-download mr-5"></i>下载二维码
          </el-button>
          <el-button @click="shareDialogVisible = false" class="btn-with-effect">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
/* 引入动画库 */
@import url('https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css');

/* 全局样式变量 */
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  --primary-bg: #ecf5ff;
  --hover-bg: #d9ecff;
  --border-radius: 8px;
  --box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --hover-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  --transition-time: 0.3s;
}

/* 通用样式 */
.mr-5 {
  margin-right: 5px;
}

.schedule-viewer-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
  transition: background-color 0.5s ease;
}

.schedule-viewer-container:hover {
  background-color: #f0f2f5;
}

/* 卡片样式 */
.query-panel,
.schedule-card {
  margin-bottom: 24px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: all var(--transition-time);
  overflow: hidden;
}

.query-panel:hover,
.schedule-card:hover {
  box-shadow: var(--hover-shadow);
  transform: translateY(-2px);
}

.query-panel :deep(.el-card__body) {
  padding: 20px;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.view-controls {
  display: flex;
  gap: 12px;
}

.view-switcher {
  transition: all var(--transition-time);
}

.view-switcher:hover {
  transform: translateY(-2px);
}

.week-selector {
  min-width: 100px;
  transition: all var(--transition-time);
}

.week-selector:hover {
  transform: translateY(-2px);
}

/* 按钮效果 */
.btn-with-effect {
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.btn-with-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-with-effect:active {
  transform: translateY(1px);
}

.btn-with-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-with-effect:hover::before {
  width: 300px;
  height: 300px;
}

/* 周视图样式 */
.week-view {
  margin-top: 20px;
  transition: all 0.5s;
}

.schedule-table {
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all var(--transition-time);
}

.schedule-table:hover {
  box-shadow: var(--hover-shadow);
}

.schedule-table :deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  height: 48px;
}

.schedule-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.course-cell {
  padding: 10px;
  margin-bottom: 6px;
  border-radius: 6px;
  background-color: var(--primary-bg);
  border-left: 3px solid var(--primary-color);
  cursor: pointer;
  transition: all var(--transition-time);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease;
}

.course-cell:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: var(--hover-bg);
}

.course-cell-small {
  padding: 4px 8px;
  font-size: 12px;
  background-color: var(--primary-bg);
  border-radius: 4px;
  margin-bottom: 3px;
  transition: all var(--transition-time);
  cursor: pointer;
  border-left: 2px solid var(--primary-color);
}

.course-cell-small:hover {
  background-color: var(--hover-bg);
  transform: translateX(3px);
  padding-left: 12px;
}

.conflict-course {
  background-color: #fff5f5;
  border-left: 3px solid var(--danger-color);
}

.conflict-course:hover {
  background-color: #ffecec;
}

.course-name {
  font-weight: 600;
  margin-bottom: 6px;
  color: #303133;
}

.course-info {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

/* 月视图样式 */
.month-view {
  margin-top: 24px;
  transition: all 0.5s;
}

.month-controls {
  margin-bottom: 20px;
  text-align: center;
}

.month-navigator {
  display: inline-flex;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.month-display {
  min-width: 150px;
  font-weight: 600;
}

.nav-btn {
  transition: all 0.3s;
}

.nav-btn:hover {
  background-color: #f5f7fa;
  color: var(--primary-color);
}

.week-header {
  display: flex;
  background: #fff;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.day-header {
  flex: 1;
  text-align: center;
  padding: 12px;
  font-weight: 600;
  color: #606266;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.month-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background-color: #ebeef5;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.day-cell {
  background-color: white;
  min-height: 120px;
  padding: 8px;
  position: relative;
  transition: all 0.3s;
}

.day-cell:hover {
  background-color: #fafafa;
  transform: scale(1.01);
  z-index: 1;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.day-number {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: 500;
  transition: all 0.3s;
}

.day-cell:hover .day-number {
  transform: scale(1.1);
}

.non-current-month {
  background-color: #fafafa;
  color: #c0c4cc;
}

.today .day-number {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
}

.day-courses {
  margin-top: 36px;
  max-height: calc(100% - 36px);
  overflow-y: auto;
}

.month-course-item {
  padding: 4px 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  background-color: var(--primary-bg);
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: all 0.3s;
  border-left: 2px solid var(--primary-color);
}

.month-course-item:hover {
  background-color: var(--hover-bg);
  transform: translateX(3px);
  padding-left: 12px;
}

/* 月视图课程列表动画 */
.course-list-enter-active,
.course-list-leave-active {
  transition: all 0.5s ease;
}

.course-list-enter-from,
.course-list-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 学期视图样式 */
.semester-view {
  margin-top: 24px;
  transition: all 0.5s;
}

.semester-tabs {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.semester-tabs :deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
  transition: all 0.3s;
}

.semester-tabs :deep(.el-tabs__item:hover) {
  color: var(--primary-color);
  transform: translateX(5px);
}

.semester-tabs :deep(.el-tabs__item.is-active) {
  font-weight: 600;
  background-color: var(--primary-bg);
  border-right: 3px solid var(--primary-color);
}

.semester-controls {
  margin-bottom: 20px;
  padding: 0 10px;
}

.view-mode-selector {
  min-width: 120px;
  transition: all 0.3s;
}

.view-mode-selector:hover {
  transform: translateY(-2px);
}

.week-list {
  margin-top: 20px;
}

.week-collapse {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.week-item {
  transition: all 0.3s;
}

.week-item:hover {
  background-color: #fafafa;
}

.week-courses {
  padding: 15px 0;
}

.schedule-table-mini {
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s;
}

.schedule-table-mini:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.course-list {
  margin-top: 20px;
}

.course-table {
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s;
}

.course-table:hover {
  box-shadow: var(--hover-shadow);
}

.detail-btn {
  transition: all 0.3s;
}

.detail-btn:hover {
  color: var(--primary-color);
  transform: translateX(3px);
}

/* 对话框样式 */
.course-detail-dialog {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.course-descriptions {
  margin-bottom: 20px;
}

/* 冲突提示样式 */
.conflict-warning {
  margin-top: 24px;
}

.conflict-alert {
  margin-bottom: 10px;
}

.conflict-detail {
  margin-top: 12px;
  padding: 12px;
  background-color: #fff7e6;
  border-radius: 6px;
  border-left: 3px solid #faad14;
  transition: all 0.3s;
}

.conflict-detail:hover {
  transform: translateX(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.conflict-detail p {
  color: #d46b08;
  font-weight: 500;
  margin-bottom: 8px;
}

.conflict-detail ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

/* 分享对话框样式 */
.share-dialog {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.share-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.share-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #303133;
  text-align: center;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.qrcode {
  width: 220px;
  height: 220px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  background-color: white;
  margin-bottom: 10px;
}

.qrcode:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.qrcode-tip {
  font-size: 14px;
  color: #909399;
  margin-top: 10px;
}

.share-link-container {
  width: 100%;
  margin-bottom: 20px;
}

.share-link-input {
  width: 100%;
}

.share-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 10px;
}

/* 打印样式 */
@media print {
  .query-panel,
  .view-controls,
  .el-dialog {
    display: none !important;
  }

  .schedule-viewer-container {
    padding: 0;
    background-color: white;
  }

  .schedule-card {
    box-shadow: none;
    margin: 0;
  }
}

/* 全局渐变动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
