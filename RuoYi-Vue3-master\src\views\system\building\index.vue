<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="教学楼编号" prop="buildingCode">
        <el-select v-model="queryParams.buildingCode" placeholder="请选择教学楼编号" clearable>
          <el-option
            v-for="dict in teaching_building_number"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="教学楼名称" prop="buildingName">
        <el-select v-model="queryParams.buildingName" placeholder="请选择教学楼名称" clearable>
          <el-option
            v-for="dict in buildings"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="校区名称" prop="campusName">
        <el-select v-model="queryParams.campusName" placeholder="请选择校区名称" clearable>
          <el-option
            v-for="dict in campuses"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="可用状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择可用状态" clearable>
          <el-option
            v-for="dict in sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:building:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:building:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:building:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:building:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="buildingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="教学楼编号" align="center" prop="buildingCode" />
      <el-table-column label="教学楼名称" align="center" prop="buildingName">
        <template #default="scope">
          <dict-tag :options="buildings" :value="scope.row.buildingName"/>
        </template>
      </el-table-column>
      <el-table-column label="校区名称" align="center" prop="campusName">
        <template #default="scope">
          <dict-tag :options="campuses" :value="scope.row.campusName"/>
        </template>
      </el-table-column>
      <el-table-column label="可用状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:building:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:building:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改教学楼信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="buildingRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="教学楼名称" prop="buildingName">
          <el-select v-model="form.buildingName" placeholder="请选择教学楼名称">
            <el-option
              v-for="dict in buildings"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="校区名称" prop="campusName">
          <el-select v-model="form.campusName" placeholder="请选择校区名称">
            <el-option
              v-for="dict in campuses"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可用状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Building">
import { listBuilding, getBuilding, delBuilding, addBuilding, updateBuilding } from "@/api/system/building";

const { proxy } = getCurrentInstance();
const { teaching_building_number, campuses, buildings, sys_yes_no } = proxy.useDict('teaching_building_number', 'campuses', 'buildings', 'sys_yes_no');

const buildingList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    buildingCode: null,
    buildingName: null,
    campusName: null,
    status: null
  },
  rules: {
    buildingCode: [
      { required: true, message: "教学楼编号不能为空", trigger: "change" }
    ],
    buildingName: [
      { required: true, message: "教学楼名称不能为空", trigger: "change" }
    ],
    status: [
      { required: true, message: "可用状态不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询教学楼信息列表 */
function getList() {
  loading.value = true;
  listBuilding(queryParams.value).then(response => {
    buildingList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    buildingCode: null,
    buildingName: null,
    campusName: null,
    status: null
  };
  proxy.resetForm("buildingRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.buildingCode);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加教学楼信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _buildingCode = row.buildingCode || ids.value
  getBuilding(_buildingCode).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改教学楼信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["buildingRef"].validate(valid => {
    if (valid) {
      if (form.value.buildingCode != null) {
        updateBuilding(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addBuilding(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _buildingCodes = row.buildingCode || ids.value;
  proxy.$modal.confirm('是否确认删除教学楼信息编号为"' + _buildingCodes + '"的数据项？').then(function() {
    return delBuilding(_buildingCodes);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/building/export', {
    ...queryParams.value
  }, `building_${new Date().getTime()}.xlsx`)
}

getList();
</script>
