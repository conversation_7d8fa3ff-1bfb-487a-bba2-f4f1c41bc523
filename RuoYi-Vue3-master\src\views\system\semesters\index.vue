<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学期ID" prop="semesterId">
        <el-input
          v-model="queryParams.semesterId"
          placeholder="请输入学期ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学期名称，如春季学期、秋季学期" prop="semesterName">
        <el-select v-model="queryParams.semesterName" placeholder="请选择学期名称，如春季学期、秋季学期" clearable>
          <el-option
            v-for="dict in semester"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联学年ID" prop="academicYearId">
        <el-select v-model="queryParams.academicYearId" placeholder="请选择关联学年ID" clearable>
          <el-option
            v-for="dict in year"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学期开始日期" style="width: 308px">
        <el-date-picker
          v-model="daterangeStartDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="学期结束日期" style="width: 308px">
        <el-date-picker
          v-model="daterangeEndDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeCreatedAt"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdatedAt"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:semesters:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:semesters:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:semesters:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:semesters:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="semestersList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学期ID" align="center" prop="semesterId" />
      <el-table-column label="学期名称，如春季学期、秋季学期" align="center" prop="semesterName">
        <template #default="scope">
          <dict-tag :options="semester" :value="scope.row.semesterName"/>
        </template>
      </el-table-column>
      <el-table-column label="关联学年ID" align="center" prop="academicYearId">
        <template #default="scope">
          <dict-tag :options="year" :value="scope.row.academicYearId"/>
        </template>
      </el-table-column>
      <el-table-column label="学期开始日期" align="center" prop="startDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学期结束日期" align="center" prop="endDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updatedAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:semesters:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:semesters:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改学期信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="semestersRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="学期名称，如春季学期、秋季学期" prop="semesterName">
          <el-select v-model="form.semesterName" placeholder="请选择学期名称，如春季学期、秋季学期">
            <el-option
              v-for="dict in semester"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联学年ID" prop="academicYearId">
          <el-select v-model="form.academicYearId" placeholder="请选择关联学年ID">
            <el-option
              v-for="dict in year"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学期开始日期" prop="startDate">
          <el-date-picker clearable
            v-model="form.startDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择学期开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="学期结束日期" prop="endDate">
          <el-date-picker clearable
            v-model="form.endDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择学期结束日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Semesters">
import { listSemesters, getSemesters, delSemesters, addSemesters, updateSemesters } from "@/api/system/semesters";

const { proxy } = getCurrentInstance();
const { year, semester } = proxy.useDict('year', 'semester');

const semestersList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeStartDate = ref([]);
const daterangeEndDate = ref([]);
const daterangeCreatedAt = ref([]);
const daterangeUpdatedAt = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    semesterId: null,
    semesterName: null,
    academicYearId: null,
    startDate: null,
    endDate: null,
    createdAt: null,
    updatedAt: null
  },
  rules: {
    semesterId: [
      { required: true, message: "学期ID不能为空", trigger: "blur" }
    ],
    semesterName: [
      { required: true, message: "学期名称，如春季学期、秋季学期不能为空", trigger: "change" }
    ],
    academicYearId: [
      { required: true, message: "关联学年ID不能为空", trigger: "change" }
    ],
    startDate: [
      { required: true, message: "学期开始日期不能为空", trigger: "blur" }
    ],
    endDate: [
      { required: true, message: "学期结束日期不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询学期信息列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != daterangeStartDate && '' != daterangeStartDate) {
    queryParams.value.params["beginStartDate"] = daterangeStartDate.value[0];
    queryParams.value.params["endStartDate"] = daterangeStartDate.value[1];
  }
  if (null != daterangeEndDate && '' != daterangeEndDate) {
    queryParams.value.params["beginEndDate"] = daterangeEndDate.value[0];
    queryParams.value.params["endEndDate"] = daterangeEndDate.value[1];
  }
  if (null != daterangeCreatedAt && '' != daterangeCreatedAt) {
    queryParams.value.params["beginCreatedAt"] = daterangeCreatedAt.value[0];
    queryParams.value.params["endCreatedAt"] = daterangeCreatedAt.value[1];
  }
  if (null != daterangeUpdatedAt && '' != daterangeUpdatedAt) {
    queryParams.value.params["beginUpdatedAt"] = daterangeUpdatedAt.value[0];
    queryParams.value.params["endUpdatedAt"] = daterangeUpdatedAt.value[1];
  }
  listSemesters(queryParams.value).then(response => {
    semestersList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    semesterId: null,
    semesterName: null,
    academicYearId: null,
    startDate: null,
    endDate: null,
    createdAt: null,
    updatedAt: null
  };
  proxy.resetForm("semestersRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeStartDate.value = [];
  daterangeEndDate.value = [];
  daterangeCreatedAt.value = [];
  daterangeUpdatedAt.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.semesterId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加学期信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _semesterId = row.semesterId || ids.value
  getSemesters(_semesterId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改学期信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["semestersRef"].validate(valid => {
    if (valid) {
      if (form.value.semesterId != null) {
        updateSemesters(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSemesters(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _semesterIds = row.semesterId || ids.value;
  proxy.$modal.confirm('是否确认删除学期信息编号为"' + _semesterIds + '"的数据项？').then(function() {
    return delSemesters(_semesterIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/semesters/export', {
    ...queryParams.value
  }, `semesters_${new Date().getTime()}.xlsx`)
}

getList();
</script>
