<template>
  <div
      class="sidebar-logo-container"
      :class="{ 'collapse': collapse }"
      :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }"
  >
    <transition name="sidebarLogoFade">
      <router-link
          v-if="collapse"
          key="collapse"
          class="sidebar-logo-link"
          to="/"
      >
        <img
            v-if="logo"
            :src="logo"
            class="sidebar-logo"
        />
        <h1
            v-else
            class="sidebar-title"
            :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }"
        >{{ title }}</h1>
      </router-link>
      <router-link
          v-else
          key="expand"
          class="sidebar-logo-link"
          to="/"
      >
        <img
            v-if="logo"
            :src="logo"
            class="sidebar-logo"
        />
        <h1
            style="color:#fff"
            class="sidebar-title"
        >TTB9</h1>
        <!-- <h1
            style="color:red"
            class="sidebar-title"
            :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }"
        >{{ title }}213</h1> -->
      </router-link>
    </transition>
  </div>
</template>

<script setup>
import variables from '@/assets/styles/variables.module.scss'
// 透明背景logo
import logo from '@/assets/logo/TTB9-2.png'
import useSettingsStore from '@/store/modules/settings'

defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
})

// const title = ;
const settingsStore = useSettingsStore();
const sideTheme = computed(() => settingsStore.sideTheme);
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}


.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-color: #090F2C !important;
  // background: red !important;
  // background: none !important;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    // 修改
    // background-color: #fff;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      // background-color: #f7f;
      vertical-align: middle;
      margin-right: 12px;
      // margin-left: -12px;
    }

    & .sidebar-title {
      // text-indent: -3px;
      // text-shadow: 1px 0 #fff, -1px 0 #fff, 0 1px #fff, 0 -1px #fff;
      font-size: 48px;
      font-weight: bold;
      background: linear-gradient(to bottom, #011452 0%, #7595ff 3%, #ffffff);
      /* 从上到下渐变 */
      -webkit-background-clip: text;
      /* 让背景裁剪到文字 */
      -webkit-text-fill-color: transparent;
      /* 让文字透明以显示背景 */
      display: inline-block;
      margin: 0;
      color: #fff;
      background-color: red;
      // 字体加宽
      // transform: scaleX(1.3);
      // transform: scaleY(1.3);
      letter-spacing: 1px;
      font-weight: bold;
      line-height: 50px;
      font-size: 18px;
      font-family: "Impact", sans-serif;
      /* 紧凑且加粗 */
      font-family: "Trebuchet MS", sans-serif;
      /* Arial 的加粗宽版 */
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>