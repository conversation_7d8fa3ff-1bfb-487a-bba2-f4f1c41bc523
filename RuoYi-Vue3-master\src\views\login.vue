<template>
  <div class="login">
    <vanta-birds :options="birdOptions"></vanta-birds>

    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">TTB9</h3>
      <div class="form-content">
        <el-form-item prop="username">
          <el-input
              v-model="loginForm.username"
              type="text"
              size="large"
              auto-complete="off"
              placeholder="账号"
          >
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
              v-model="loginForm.password"
              type="password"
              size="large"
              auto-complete="off"
              placeholder="密码"
              @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code" v-if="captchaEnabled">
          <el-input
              v-model="loginForm.code"
              size="large"
              auto-complete="off"
              placeholder="验证码"
              style="width: 63%"
              @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img"/>
          </div>
        </el-form-item>
        <el-checkbox v-model="loginForm.rememberMe" class="remember-me">记住密码</el-checkbox>
        <el-form-item>
          <el-button
              :loading="loading"
              size="large"
              type="primary"
              class="login-button"
              @click.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<style lang='scss' scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.login-form {
  width: 400px; // 稍微加宽一点
  padding: 35px; // 增加内边距
  border-radius: 15px; // 增加圆角
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(10px); // 增加模糊效果
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  border: 1px solid rgba(255, 255, 255, 0.18);
  z-index: 1;
  animation: fadeIn 0.5s ease-out; // 添加淡入动画

  .title {
    font-size: 32px; // 增大标题
    color: #fff;
    text-align: center;
    margin-bottom: 35px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px; // 增加字间距
  }

  .form-content {
    .el-form-item {
      margin-bottom: 25px;
    }

    .el-input {
      height: 45px; // 增加输入框高度
      --el-input-bg-color: rgba(255, 255, 255, 0.08);
      --el-input-border-color: rgba(255, 255, 255, 0.2);
      --el-input-hover-border-color: rgba(255, 255, 255, 0.3);
      --el-input-focus-border-color: rgba(255, 255, 255, 0.4);

      :deep(.el-input__wrapper) {
        background-color: rgba(255, 255, 255, 0.08);
        box-shadow: none;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px; // 增加圆角
        transition: all 0.3s ease;

        &:hover {
          border-color: rgba(255, 255, 255, 0.3);
          transform: translateY(-1px);
        }

        &.is-focus {
          border-color: rgba(255, 255, 255, 0.4);
          transform: translateY(-1px);
        }
      }

      input {
        color: #fff;
        height: 45px;
        font-size: 15px;

        &::placeholder {
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }

    .input-icon {
      color: rgba(255, 255, 255, 0.9);
      font-size: 18px;
    }

    .remember-me {
      color: #fff;
      margin: 0 0 25px;

      :deep(.el-checkbox__label) {
        color: #fff;
        font-size: 14px;
      }
    }

    .login-button {
      width: 100%;
      height: 45px;
      background: linear-gradient(45deg, #4299e1, #48bb78);
      border: none;
      font-size: 16px;
      font-weight: 600;
      letter-spacing: 1px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(45deg, #3182ce, #38a169);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      &:active {
        transform: translateY(-1px);
      }
    }
  }

  .login-code {
    width: 33%;
    height: 45px;
    float: right;

    .login-code-img {
      height: 45px;
      width: 100%;
      border-radius: 8px;
      cursor: pointer;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }
}

// 添加淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.el-form-item__error) {
  color: #fbd38d;
  font-size: 13px;
  margin-top: 5px;
}
</style>

<script setup>
import { ref, getCurrentInstance, watch } from 'vue'  // 添加必要的导入
import { useRoute, useRouter } from 'vue-router'
import { getCodeImg } from "@/api/login"
import Cookies from "js-cookie"
import { encrypt, decrypt } from "@/utils/jsencrypt"
import useUserStore from '@/store/modules/user'
import VantaBirds from './VantaBirds.vue'

// 修改飞鸟配置
const birdOptions = {
  backgroundColor: 0x0a192f, // 深邃的蓝色背景
  color1: 0x64ffda, // 青绿色
  color2: 0x4299e1, // 蓝色
  colorMode: 'lerpGradient',
  birdSize: 1.2,
  wingSpan: 40.0,
  speedLimit: 4.0,
  separation: 80.0,
  alignment: 25.0,
  cohesion: 25.0,
  quantity: 3.5,
  backgroundAlpha: 1,
  scale: 1.2
}

const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

// 修改为 ref 响应式数据
const loginForm = ref({
  username: "admin",
  password: "admin123",
  rememberMe: false,
  code: "",
  uuid: ""
})

// 登录表单校验规则
const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
}

const codeUrl = ref("")
const loading = ref(false)
const captchaEnabled = ref(true)
const register = ref(false)
const redirect = ref(undefined)

watch(route, (newRoute) => {
  redirect.value = newRoute.query && newRoute.query.redirect
}, { immediate: true })

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 })
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 })
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 })
      } else {
        Cookies.remove("username")
        Cookies.remove("password")
        Cookies.remove("rememberMe")
      }
      // 调用登录方法
      userStore.login(loginForm.value).then(() => {
        router.push({ path: redirect.value || "/" })
      }).catch(() => {
        loading.value = false
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode()
        }
      })
    }
  })
}

function getCode() {
  getCodeImg().then(res => {
    codeUrl.value = "data:image/gif;base64," + res.img
    loginForm.value.uuid = res.uuid
  })
}

function getCookie() {
  const username = Cookies.get("username")
  const password = Cookies.get("password")
  const rememberMe = Cookies.get("rememberMe")
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
    code: "",
    uuid: ""
  }
}
function handleFrontLogin() {
  // 模拟登录 不使用后端
  userStore.setToken("1234567890")
  router.push("/")
      .catch((err) => {
        console.error("路由跳转失败:", err)
        console.error("错误详情:", {
          name: err.name,
          message: err.message,
          stack: err.stack
        })
      })
}
// 初始化方法
function init() {
  getCookie()
  if (captchaEnabled.value) {
    getCode()
  }
}

// 页面加载时调用初始化方法
init()
</script>
