<template>
  <div class="task-monitoring-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="title-container">
            <h1 class="page-title">
              <el-icon>
                <Monitor />
              </el-icon>
              排课任务监控中心
            </h1>
            <el-button
                type="primary"
                @click="refreshData"
                :loading="loading"
            >
              <el-icon>
                <Refresh />
              </el-icon>
              刷新数据
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col
            :xs="24"
            :sm="12"
            :md="6"
        >
          <el-card
              class="statistic-card"
              shadow="hover"
          >
            <template #header>
              <div class="card-header">
                <el-icon>
                  <List />
                </el-icon>
                <span>总任务数</span>
              </div>
            </template>
            <div class="card-content">
              <count-to
                  class="card-value"
                  :start-val="0"
                  :end-val="statistics.totalTasks"
                  :duration="2000"
                  :decimal="0"
              />
              <div class="card-detail">
                <span>最近更新: {{ formatTime(statistics.lastUpdate) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col
            :xs="24"
            :sm="12"
            :md="6"
        >
          <el-card
              class="statistic-card completed"
              shadow="hover"
          >
            <template #header>
              <div class="card-header">
                <el-icon>
                  <Check />
                </el-icon>
                <span>已完成任务</span>
              </div>
            </template>
            <div class="card-content">
              <count-to
                  class="card-value"
                  :start-val="0"
                  :end-val="statistics.completedTasks"
                  :duration="2000"
                  :decimal="0"
              />
              <div class="card-detail">
                <span>完成率: {{ completionRate }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col
            :xs="24"
            :sm="12"
            :md="6"
        >
          <el-card
              class="statistic-card pending"
              shadow="hover"
          >
            <template #header>
              <div class="card-header">
                <el-icon>
                  <Loading />
                </el-icon>
                <span>处理中任务</span>
              </div>
            </template>
            <div class="card-content">
              <count-to
                  class="card-value"
                  :start-val="0"
                  :end-val="statistics.pendingTasks"
                  :duration="2000"
                  :decimal="0"
              />
              <div class="card-detail">
                <span>占比: {{ pendingRate }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col
            :xs="24"
            :sm="12"
            :md="6"
        >
          <el-card
              class="statistic-card failed"
              shadow="hover"
          >
            <template #header>
              <div class="card-header">
                <el-icon>
                  <WarningFilled />
                </el-icon>
                <span>未完成任务</span>
              </div>
            </template>
            <div class="card-content">
              <count-to
                  class="card-value"
                  :start-val="0"
                  :end-val="statistics.failedTasks"
                  :duration="2000"
                  :decimal="0"
              />
              <div class="card-detail">
                <span>失败率: {{ failureRate }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 进度条 -->
    <div class="progress-section">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <h2><el-icon>
              <Odometer />
            </el-icon> 排课进度</h2>
          </div>
        </template>
        <el-progress
            :percentage="completionRate"
            :status="progressStatus"
            :stroke-width="20"
            :format="(percentage) => `${Number(percentage).toFixed(2)}%`"
            class="progress-bar"
        >
          <template #default="{ percentage }">
            <span class="progress-label">{{ Number(percentage).toFixed(2) }}%</span>
          </template>
        </el-progress>

        <div class="progress-detail">
          <el-descriptions
              :column="4"
              border
          >
            <el-descriptions-item label="开始时间">{{ formatTime(taskDetails.startTime)
              }}</el-descriptions-item>
            <el-descriptions-item label="预计结束时间">{{ formatTime(taskDetails.estimatedEndTime)
              }}</el-descriptions-item>
            <el-descriptions-item label="已用时间">{{ taskDetails.elapsedTime }}</el-descriptions-item>
            <el-descriptions-item label="预计剩余时间">{{ taskDetails.remainingTime }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header task-list-header">
            <h2><el-icon>
              <Document />
            </el-icon> 排课任务列表</h2>
            <div class="filter-controls">
              <el-select
                  v-model="filterStatus"
                  placeholder="任务状态"
                  clearable
                  @change="filterTasks"
              >
                <el-option
                    label="全部"
                    value=""
                />
                <el-option
                    label="等待中"
                    value="waiting"
                />
                <el-option
                    label="进行中"
                    value="processing"
                />
                <el-option
                    label="已完成"
                    value="completed"
                />
                <el-option
                    label="失败"
                    value="failed"
                />
              </el-select>
              <el-input
                  v-model="searchKeyword"
                  placeholder="搜索任务"
                  clearable
                  @input="filterTasks"
              >
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </template>

        <el-table
            v-loading="loading"
            :data="filteredTasks"
            style="width: 100%"
            stripe
            border
            row-key="id"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column
              prop="id"
              label="任务ID"
              width="100"
          />
          <el-table-column
              prop="name"
              label="任务名称"
              min-width="140"
          />
          <el-table-column
              prop="type"
              label="任务类型"
              width="150"
          >
            <template #default="scope">
              <el-tag :type="getTaskTypeTag(scope.row.type)">
                {{ getTaskTypeName(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
              prop="status"
              label="状态"
              width="120"
          >
            <template #default="scope">
              <el-tag
                  :type="getStatusTag(scope.row.status)"
                  effect="dark"
              >
                {{ getStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
              prop="progress"
              label="进度"
              width="180"
          >
            <template #default="scope">
              <el-progress
                  :percentage="scope.row.progress"
                  :status="getProgressStatus(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column
              prop="startTime"
              label="开始时间"
              width="180"
          >
            <template #default="scope">
              {{ formatTime(scope.row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column
              prop="estimatedEndTime"
              label="预计结束时间"
              width="180"
          >
            <template #default="scope">
              {{ formatTime(scope.row.estimatedEndTime) }}
            </template>
          </el-table-column>
          <el-table-column
              label="操作"
              width="300"
              fixed="right"
          >
            <template #default="scope">
              <el-button
                  v-if="scope.row.status === 'processing'"
                  type="warning"
                  size="small"
                  @click="pauseTask(scope.row)"
                  :loading="scope.row.actionLoading"
              >
                暂停
              </el-button>
              <el-button
                  v-if="scope.row.status === 'waiting' || scope.row.status === 'paused'"
                  type="success"
                  size="small"
                  @click="resumeTask(scope.row)"
                  :loading="scope.row.actionLoading"
              >
                开始/继续
              </el-button>
              <el-button
                  type="danger"
                  size="small"
                  @click="cancelTask(scope.row)"
                  :disabled="scope.row.status === 'completed' || scope.row.status === 'failed'"
                  :loading="scope.row.actionLoading"
              >
                取消
              </el-button>
              <el-button
                  type="primary"
                  size="small"
                  @click="viewTaskDetail(scope.row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalItems"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog
        v-model="taskDetailVisible"
        title="排课任务详情"
        width="70%"
        destroy-on-close
    >
      <el-descriptions
          :column="2"
          border
      >
        <el-descriptions-item label="任务ID">{{ currentTask.id }}</el-descriptions-item>
        <el-descriptions-item label="任务名称">{{ currentTask.name }}</el-descriptions-item>
        <el-descriptions-item label="任务类型">
          <el-tag :type="getTaskTypeTag(currentTask.type)">
            {{ getTaskTypeName(currentTask.type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag
              :type="getStatusTag(currentTask.status)"
              effect="dark"
          >
            {{ getStatusName(currentTask.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatTime(currentTask.createdTime) }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ formatTime(currentTask.startTime) }}</el-descriptions-item>
        <el-descriptions-item
            label="结束时间"
            v-if="currentTask.endTime"
        >{{ formatTime(currentTask.endTime)
          }}</el-descriptions-item>
        <el-descriptions-item
            label="预计结束时间"
            v-else
        >{{ formatTime(currentTask.estimatedEndTime)
          }}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ currentTask.creator }}</el-descriptions-item>
        <el-descriptions-item label="任务进度">
          <el-progress
              :percentage="currentTask.progress"
              :status="getProgressStatus(currentTask)"
          />
        </el-descriptions-item>
      </el-descriptions>

      <div
          class="task-detail-additional"
          v-if="currentTask.parameters"
      >
        <h3>排课参数设置</h3>
        <el-descriptions
            :column="2"
            border
        >
          <el-descriptions-item
              v-for="(value, key) in currentTask.parameters"
              :key="key"
              :label="getParameterLabel(key)"
          >
            {{ formatParameter(key, value) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div
          class="task-logs"
          v-if="currentTask.logs && currentTask.logs.length"
      >
        <h3>任务日志</h3>
        <el-timeline>
          <el-timeline-item
              v-for="(log, index) in currentTask.logs"
              :key="index"
              :type="getLogTypeIcon(log.type)"
              :color="getLogTypeColor(log.type)"
              :timestamp="formatTime(log.timestamp)"
          >
            {{ log.message }}
          </el-timeline-item>
        </el-timeline>
      </div>

      <template #footer>
                <span class="dialog-footer">
                    <el-button @click="taskDetailVisible = false">关闭</el-button>
                    <el-button
                        type="primary"
                        @click="viewScheduleResult"
                        v-if="currentTask.status === 'completed'"
                    >
                        查看排课结果
                    </el-button>
                </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Monitor, Refresh, List, Check, Loading, WarningFilled,
  Odometer, Document, Search
} from '@element-plus/icons-vue';

// 状态数据
const loading = ref(false);
const statistics = ref({
  totalTasks: 0,
  completedTasks: 0,
  pendingTasks: 0,
  failedTasks: 0,
  lastUpdate: new Date()
});

// 任务详情数据
const taskDetails = ref({
  startTime: new Date(),
  estimatedEndTime: new Date(Date.now() + 3600000), // 1小时后
  elapsedTime: '00:00:00',
  remainingTime: '01:00:00'
});

// 任务列表数据
const tasks = ref([]);
const filteredTasks = ref([]);
const searchKeyword = ref('');
const filterStatus = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// 任务详情对话框
const taskDetailVisible = ref(false);
const currentTask = ref({});

// 计算属性
const completionRate = computed(() => {
  if (statistics.value.totalTasks === 0) return 0;
  return ((statistics.value.completedTasks / statistics.value.totalTasks) * 100).toFixed(2);
});

const pendingRate = computed(() => {
  if (statistics.value.totalTasks === 0) return 0;
  return ((statistics.value.pendingTasks / statistics.value.totalTasks) * 100).toFixed(2);
});

const failureRate = computed(() => {
  if (statistics.value.totalTasks === 0) return 0;
  return ((statistics.value.failedTasks / statistics.value.totalTasks) * 100).toFixed(2);
});

const progressStatus = computed(() => {
  if (completionRate.value >= 100) return 'success';
  if (statistics.value.failedTasks > 0) return 'warning';
  return '';
});

// 模拟数据获取
let dataUpdateTimer = null;

// 生命周期钩子
onMounted(() => {
  fetchData();
  // 设置定时刷新
  dataUpdateTimer = setInterval(() => {
    updateRealTimeData();
  }, 1000);
});

onBeforeUnmount(() => {
  // 清除定时器
  if (dataUpdateTimer) {
    clearInterval(dataUpdateTimer);
    dataUpdateTimer = null;
  }
});

// 方法
function fetchData () {
  loading.value = true;

  // 模拟API请求延迟
  setTimeout(() => {
    // 模拟统计数据
    statistics.value = {
      totalTasks: 120,
      completedTasks: 68,
      pendingTasks: 42,
      failedTasks: 10,
      lastUpdate: new Date()
    };

    // 模拟任务详情
    taskDetails.value = {
      startTime: new Date(Date.now() - 1800000), // 30分钟前
      estimatedEndTime: new Date(Date.now() + 1800000), // 30分钟后
      elapsedTime: '00:30:00',
      remainingTime: '00:30:00'
    };

    // 模拟任务列表数据
    const mockTasks = [];
    const taskTypes = ['auto', 'manual', 'mixed', 'adjustment'];
    const taskStatuses = ['waiting', 'processing', 'completed', 'failed', 'paused'];

    for (let i = 1; i <= 120; i++) {
      const type = taskTypes[Math.floor(Math.random() * taskTypes.length)];
      const status = taskStatuses[Math.floor(Math.random() * taskStatuses.length)];
      let progress = 0;

      if (status === 'completed') progress = 100;
      else if (status === 'failed') progress = Math.floor(Math.random() * 90);
      else if (status === 'processing') progress = Math.floor(Math.random() * 90) + 10;
      else if (status === 'paused') progress = Math.floor(Math.random() * 70);

      mockTasks.push({
        id: `TASK${1000 + i}`,
        name: `排课任务 ${i}`,
        type,
        status,
        progress,
        startTime: new Date(Date.now() - Math.random() * 86400000 * 2),
        estimatedEndTime: new Date(Date.now() + Math.random() * 86400000),
        createdTime: new Date(Date.now() - Math.random() * 86400000 * 3),
        endTime: status === 'completed' || status === 'failed' ? new Date() : null,
        creator: `管理员${Math.floor(Math.random() * 5) + 1}`,
        parameters: {
          clearPrevious: Math.random() > 0.5,
          autoAssignRooms: Math.random() > 0.5,
          prioritizeFaculty: Math.random() > 0.5,
          allowEvening: Math.random() > 0.3,
          sportsCourseAfternoonOnly: Math.random() > 0.4,
          teacherMaxHoursPerDay: Math.floor(Math.random() * 4) + 4
        },
        logs: Array.from({ length: Math.floor(Math.random() * 10) + 2 }, (_, index) => {
          const types = ['info', 'warning', 'error', 'success'];
          const type = types[Math.floor(Math.random() * types.length)];
          return {
            timestamp: new Date(Date.now() - (10 - index) * 600000),
            type,
            message: getRandomLogMessage(type, i)
          };
        }),
        actionLoading: false
      });
    }

    tasks.value = mockTasks;
    filterTasks();
    loading.value = false;
  }, 800);
}

function getRandomLogMessage (type, taskId) {
  const infoMessages = [
    '开始初始化排课任务',
    '正在加载课程数据',
    '正在加载教师数据',
    '正在加载教室数据',
    '正在计算排课约束条件',
    '开始应用排课策略',
    '正在优化排课结果'
  ];

  const warningMessages = [
    '教室资源紧张，可能影响部分课程安排',
    '存在教师时间冲突，尝试自动调整',
    '部分班级的课时安排存在重叠',
    '算法迭代次数较多，排课耗时可能增加'
  ];

  const errorMessages = [
    '无法为特定课程找到合适时间段',
    '教师排课超出最大工作负荷',
    '部分教室容量不足以满足班级人数',
    '存在不可解决的时间冲突'
  ];

  const successMessages = [
    '排课任务完成',
    '成功生成课表',
    '已优化教室利用率',
    '教师工作负荷均衡分配完成'
  ];

  switch (type) {
    case 'info':
      return infoMessages[Math.floor(Math.random() * infoMessages.length)];
    case 'warning':
      return warningMessages[Math.floor(Math.random() * warningMessages.length)];
    case 'error':
      return errorMessages[Math.floor(Math.random() * errorMessages.length)];
    case 'success':
      return successMessages[Math.floor(Math.random() * successMessages.length)];
    default:
      return '系统日志';
  }
}

function updateRealTimeData () {
  // 模拟数据变化，增加真实感
  if (statistics.value.pendingTasks > 0) {
    const completedIncrement = Math.floor(Math.random() * 3);
    const failedIncrement = Math.random() > 0.8 ? 1 : 0;

    if (completedIncrement + failedIncrement <= statistics.value.pendingTasks) {
      statistics.value.completedTasks += completedIncrement;
      statistics.value.failedTasks += failedIncrement;
      statistics.value.pendingTasks -= (completedIncrement + failedIncrement);
    }
  }

  // 更新时间
  statistics.value.lastUpdate = new Date();

  // 更新任务进度
  tasks.value.forEach(task => {
    if (task.status === 'processing') {
      task.progress = Math.min(100, task.progress + Math.floor(Math.random() * 5));
      if (task.progress >= 100) {
        task.status = 'completed';
        task.endTime = new Date();
      }
    }
  });

  filterTasks();
}

function refreshData () {
  fetchData();
}

function filterTasks () {
  const keyword = searchKeyword.value.toLowerCase();
  const status = filterStatus.value;

  filteredTasks.value = tasks.value.filter(task => {
    const matchesKeyword = !keyword ||
        task.id.toLowerCase().includes(keyword) ||
        task.name.toLowerCase().includes(keyword);

    const matchesStatus = !status || task.status === status;

    return matchesKeyword && matchesStatus;
  });

  totalItems.value = filteredTasks.value.length;

  // 分页处理
  filteredTasks.value = filteredTasks.value.slice(
      (currentPage.value - 1) * pageSize.value,
      currentPage.value * pageSize.value
  );
}

function handleSizeChange (size) {
  pageSize.value = size;
  filterTasks();
}

function handleCurrentChange (page) {
  currentPage.value = page;
  filterTasks();
}

function formatTime (time) {
  if (!time) return '--';
  const date = new Date(time);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function getStatusTag (status) {
  switch (status) {
    case 'waiting': return 'info';
    case 'processing': return 'primary';
    case 'completed': return 'success';
    case 'failed': return 'danger';
    case 'paused': return 'warning';
    default: return '';
  }
}

function getStatusName (status) {
  switch (status) {
    case 'waiting': return '等待中';
    case 'processing': return '进行中';
    case 'completed': return '已完成';
    case 'failed': return '失败';
    case 'paused': return '已暂停';
    default: return '未知';
  }
}

function getTaskTypeTag (type) {
  switch (type) {
    case 'auto': return 'success';
    case 'manual': return 'warning';
    case 'mixed': return 'primary';
    case 'adjustment': return 'info';
    default: return '';
  }
}

function getTaskTypeName (type) {
  switch (type) {
    case 'auto': return '自动排课';
    case 'manual': return '手动排课';
    case 'mixed': return '混合排课';
    case 'adjustment': return '课表调整';
    default: return '未知';
  }
}

function getProgressStatus (task) {
  if (task.status === 'failed') return 'exception';
  if (task.status === 'completed') return 'success';
  if (task.status === 'paused') return 'warning';
  return '';
}

function getParameterLabel (key) {
  const labels = {
    clearPrevious: '清除已排信息',
    autoAssignRooms: '自动安排教室',
    prioritizeFaculty: '院系优先',
    allowEvening: '允许晚上排课',
    sportsCourseAfternoonOnly: '体育课仅安排在下午',
    teacherMaxHoursPerDay: '教师每天最大课时'
  };

  return labels[key] || key;
}

function formatParameter (key, value) {
  if (typeof value === 'boolean') {
    return value ? '是' : '否';
  }
  return value;
}

function getLogTypeIcon (type) {
  switch (type) {
    case 'info': return '';
    case 'warning': return 'warning';
    case 'error': return 'danger';
    case 'success': return 'success';
    default: return '';
  }
}

function getLogTypeColor (type) {
  switch (type) {
    case 'info': return '#909399';
    case 'warning': return '#E6A23C';
    case 'error': return '#F56C6C';
    case 'success': return '#67C23A';
    default: return '#909399';
  }
}

// 任务操作函数
function pauseTask (task) {
  task.actionLoading = true;

  // 模拟API请求
  setTimeout(() => {
    task.status = 'paused';
    task.actionLoading = false;
    ElMessage.success(`已暂停任务: ${task.name}`);
  }, 800);
}

function resumeTask (task) {
  task.actionLoading = true;

  // 模拟API请求
  setTimeout(() => {
    task.status = 'processing';
    task.actionLoading = false;
    ElMessage.success(`已启动任务: ${task.name}`);
  }, 800);
}

function cancelTask (task) {
  ElMessageBox.confirm(
      `确认取消任务 "${task.name}"？此操作将中止当前排课过程。`,
      '取消确认',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
  ).then(() => {
    task.actionLoading = true;

    // 模拟API请求
    setTimeout(() => {
      task.status = 'failed';
      task.actionLoading = false;
      task.progress = Math.min(task.progress, 90); // 确保进度不为100%
      ElMessage.info(`已取消任务: ${task.name}`);
    }, 800);
  }).catch(() => {
    // 用户取消操作，不做任何处理
  });
}

function viewTaskDetail (task) {
  currentTask.value = JSON.parse(JSON.stringify(task));
  taskDetailVisible.value = true;
}

function viewScheduleResult () {
  ElMessage({
    message: '即将跳转到排课结果页面',
    type: 'success'
  });

  // 以下为实际跳转代码，需根据实际项目路由进行调整
  // router.push({
  //   name: 'scheduleResult',
  //   params: { taskId: currentTask.value.id }
  // });

  taskDetailVisible.value = false;
}
</script>

<style scoped>
.task-monitoring-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.statistics-cards {
  margin-bottom: 24px;
}

.statistic-card {
  transition: all 0.3s ease;
  height: 100%;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.statistic-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.statistic-card.completed {
  border-left: 4px solid #67C23A;
}

.statistic-card.pending {
  border-left: 4px solid #409EFF;
}

.statistic-card.failed {
  border-left: 4px solid #F56C6C;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #17181a;
  margin-bottom: 10px;
}

.card-detail {
  font-size: 18px;
  color: #909297;
}

.progress-section {
  margin-bottom: 24px;
}

.progress-bar {
  margin: 20px 0;
}

.progress-label {
  font-weight: 600;
  color: #000000;
}

.progress-detail {
  margin-top: 20px;
}

.task-list-section {
  margin-bottom: 24px;
}

.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.task-detail-additional {
  margin-top: 24px;
}

.task-logs {
  margin-top: 24px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 10px;
}

/* 全局动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 列表动画 */
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .task-list-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-controls {
    width: 100%;
    flex-wrap: wrap;
  }

  .card-value {
    font-size: 28px;
  }
}
</style>
<style>
.el-form-item .el-form-item__label {
  width: 170px !important;
}

.card-content {
  text-align: center;
}

.card-detail {
  line-height: 30px;
}

.el-progress-bar__outer {
  /* width: 1135px; */
  width: 100%;
}


.el-table__cell .cell {
  text-align: center;
}


.el-table_1_column_8 .cell {
  text-align: left;
}

.el-pagination {
  margin-right: 25%;
}
</style>
