import request from '@/utils/request'

// 查询排课时间槽列表
export function listSlot(query) {
  return request({
    url: '/system/slot/list',
    method: 'get',
    params: query
  })
}

// 查询排课时间槽详细
export function getSlot(id) {
  return request({
    url: '/system/slot/' + id,
    method: 'get'
  })
}

// 新增排课时间槽
export function addSlot(data) {
  return request({
    url: '/system/slot',
    method: 'post',
    data: data
  })
}

// 修改排课时间槽
export function updateSlot(data) {
  return request({
    url: '/system/slot',
    method: 'put',
    data: data
  })
}

// 删除排课时间槽
export function delSlot(id) {
  return request({
    url: '/system/slot/' + id,
    method: 'delete'
  })
}
