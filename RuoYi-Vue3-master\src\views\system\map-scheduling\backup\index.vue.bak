<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>地图排课系统</span>
        </div>
      </template>
      
      <!-- 地图容器 -->
      <div class="map-container">
        <div id="map-container" style="width: 100%; height: 500px;"></div>
        <div class="search-box">
          <el-input 
            v-model="searchKeyword" 
            placeholder="输入地点名称搜索" 
            prefix-icon="Search" 
            @keyup.enter="searchLocation"
          >
            <template #append>
              <el-button @click="searchLocation">搜索</el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 位置列表 -->
      <div class="location-list" v-if="locations.length > 0">
        <el-divider content-position="center">可选位置</el-divider>
        <el-row :gutter="12">
          <el-col v-for="location in locations" :key="location.id" :span="8" :xs="24" :sm="12" :md="8">
            <el-card class="location-card" :class="{ 'selected': selectedLocation === location.id, 'office-building': location.isOfficeBuilding }" @click="selectLocation(location)">
              <div class="location-info">
                <h4>{{ location.name }} 
                  <el-tag v-if="location.isOfficeBuilding" type="success" size="small">教师办公楼</el-tag>
                </h4>
                <p>{{ location.address }}</p>
                <p class="classroom-count">可用教室: {{ location.classrooms.length }} 间</p>
                <p v-if="nearestOfficeDistance(location) !== null" class="distance-info">
                  距最近办公楼: {{ (nearestOfficeDistance(location) / 1000).toFixed(2) }}km
                </p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      
      <!-- 排课选项 -->
      <div class="scheduling-options">
        <el-form :model="schedulingForm" label-width="120px">
          <el-divider content-position="center">排课设置</el-divider>
          
          <el-form-item label="排课模式">
            <el-radio-group v-model="schedulingForm.mode">
              <el-radio label="normal">正常排课</el-radio>
              <el-radio label="staggered">错峰排课</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <!-- 教师办公楼选择 -->
          <el-form-item label="教师所在楼">
            <el-select
              v-model="schedulingForm.teacherOffice"
              placeholder="请选择教师所在办公楼"
              style="width: 100%;"
            >
              <el-option
                v-for="item in officeBuildings"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          
          <!-- 错峰排课选项 -->
          <template v-if="schedulingForm.mode === 'staggered'">
            <el-form-item label="错峰方式">
              <el-checkbox-group v-model="schedulingForm.staggeredOptions">
                <el-checkbox label="time">时间错峰</el-checkbox>
                <el-checkbox label="grade">年级错峰</el-checkbox>
                <el-checkbox label="major">专业错峰</el-checkbox>
                <el-checkbox label="class">班级错峰</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <!-- 时间错峰选项 -->
            <el-form-item label="时间错峰" v-if="schedulingForm.staggeredOptions.includes('time')">
              <el-time-select
                v-model="schedulingForm.timeRanges.start"
                start="08:00"
                step="00:30"
                end="18:00"
                placeholder="开始时间"
                style="width: 150px; margin-right: 12px;"
              />
              <span>至</span>
              <el-time-select
                v-model="schedulingForm.timeRanges.end"
                start="08:00"
                step="00:30"
                end="18:00"
                placeholder="结束时间"
                style="width: 150px; margin-left: 12px;"
              />
            </el-form-item>
            
            <!-- 年级错峰选项 -->
            <el-form-item label="年级错峰" v-if="schedulingForm.staggeredOptions.includes('grade')">
              <el-select
                v-model="schedulingForm.selectedGrades"
                multiple
                placeholder="请选择年级"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in gradeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            
            <!-- 专业错峰选项 -->
            <el-form-item label="专业错峰" v-if="schedulingForm.staggeredOptions.includes('major')">
              <el-select
                v-model="schedulingForm.selectedMajors"
                multiple
                placeholder="请选择专业"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in majorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            
            <!-- 班级错峰选项 -->
            <el-form-item label="班级错峰" v-if="schedulingForm.staggeredOptions.includes('class')">
              <el-select
                v-model="schedulingForm.selectedClasses"
                multiple
                placeholder="请选择班级"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in classOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
          
          <el-form-item label="选择教室">
            <el-select
              v-model="schedulingForm.selectedClassrooms"
              multiple
              placeholder="请选择教室"
              style="width: 100%;"
              :disabled="!selectedLocation"
            >
              <el-option
                v-for="item in selectedLocationClassrooms"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="距离优先级">
            <el-slider
              v-model="schedulingForm.distancePriority"
              :min="0"
              :max="100"
              :format-tooltip="formatDistancePriority"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="submitScheduling">确认排课</el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button type="success" @click="addAsOfficeBuilding" v-if="selectedLocation && !isOfficeBuilding(selectedLocation)">
              设为办公楼
            </el-button>
            <el-button type="danger" @click="removeFromOfficeBuilding" v-if="selectedLocation && isOfficeBuilding(selectedLocation)">
              取消办公楼
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 排课结果预览 -->
      <div class="scheduling-results" v-if="showSchedulingResults">
        <el-divider content-position="center">排课结果预览</el-divider>
        <el-table :data="schedulingResults" style="width: 100%">
          <el-table-column prop="building" label="教学楼" />
          <el-table-column prop="classroom" label="教室" />
          <el-table-column prop="distance" label="距离办公楼">
            <template #default="scope">
              {{ (scope.row.distance / 1000).toFixed(2) }} km
            </template>
          </el-table-column>
          <el-table-column prop="timeSlot" label="时间段" v-if="schedulingForm.mode === 'staggered' && schedulingForm.staggeredOptions.includes('time')" />
          <el-table-column prop="grade" label="年级" v-if="schedulingForm.mode === 'staggered' && schedulingForm.staggeredOptions.includes('grade')" />
          <el-table-column prop="major" label="专业" v-if="schedulingForm.mode === 'staggered' && schedulingForm.staggeredOptions.includes('major')" />
          <el-table-column prop="class" label="班级" v-if="schedulingForm.mode === 'staggered' && schedulingForm.staggeredOptions.includes('class')" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { getMapAPI, loadTMapScript, createMarkerStyle, calculateDistance } from '@/utils/mapUtils';

// 获取腾讯地图API密钥
const mapApiKey = getMapAPI();
const searchKeyword = ref('');
let map = null;
let searchService = null;
let markerLayer = null;
const selectedLocation = ref(null);
const showSchedulingResults = ref(false);
const schedulingResults = ref([]);

// 排课表单数据
const schedulingForm = reactive({
  mode: 'normal',
  staggeredOptions: [],
  timeRanges: {
    start: '',
    end: ''
  },
  selectedGrades: [],
  selectedMajors: [],
  selectedClasses: [],
  selectedClassrooms: [],
  teacherOffice: null,
  distancePriority: 70 // 距离优先级，0-100
});

// 模拟数据
const locations = reactive([
  {
    id: 1,
    name: '校本部A区',
    address: '广州市天河区中山大道',
    lat: 23.137075,
    lng: 113.319968,
    isOfficeBuilding: true, // 设为办公楼
    classrooms: [
      { id: 101, name: 'A101教室' },
      { id: 102, name: 'A102教室' },
      { id: 103, name: 'A103教室' },
      { id: 104, name: 'A104多媒体教室' }
    ]
  },
  {
    id: 2,
    name: '校本部B区',
    address: '广州市天河区中山大道东',
    lat: 23.137592,
    lng: 113.321642,
    isOfficeBuilding: true, // 设为办公楼
    classrooms: [
      { id: 201, name: 'B201阶梯教室' },
      { id: 202, name: 'B202阶梯教室' },
      { id: 203, name: 'B203教室' },
      { id: 204, name: 'B204多媒体教室' }
    ]
  },
  {
    id: 3,
    name: '大学城校区',
    address: '广州市番禺区大学城',
    lat: 23.063505,
    lng: 113.397919,
    isOfficeBuilding: false,
    classrooms: [
      { id: 301, name: 'C301教室' },
      { id: 302, name: 'C302教室' },
      { id: 303, name: 'C303多媒体教室' },
      { id: 304, name: 'C304实验室' }
    ]
  },
  {
    id: 4,
    name: '教学楼D区',
    address: '广州市天河区中山大道西',
    lat: 23.138210,
    lng: 113.317800,
    isOfficeBuilding: false,
    classrooms: [
      { id: 401, name: 'D101教室' },
      { id: 402, name: 'D102教室' },
      { id: 403, name: 'D103教室' },
      { id: 404, name: 'D104多媒体教室' }
    ]
  },
  {
    id: 5,
    name: '教学楼E区',
    address: '广州市天河区中山大道南',
    lat: 23.136900,
    lng: 113.321000,
    isOfficeBuilding: false,
    classrooms: [
      { id: 501, name: 'E101教室' },
      { id: 502, name: 'E102教室' },
      { id: 503, name: 'E103教室' },
      { id: 504, name: 'E104多媒体教室' }
    ]
  }
]);

// 根据isOfficeBuilding属性获取办公楼列表
const officeBuildings = computed(() => {
  return locations.filter(loc => loc.isOfficeBuilding);
});

// 年级和专业选项
const gradeOptions = [
  { value: '2020', label: '2020级' },
  { value: '2021', label: '2021级' },
  { value: '2022', label: '2022级' },
  { value: '2023', label: '2023级' }
];

const majorOptions = [
  { value: 'computer', label: '计算机科学与技术' },
  { value: 'software', label: '软件工程' },
  { value: 'network', label: '网络工程' },
  { value: 'ai', label: '人工智能' },
  { value: 'data', label: '数据科学与大数据技术' }
];

// 班级选项
const classOptions = [
  { value: 'class-1', label: '1班' },
  { value: 'class-2', label: '2班' },
  { value: 'class-3', label: '3班' },
  { value: 'class-4', label: '4班' },
  { value: 'class-5', label: '5班' },
  { value: 'class-6', label: '6班' }
];

// 根据选择的位置筛选教室
const selectedLocationClassrooms = computed(() => {
  if (!selectedLocation.value) return [];
  const location = locations.find(loc => loc.id === selectedLocation.value);
  return location ? location.classrooms : [];
});

// 判断位置是否为办公楼
const isOfficeBuilding = (location) => {
  return location && location.isOfficeBuilding;
};

// 设置当前选中的位置为办公楼
const addAsOfficeBuilding = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择一个位置');
    return;
  }
  
  const location = locations.find(loc => loc.id === selectedLocation.value);
  if (location) {
    location.isOfficeBuilding = true;
    updateMarkerStyles();
    ElMessage.success(`已将 ${location.name} 设置为教师办公楼`);
  }
};

// 取消当前选中位置的办公楼设置
const removeFromOfficeBuilding = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择一个位置');
    return;
  }
  
  const location = locations.find(loc => loc.id === selectedLocation.value);
  if (location) {
    location.isOfficeBuilding = false;
    updateMarkerStyles();
    ElMessage.success(`已取消 ${location.name} 的教师办公楼设置`);
  }
};

// 计算位置距离最近办公楼的距离
const nearestOfficeDistance = (location) => {
  if (!location || isOfficeBuilding(location)) return null;
  
  let minDistance = Infinity;
  officeBuildings.value.forEach(office => {
    const distance = calculateDistance(
      location.lat, location.lng,
      office.lat, office.lng
    );
    if (distance < minDistance) {
      minDistance = distance;
    }
  });
  
  return minDistance === Infinity ? null : minDistance;
};

// 格式化距离优先级提示
const formatDistancePriority = (value) => {
  if (value <= 20) return '低优先级';
  if (value <= 50) return '中低优先级';
  if (value <= 80) return '中高优先级';
  return '高优先级';
};

// 初始化地图
const initMap = () => {
  loadTMapScript(() => {
    // 地图初始化
    map = new TMap.Map(document.getElementById('map-container'), {
      center: new TMap.LatLng(23.132275, 113.332001), // 默认中心点（广州）
      zoom: 12,   // 缩放比例
      viewMode: '2D'  // 2D模式
    });
    
    // 初始化搜索服务
    searchService = new TMap.service.Search({
      key: mapApiKey
    });
    
    // 创建标记图层
    createMarkerLayer();
  });
};

// 更新所有标记的样式
const updateMarkerStyles = () => {
  if (!markerLayer) return;
  
  const geometries = markerLayer.getGeometries();
  geometries.forEach(geo => {
    const locationId = parseInt(geo.id.split('_')[1]);
    const location = locations.find(loc => loc.id === locationId);
    if (location) {
      if (locationId === selectedLocation.value) {
        geo.styleId = 'selected';
      } else if (location.isOfficeBuilding) {
        geo.styleId = 'office';
      } else {
        geo.styleId = 'normal';
      }
    }
  });
  
  markerLayer.updateGeometries(geometries);
};

// 创建标记图层
const createMarkerLayer = () => {
  if (markerLayer) {
    markerLayer.setMap(null);
  }
  
  // 准备标记点数据
  const markers = locations.map(location => ({
    id: `location_${location.id}`,
    styleId: location.isOfficeBuilding ? 'office' : 'normal',
    position: new TMap.LatLng(location.lat, location.lng),
    properties: {
      title: location.name
    }
  }));
  
  // 创建标记图层
  markerLayer = new TMap.MultiMarker({
    map: map,
    styles: {
      'normal': new TMap.MarkerStyle(createMarkerStyle('#FF0000')),
      'selected': new TMap.MarkerStyle(createMarkerStyle('#0000FF')),
      'office': new TMap.MarkerStyle(createMarkerStyle('#00AA00'))
    },
    geometries: markers
  });
  
  // 添加点击事件
  markerLayer.on('click', (evt) => {
    const locationId = parseInt(evt.geometry.id.split('_')[1]);
    selectLocation(locations.find(loc => loc.id === locationId));
  });
};

// 选择位置
const selectLocation = (location) => {
  selectedLocation.value = location.id;
  
  // 如果选中的是办公楼，自动设置为教师所在楼
  if (location.isOfficeBuilding) {
    schedulingForm.teacherOffice = location.id;
  }
  
  // 更新标记样式
  updateMarkerStyles();
  
  // 将地图中心设置为选中的位置
  map.setCenter(new TMap.LatLng(location.lat, location.lng));
  map.setZoom(16);
};

// 搜索位置
const searchLocation = () => {
  if (!searchKeyword.value) {
    ElMessage.warning('请输入搜索关键词');
    return;
  }
  
  if (searchService) {
    searchService.search({
      keyword: searchKeyword.value,
      region: '广州',
      page_size: 10,
      page_index: 1,
      auto_extend: 1
    }).then(res => {
      if (res.data.length === 0) {
        ElMessage.warning('未找到相关位置');
        return;
      }
      
      // 设置地图中心点为第一个搜索结果
      const firstResult = res.data[0];
      const position = new TMap.LatLng(firstResult.location.lat, firstResult.location.lng);
      map.setCenter(position);
      map.setZoom(16);
      
      // 模拟将搜索到的位置添加到列表中
      const newLocation = {
        id: locations.length + 1,
        name: firstResult.title,
        address: firstResult.address,
        lat: firstResult.location.lat,
        lng: firstResult.location.lng,
        isOfficeBuilding: false,
        classrooms: [
          { id: 1000 + locations.length * 10 + 1, name: `${firstResult.title}教室1` },
          { id: 1000 + locations.length * 10 + 2, name: `${firstResult.title}教室2` },
          { id: 1000 + locations.length * 10 + 3, name: `${firstResult.title}多媒体教室` }
        ]
      };
      
      // 检查是否已存在相同位置
      const exists = locations.some(loc => 
        Math.abs(loc.lat - newLocation.lat) < 0.001 && 
        Math.abs(loc.lng - newLocation.lng) < 0.001
      );
      
      if (!exists) {
        locations.push(newLocation);
        
        // 更新标记图层
        markerLayer.add([{
          id: `location_${newLocation.id}`,
          styleId: 'normal',
          position: position,
          properties: {
            title: newLocation.name
          }
        }]);
      }
      
      // 选中该位置
      selectLocation(newLocation);
      
      ElMessage.success(`已找到: ${firstResult.title}`);
    }).catch(error => {
      console.error('搜索位置失败:', error);
      ElMessage.error('搜索位置失败，请稍后重试');
    });
  }
};

// 生成排课结果
const generateSchedulingResults = () => {
  const results = [];
  const selectedLocationObj = locations.find(loc => loc.id === selectedLocation.value);
  const selectedOffice = locations.find(loc => loc.id === schedulingForm.teacherOffice);
  
  if (!selectedLocationObj || !selectedOffice) return results;
  
  // 根据距离优先级对教室进行排序
  const classrooms = [...selectedLocationClassrooms.value];
  
  schedulingForm.selectedClassrooms.forEach(classroomId => {
    const classroom = classrooms.find(room => room.id === classroomId);
    if (!classroom) return;
    
    const distance = calculateDistance(
      selectedLocationObj.lat, selectedLocationObj.lng,
      selectedOffice.lat, selectedOffice.lng
    );
    
    const result = {
      building: selectedLocationObj.name,
      classroom: classroom.name,
      distance: distance
    };
    
    // 根据错峰选项添加相应的信息
    if (schedulingForm.mode === 'staggered') {
      if (schedulingForm.staggeredOptions.includes('time') && 
          schedulingForm.timeRanges.start && schedulingForm.timeRanges.end) {
        result.timeSlot = `${schedulingForm.timeRanges.start} - ${schedulingForm.timeRanges.end}`;
      }
      
      if (schedulingForm.staggeredOptions.includes('grade') && schedulingForm.selectedGrades.length > 0) {
        // 随机分配一个年级
        const randomGradeIndex = Math.floor(Math.random() * schedulingForm.selectedGrades.length);
        const gradeValue = schedulingForm.selectedGrades[randomGradeIndex];
        const grade = gradeOptions.find(g => g.value === gradeValue);
        result.grade = grade ? grade.label : gradeValue;
      }
      
      if (schedulingForm.staggeredOptions.includes('major') && schedulingForm.selectedMajors.length > 0) {
        // 随机分配一个专业
        const randomMajorIndex = Math.floor(Math.random() * schedulingForm.selectedMajors.length);
        const majorValue = schedulingForm.selectedMajors[randomMajorIndex];
        const major = majorOptions.find(m => m.value === majorValue);
        result.major = major ? major.label : majorValue;
      }
      
      if (schedulingForm.staggeredOptions.includes('class') && schedulingForm.selectedClasses.length > 0) {
        // 随机分配一个班级
        const randomClassIndex = Math.floor(Math.random() * schedulingForm.selectedClasses.length);
        const classValue = schedulingForm.selectedClasses[randomClassIndex];
        const classOption = classOptions.find(c => c.value === classValue);
        result.class = classOption ? classOption.label : classValue;
      }
    }
    
    results.push(result);
  });
  
  // 根据距离和优先级进行排序
  results.sort((a, b) => {
    // 将距离权重与用户设置的优先级结合
    const distanceWeight = schedulingForm.distancePriority / 100;
    return a.distance * distanceWeight - b.distance * distanceWeight;
  });
  
  return results;
};

// 提交排课
const submitScheduling = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择一个位置');
    return;
  }
  
  if (schedulingForm.selectedClassrooms.length === 0) {
    ElMessage.warning('请选择至少一个教室');
    return;
  }
  
  if (!schedulingForm.teacherOffice) {
    ElMessage.warning('请选择教师所在办公楼');
    return;
  }
  
  if (schedulingForm.mode === 'staggered' && schedulingForm.staggeredOptions.length === 0) {
    ElMessage.warning('请选择至少一种错峰方式');
    return;
  }
  
  if (schedulingForm.staggeredOptions.includes('time') && 
    (!schedulingForm.timeRanges.start || !schedulingForm.timeRanges.end)) {
    ElMessage.warning('请选择错峰时间范围');
    return;
  }
  
  if (schedulingForm.staggeredOptions.includes('grade') && schedulingForm.selectedGrades.length === 0) {
    ElMessage.warning('请选择错峰年级');
    return;
  }
  
  if (schedulingForm.staggeredOptions.includes('major') && schedulingForm.selectedMajors.length === 0) {
    ElMessage.warning('请选择错峰专业');
    return;
  }
  
  if (schedulingForm.staggeredOptions.includes('class') && schedulingForm.selectedClasses.length === 0) {
    ElMessage.warning('请选择错峰班级');
    return;
  }
  
  // 生成排课结果
  schedulingResults.value = generateSchedulingResults();
  showSchedulingResults.value = true;
  
  // 控制台输出排课数据
  console.log('排课表单数据:', {
    location: locations.find(loc => loc.id === selectedLocation.value),
    teacherOffice: locations.find(loc => loc.id === schedulingForm.teacherOffice),
    schedulingData: schedulingForm,
    results: schedulingResults.value
  });
  
  ElMessage.success('排课成功！请查看排课结果');
};

// 重置表单
const resetForm = () => {
  schedulingForm.mode = 'normal';
  schedulingForm.staggeredOptions = [];
  schedulingForm.timeRanges.start = '';
  schedulingForm.timeRanges.end = '';
  schedulingForm.selectedGrades = [];
  schedulingForm.selectedMajors = [];
  schedulingForm.selectedClasses = [];
  schedulingForm.selectedClassrooms = [];
  schedulingForm.teacherOffice = null;
  schedulingForm.distancePriority = 70;
  selectedLocation.value = null;
  showSchedulingResults.value = false;
  
  // 重置标记样式
  updateMarkerStyles();
  
  // 重置地图视图
  map.setCenter(new TMap.LatLng(23.132275, 113.332001));
  map.setZoom(12);
};

// 在组件挂载后初始化地图
onMounted(() => {
  initMap();
});
</script>

<style scoped>
.map-container {
  position: relative;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.search-box {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 350px;
  z-index: 1000;
  background-color: white;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.location-list {
  margin-top: 20px;
  margin-bottom: 20px;
}

.location-card {
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.location-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.location-card.selected {
  border: 2px solid #409EFF;
}

.location-card.office-building {
  border-left: 5px solid #67C23A;
}

.location-info h4 {
  margin: 0 0 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.location-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.classroom-count {
  color: #409EFF !important;
}

.distance-info {
  color: #E6A23C !important;
  font-weight: bold;
}

.scheduling-options {
  margin-top: 20px;
}

.scheduling-results {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 