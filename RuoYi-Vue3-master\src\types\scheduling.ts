// 课程类型定义
export interface Course {
  id: number | string
  name: string
  teacher: string
  hours: number
  studentCount: number
  courseType: string
  classId: string
  classroomId: string
  timeSlotId: string
}

// 教室类型定义
export interface ClassRoom {
  id: number
  name: string
  capacity: number
  building: string
  floor: string
  equipment: string[]
  classId: string
}

// 时间槽类型定义
export interface TimeSlot {
  id: number
  time: string
  period: number
  dayOfWeek: number
}

// 排课项类型定义
export interface ScheduleItem {
  courseId: number | string
  classroomId: number
  timeSlotId: number
  week: number
  weekday: number
  course?: Course
  classroom?: ClassRoom
  timeSlot?: TimeSlot
}

// 班级类型定义
export interface Class {
  id: number | string
  name: string
  grade: string
  majorName: string
  studentCount: number
}

// 冲突信息定义
export interface ConflictInfo {
  id: number
  type: string
  message: string
  classroomId: number
  timeSlotId: number
  weekday: number
  courseId: string | number
}

// 周次信息定义
export interface WeekInfo {
  id: number
  name: string
  startDate?: string
  endDate?: string
  isCurrent?: boolean
}

// 导出格式定义
export type ExportFormat = 'xlsx' | 'pdf' | 'csv'

// 统计信息定义
export interface SchedulingStats {
  totalCourses: number
  scheduledCourses: number
  completionRate: string
}