import request from '@/utils/request'

// 查询节假日信息列表
export function listHolidays(query) {
  return request({
    url: '/system/holidays/list',
    method: 'get',
    params: query
  })
}

// 查询节假日信息详细
export function getHolidays(holidayId) {
  return request({
    url: '/system/holidays/' + holidayId,
    method: 'get'
  })
}

// 新增节假日信息
export function addHolidays(data) {
  return request({
    url: '/system/holidays',
    method: 'post',
    data: data
  })
}

// 修改节假日信息
export function updateHolidays(data) {
  return request({
    url: '/system/holidays',
    method: 'put',
    data: data
  })
}

// 删除节假日信息
export function delHolidays(holidayId) {
  return request({
    url: '/system/holidays/' + holidayId,
    method: 'delete'
  })
}
