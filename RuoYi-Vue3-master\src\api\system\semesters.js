import request from '@/utils/request'

// 查询学期信息列表
export function listSemesters(query) {
  return request({
    url: '/system/semesters/list',
    method: 'get',
    params: query
  })
}

// 查询学期信息详细
export function getSemesters(semesterId) {
  return request({
    url: '/system/semesters/' + semesterId,
    method: 'get'
  })
}

// 新增学期信息
export function addSemesters(data) {
  return request({
    url: '/system/semesters',
    method: 'post',
    data: data
  })
}

// 修改学期信息
export function updateSemesters(data) {
  return request({
    url: '/system/semesters',
    method: 'put',
    data: data
  })
}

// 删除学期信息
export function delSemesters(semesterId) {
  return request({
    url: '/system/semesters/' + semesterId,
    method: 'delete'
  })
}
