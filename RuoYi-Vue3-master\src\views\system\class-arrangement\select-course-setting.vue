<!-- SchedulingSettings.vue -->
<template>
  <div class="scheduling-settings">
    <el-row class="mb-4">
      <el-col :span="24">
        <div class="page-header">
          <h2>排课设置</h2>
          <p>配置自动排课算法的各项参数和规则</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <!-- 排课基本设置 -->
        <el-card class="setting-card mb-4" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon class="header-icon"><Setting /></el-icon>
                <span>排课基本设置</span>
              </div>
              <el-button type="primary" @click="saveSettings" :icon="Check">保存设置</el-button>
            </div>
          </template>
          <el-form :model="basicSettings" label-width="160px" class="animated-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="排课最小单位" tooltip="设置排课的基本单位">
                  <el-select v-model="basicSettings.minUnit" class="w-full">
                    <el-option label="教学班" value="teachingClass" />
                    <el-option label="行政班" value="adminClass" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="自动排课前清除已排课程">
                  <el-switch 
                    v-model="basicSettings.clearBeforeSchedule" 
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="自动排课时安排教室">
                  <el-switch 
                    v-model="basicSettings.autoAssignClassroom" 
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="启用班级固定教室">
                  <el-tooltip content="启用后班级的固定教室为班级的排课地点，合班课程不生效" placement="top">
                    <el-switch 
                      v-model="basicSettings.useFixedClassroom" 
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="允许晚上排课">
                  <el-switch 
                    v-model="basicSettings.allowEveningClasses" 
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="勾选课程优先排课">
                  <el-switch 
                    v-model="basicSettings.enableSelectiveCourses" 
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="班级教室尽量集中">
                  <el-tooltip content="同一班级的课程尽量安排在相近的教室" placement="top">
                    <el-switch 
                      v-model="basicSettings.centralizeClassRooms" 
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="教师教室尽量集中">
                  <el-tooltip content="同一教师的课程尽量安排在相近的教室" placement="top">
                    <el-switch 
                      v-model="basicSettings.centralizeTeacherRooms" 
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="同一课程使用相同教室">
                  <el-switch 
                    v-model="basicSettings.sameCourseUsesSameRoom" 
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="班级人数小于教室容量">
                  <el-tooltip content="确保班级人数不超过所分配教室的容量" placement="top">
                    <el-switch 
                      v-model="basicSettings.enforceRoomCapacity" 
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 时间规则设置 -->
        <el-card class="setting-card mb-4" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon class="header-icon"><Clock /></el-icon>
                <span>时间规则设置</span>
              </div>
            </div>
          </template>
          <el-form :model="timeSettings" label-width="160px" class="animated-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="连排规则">
                  <el-select v-model="timeSettings.continuousRule" class="w-full">
                    <el-option label="两节连排(1,3,5,7)" value="2" />
                    <el-option label="四节连排(1,3,5)" value="4" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="奇数学时处理方式">
                  <el-tooltip content="处理周学时为奇数，连排节次为偶数的情况" placement="top">
                    <el-select v-model="timeSettings.oddHoursHandle" class="w-full">
                      <el-option label="单双周拆分" value="split" />
                      <el-option label="降低连排节次" value="reduce" />
                    </el-select>
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="教师每日最大课时" class="number-input-group">
                  <el-input-number v-model="timeSettings.teacherMaxDailyHours" :min="1" :max="8" controls-position="right" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="教师每周最大课时" class="number-input-group">
                  <el-input-number v-model="timeSettings.teacherMaxWeeklyHours" :min="1" :max="40" controls-position="right" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="教师上午最大课时" class="number-input-group">
                  <el-input-number v-model="timeSettings.teacherMaxMorningHours" :min="1" :max="4" controls-position="right" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="教师下午最大课时" class="number-input-group">
                  <el-input-number v-model="timeSettings.teacherMaxAfternoonHours" :min="1" :max="4" controls-position="right" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 特殊课程规则 -->
        <el-card class="setting-card mb-4" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon class="header-icon"><Notebook /></el-icon>
                <span>特殊课程规则</span>
              </div>
            </div>
          </template>
          <el-form :model="specialCourseSettings" label-width="160px" class="animated-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="体育课仅安排在下午">
                  <el-switch 
                    v-model="specialCourseSettings.peOnlyAfternoon" 
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="体育课后不安排课程">
                  <el-tooltip content="避免学生体育课后立即安排其他课程" placement="top">
                    <el-switch 
                      v-model="specialCourseSettings.noClassAfterPE" 
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="实验课仅安排在晚上">
                  <el-switch 
                    v-model="specialCourseSettings.labOnlyEvening" 
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="多学时课程连续排课">
                  <el-tooltip content="多学时（理论、实验、上机）类型的课程学时连续排" placement="top">
                    <el-switch 
                      v-model="specialCourseSettings.multiHoursContinuous" 
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 优先级设置 -->
        <el-card class="setting-card mb-4" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon class="header-icon"><Star /></el-icon>
                <span>优先级设置</span>
              </div>
            </div>
          </template>
          <el-form :model="prioritySettings" label-width="160px" class="animated-form">
            <el-form-item label="优先级排序">
              <el-table
                :data="prioritySettings.priorities"
                style="width: 100%"
                border
                class="priority-table"
                :row-class-name="tableRowClassName"
              >
                <el-table-column label="优先级" width="100">
                  <template #default="scope">
                    <div class="priority-rank">{{ scope.$index + 1 }}</div>
                  </template>
                </el-table-column>
                <el-table-column prop="label" label="规则类型">
                </el-table-column>
                <el-table-column label="操作" width="160" align="center">
                  <template #default="scope">
                    <el-button-group>
                      <el-button
                        :disabled="scope.$index === 0"
                        @click="moveUp(scope.$index)"
                        type="primary"
                        link
                      >
                        <el-icon><ArrowUp /></el-icon>
                      </el-button>
                      <el-button
                        :disabled="scope.$index === prioritySettings.priorities.length - 1"
                        @click="moveDown(scope.$index)"
                        type="primary"
                        link
                      >
                        <el-icon><ArrowDown /></el-icon>
                      </el-button>
                    </el-button-group>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 禁排设置 -->
        <el-card class="setting-card mb-4" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon class="header-icon"><WarningFilled /></el-icon>
                <span>禁排设置</span>
              </div>
            </div>
          </template>

          <el-form :model="restrictionSettings" label-width="160px" class="animated-form">
            <el-tabs class="custom-tabs">
              <el-tab-pane label="时间禁排">
                <div class="tab-content">
                  <el-form-item label="选择时间段">
                    <el-time-select 
                      v-model="restrictionSettings.schoolRestrictedTime"
                      class="w-full"
                      multiple
                      clearable />
                  </el-form-item>
                </div>
              </el-tab-pane>

              <el-tab-pane label="教师禁排">
                <div class="tab-content">
                  <el-form-item label="选择教师">
                    <el-select 
                      v-model="restrictionSettings.selectedTeachers" 
                      class="w-full" 
                      filterable
                      multiple
                      collapse-tags
                      placeholder="请选择多个教师"
                      clearable>
                      <el-option v-for="teacher in teachers" :key="teacher.id"
                              :label="teacher.name" :value="teacher.id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="禁排时段">
                    <el-time-select 
                      v-model="restrictionSettings.teacherRestrictedTime"
                      class="w-full"
                      multiple
                      clearable />
                  </el-form-item>
                </div>
              </el-tab-pane>

              <el-tab-pane label="学生禁排">
                <div class="tab-content">
                  <el-form-item label="选择班级">
                    <el-select 
                      v-model="restrictionSettings.selectedClasses" 
                      class="w-full" 
                      filterable
                      multiple
                      collapse-tags
                      placeholder="请选择多个班级"
                      clearable>
                      <el-option v-for="cls in classes" :key="cls.id"
                              :label="cls.name" :value="cls.id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="禁排时段">
                    <el-time-select 
                      v-model="restrictionSettings.studentRestrictedTime"
                      class="w-full"
                      multiple
                      clearable />
                  </el-form-item>
                </div>
              </el-tab-pane>

              <el-tab-pane label="课程禁排">
                <div class="tab-content">
                  <el-form-item label="选择课程">
                    <el-select 
                      v-model="restrictionSettings.selectedCourses" 
                      class="w-full" 
                      filterable
                      multiple
                      collapse-tags
                      placeholder="请选择多个课程"
                      clearable>
                      <el-option v-for="course in courses" :key="course.id"
                              :label="course.name" :value="course.id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="禁排时段">
                    <el-time-select 
                      v-model="restrictionSettings.courseRestrictedTime"
                      class="w-full"
                      multiple
                      clearable />
                  </el-form-item>
                </div>
              </el-tab-pane>

              <el-tab-pane label="教室禁排">
                <div class="tab-content">
                  <el-form-item label="选择教室">
                    <el-select 
                      v-model="restrictionSettings.selectedClassrooms" 
                      class="w-full" 
                      filterable
                      multiple
                      collapse-tags
                      placeholder="请选择多个教室"
                      clearable>
                      <el-option v-for="room in classrooms" :key="room.id"
                              :label="room.name" :value="room.id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="禁排时段">
                    <el-time-select 
                      v-model="restrictionSettings.classroomRestrictedTime"
                      class="w-full"
                      multiple
                      clearable />
                  </el-form-item>
                </div>
              </el-tab-pane>

              <el-tab-pane label="角色禁排">
                <div class="tab-content">
                  <el-form-item label="选择角色">
                    <el-select 
                      v-model="restrictionSettings.selectedRoles" 
                      class="w-full" 
                      filterable
                      multiple
                      collapse-tags
                      placeholder="请选择多个角色"
                      clearable>
                      <el-option v-for="role in roles" :key="role.id"
                              :label="role.name" :value="role.id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="禁排时段">
                    <el-time-select 
                      v-model="restrictionSettings.roleRestrictedTime"
                      class="w-full"
                      multiple
                      clearable />
                  </el-form-item>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-form>
        </el-card>

        <!-- 选择性排课 -->
        <transition name="fade">
          <el-card class="setting-card mt-4" shadow="hover" v-if="basicSettings.enableSelectiveCourses">
            <template #header>
              <div class="card-header">
                <div class="header-left">
                  <el-icon class="header-icon"><List /></el-icon>
                  <span>选择性排课</span>
                </div>
              </div>
            </template>
            <el-table 
              :data="selectiveCourses" 
              style="width: 100%" 
              border
              class="custom-table"
              :row-class-name="tableRowClassName"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="courseCode" label="课程代码" width="120" />
              <el-table-column prop="courseName" label="课程名称" />
              <el-table-column prop="teacher" label="教师" width="120" />
              <el-table-column prop="classInfo" label="班级" width="180" />
              <el-table-column prop="hours" label="学时" width="80" />
            </el-table>
            <div class="flex justify-end mt-3">
              <el-button type="primary" @click="scheduleSelectedCourses" :icon="Calendar">优先排课</el-button>
            </div>
          </el-card>
        </transition>
        
        <!-- 保存和重置 -->
        <div class="footer-actions">
          <el-button type="primary" @click="saveSettings" :icon="Check">保存全部设置</el-button>
          <el-button @click="resetSettings" :icon="Refresh">重置设置</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Clock, Notebook, Star, WarningFilled, Check, 
         Refresh, ArrowUp, ArrowDown, List, Calendar } from '@element-plus/icons-vue'
import { useSelectCourseSettingsStore } from '@/store/modules/select-course-settings'

// 使用Pinia store
const settingsStore = useSelectCourseSettingsStore()

// 基本设置
const basicSettings = computed(() => settingsStore.basicSettings)

// 时间规则设置
const timeSettings = computed(() => settingsStore.timeSettings)

// 特殊课程规则
const specialCourseSettings = computed(() => settingsStore.specialCourseSettings)

// 优先级设置
const prioritySettings = computed(() => settingsStore.prioritySettings)

// 禁排设置
const restrictionSettings = computed(() => settingsStore.restrictionSettings)

// 数据引用
const teachers = computed(() => settingsStore.teachers)
const classrooms = computed(() => settingsStore.classrooms)
const classes = computed(() => settingsStore.classes)
const courses = computed(() => settingsStore.courses)
const roles = computed(() => settingsStore.roles)
const selectiveCourses = computed(() => settingsStore.selectiveCourses)

// 表格行类名
const tableRowClassName = ({ row, rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 优先级调整函数
const moveUp = (index) => {
  settingsStore.movePriorityUp(index)
}

const moveDown = (index) => {
  settingsStore.movePriorityDown(index)
}

// 选择性排课函数
const scheduleSelectedCourses = async () => {
  try {
    const message = await settingsStore.scheduleSelectedCourses()
    ElMessage({
      message,
      type: 'success',
      duration: 2000
    })
  } catch (error) {
    ElMessage.error('操作失败：' + error.message)
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    const message = await settingsStore.saveSettings()
    ElMessage({
      message,
      type: 'success',
      duration: 2000
    })
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  }
}

// 重置设置
const resetSettings = async () => {
  try {
    const message = await settingsStore.resetSettings()
    ElMessage({
      message,
      type: 'info',
      duration: 2000
    })
  } catch (error) {
    ElMessage.error('重置失败：' + error.message)
  }
}
</script>

<style scoped>
.scheduling-settings {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  padding: 20px;
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  color: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.8s ease-in-out;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: bold;
}

.page-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.setting-card {
  margin-bottom: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  animation: slideUp 0.5s ease-in-out;
}

.setting-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  font-size: 16px;
}

.header-icon {
  font-size: 18px;
  color: #409EFF;
}

.el-card {
  margin-bottom: 20px;
  overflow: visible;
}

.el-form-item:last-child {
  margin-bottom: 0;
}

.animated-form .el-form-item {
  transition: all 0.3s ease;
  margin-bottom: 15px;
}

.animated-form .el-row {
  margin-bottom: 10px;
}

.animated-form .el-form-item:hover {
  transform: translateX(5px);
}

.el-switch {
  height: 26px;
  display: flex;
  align-items: center;
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 40px;
  margin-bottom: 20px;
}

.footer-actions .el-button {
  padding: 12px 20px;
  transition: all 0.3s ease;
}

.footer-actions .el-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.custom-tabs .el-tabs__header {
  margin-bottom: 20px;
}

.tab-content {
  animation: fadeIn 0.5s ease-in-out;
  padding: 10px;
}

.custom-table {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.w-full {
  width: 100%;
}

.priority-rank {
  background: #409EFF;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.priority-table .el-table__row {
  transition: all 0.3s ease;
}

.priority-table .el-table__row:hover {
  background-color: #f0f9ff !important;
  transform: scale(1.01);
}

.even-row {
  background-color: #fafafa;
}

.odd-row {
  background-color: white;
}

.number-input-group .el-input-number {
  width: 100%;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>
