import request from '@/utils/request'

// 排课管理相关接口
export function useSchedulingApi() {
  return {
    // 获取课程列表
    getCourses() {
      return request({
        url: '/system/scheduling/courses',
        method: 'get'
      })
    },

    // 获取教室列表
    getClassrooms() {
      return request({
        url: '/system/scheduling/classrooms',
        method: 'get'
      })
    },

    // 获取时间槽
    getTimeSlots() {
      return request({
        url: '/system/scheduling/timeslots',
        method: 'get'
      })
    },

    // 获取已排课程
    getScheduledCourses(week) {
      return request({
        url: '/system/scheduling/scheduled',
        method: 'get',
        params: { week }
      })
    },

    // 获取班级列表
    getClasses() {
      return request({
        url: '/system/scheduling/classes',
        method: 'get'
      })
    },

    // 保存排课
    saveSchedule(data) {
      return request({
        url: '/system/scheduling/save',
        method: 'post',
        data
      })
    },

    // 删除排课
    deleteSchedule(data) {
      return request({
        url: '/system/scheduling/delete',
        method: 'post',
        data
      })
    },

    // 获取智能建议
    getSmartSuggestions(week) {
      return request({
        url: '/system/scheduling/suggestions',
        method: 'get',
        params: { week }
      })
    }
  }
}
