<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="教师编号" prop="teacherNo">
        <el-input
          v-model="queryParams.teacherNo"
          placeholder="请输入教师编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          placeholder="请输入工号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable>
          <el-option
            v-for="dict in gender"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="英文姓名" prop="enName">
        <el-input
          v-model="queryParams.enName"
          placeholder="请输入英文姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="民族" prop="ethnicity">
        <el-select v-model="queryParams.ethnicity" placeholder="请选择民族" clearable>
          <el-option
            v-for="dict in nation"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="职称" prop="title">
        <el-select v-model="queryParams.title" placeholder="请选择职称" clearable>
          <el-option
            v-for="dict in job_title"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="单位" prop="departmentCode">
        <el-select v-model="queryParams.departmentCode" placeholder="请选择单位" clearable>
          <el-option
            v-for="dict in management_departments"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否外聘" prop="isExternal">
        <el-select v-model="queryParams.isExternal" placeholder="请选择是否外聘" clearable>
          <el-option
            v-for="dict in sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="教职工类别" prop="staffCategory">
        <el-select v-model="queryParams.staffCategory" placeholder="请选择教职工类别" clearable>
          <el-option
            v-for="dict in faculty_types"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="逻辑删除" prop="isDeleted">
        <el-select v-model="queryParams.isDeleted" placeholder="请选择逻辑删除" clearable>
          <el-option
            v-for="dict in sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="联系电话" prop="telephone">
        <el-input
          v-model="queryParams.telephone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电子邮件" prop="email">
        <el-input
          v-model="queryParams.email"
          placeholder="请输入电子邮件"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:teacher:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:teacher:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:teacher:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:teacher:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="teacherList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="教师编号" align="center" prop="teacherNo" />
      <el-table-column label="工号" align="center" prop="teacherId" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="性别" align="center" prop="gender">
        <template #default="scope">
          <dict-tag :options="gender" :value="scope.row.gender"/>
        </template>
      </el-table-column>
      <el-table-column label="英文姓名" align="center" prop="enName" />
      <el-table-column label="民族" align="center" prop="ethnicity">
        <template #default="scope">
          <dict-tag :options="nation" :value="scope.row.ethnicity"/>
        </template>
      </el-table-column>
      <el-table-column label="职称" align="center" prop="title">
        <template #default="scope">
          <dict-tag :options="job_title" :value="scope.row.title"/>
        </template>
      </el-table-column>
      <el-table-column label="单位" align="center" prop="departmentCode">
        <template #default="scope">
          <dict-tag :options="management_departments" :value="scope.row.departmentCode"/>
        </template>
      </el-table-column>
      <el-table-column label="是否外聘" align="center" prop="isExternal">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isExternal"/>
        </template>
      </el-table-column>
      <el-table-column label="教职工类别" align="center" prop="staffCategory">
        <template #default="scope">
          <dict-tag :options="faculty_types" :value="scope.row.staffCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="逻辑删除" align="center" prop="isDeleted">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isDeleted"/>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="telephone" />
      <el-table-column label="电子邮件" align="center" prop="email" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:teacher:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:teacher:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改教师信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="teacherRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="教师编号" prop="teacherNo">
          <el-input v-model="form.teacherNo" placeholder="请输入教师编号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender" placeholder="请选择性别">
            <el-option
              v-for="dict in gender"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="英文姓名" prop="enName">
          <el-input v-model="form.enName" placeholder="请输入英文姓名" />
        </el-form-item>
        <el-form-item label="民族" prop="ethnicity">
          <el-select v-model="form.ethnicity" placeholder="请选择民族">
            <el-option
              v-for="dict in nation"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="职称" prop="title">
          <el-select v-model="form.title" placeholder="请选择职称">
            <el-option
              v-for="dict in job_title"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位" prop="departmentCode">
          <el-select v-model="form.departmentCode" placeholder="请选择单位">
            <el-option
              v-for="dict in management_departments"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否外聘" prop="isExternal">
          <el-radio-group v-model="form.isExternal">
            <el-radio
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="教职工类别" prop="staffCategory">
          <el-select v-model="form.staffCategory" placeholder="请选择教职工类别">
            <el-option
              v-for="dict in faculty_types"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="逻辑删除" prop="isDeleted">
          <el-radio-group v-model="form.isDeleted">
            <el-radio
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="联系电话" prop="telephone">
          <el-input v-model="form.telephone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="电子邮件" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮件" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Teacher">
import { listTeacher, getTeacher, delTeacher, addTeacher, updateTeacher } from "@/api/system/teacher";

const { proxy } = getCurrentInstance();
const { management_departments, faculty_types, job_title, sys_yes_no, gender, nation } = proxy.useDict('management_departments', 'faculty_types', 'job_title', 'sys_yes_no', 'gender', 'nation');

const teacherList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    teacherNo: null,
    teacherId: null,
    name: null,
    gender: null,
    enName: null,
    ethnicity: null,
    title: null,
    departmentCode: null,
    isExternal: null,
    staffCategory: null,
    isDeleted: null,
    telephone: null,
    email: null
  },
  rules: {
    teacherNo: [
      { required: true, message: "教师编号不能为空", trigger: "blur" }
    ],
    teacherId: [
      { required: true, message: "工号不能为空", trigger: "blur" }
    ],
    name: [
      { required: true, message: "姓名不能为空", trigger: "blur" }
    ],
    gender: [
      { required: true, message: "性别不能为空", trigger: "change" }
    ],
    title: [
      { required: true, message: "职称不能为空", trigger: "change" }
    ],
    departmentCode: [
      { required: true, message: "单位不能为空", trigger: "change" }
    ],
    isExternal: [
      { required: true, message: "是否外聘不能为空", trigger: "change" }
    ],
    staffCategory: [
      { required: true, message: "教职工类别不能为空", trigger: "change" }
    ],
    telephone: [
      { required: true, message: "联系电话不能为空", trigger: "blur" }
    ],
    email: [
      { required: true, message: "电子邮件不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询教师信息列表 */
function getList() {
  loading.value = true;
  listTeacher(queryParams.value).then(response => {
    teacherList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    teacherNo: null,
    teacherId: null,
    name: null,
    gender: null,
    enName: null,
    ethnicity: null,
    title: null,
    departmentCode: null,
    isExternal: null,
    staffCategory: null,
    createTime: null,
    updateTime: null,
    isDeleted: null,
    telephone: null,
    email: null
  };
  proxy.resetForm("teacherRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.teacherId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加教师信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _teacherId = row.teacherId || ids.value
  getTeacher(_teacherId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改教师信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["teacherRef"].validate(valid => {
    if (valid) {
      if (form.value.teacherId != null) {
        updateTeacher(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTeacher(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _teacherIds = row.teacherId || ids.value;
  proxy.$modal.confirm('是否确认删除教师信息编号为"' + _teacherIds + '"的数据项？').then(function() {
    return delTeacher(_teacherIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/teacher/export', {
    ...queryParams.value
  }, `teacher_${new Date().getTime()}.xlsx`)
}

getList();
</script>
