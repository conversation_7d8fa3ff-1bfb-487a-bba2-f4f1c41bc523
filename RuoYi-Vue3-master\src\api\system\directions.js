import request from '@/utils/request'

// 查询专业方向数据列表
export function listDirections(query) {
  return request({
    url: '/system/directions/list',
    method: 'get',
    params: query
  })
}

// 查询专业方向数据详细
export function getDirections(majorDirectionCode) {
  return request({
    url: '/system/directions/' + majorDirectionCode,
    method: 'get'
  })
}

// 新增专业方向数据
export function addDirections(data) {
  return request({
    url: '/system/directions',
    method: 'post',
    data: data
  })
}

// 修改专业方向数据
export function updateDirections(data) {
  return request({
    url: '/system/directions',
    method: 'put',
    data: data
  })
}

// 删除专业方向数据
export function delDirections(majorDirectionCode) {
  return request({
    url: '/system/directions/' + majorDirectionCode,
    method: 'delete'
  })
}
