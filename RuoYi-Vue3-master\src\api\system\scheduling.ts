import request from '@/utils/request'
import type { Course, ClassRoom, TimeSlot, ScheduleItem, Class, WeekInfo, ExportFormat } from '@/types/scheduling'

// 排课管理相关接口
export function useSchedulingApi() {
  return {
    // 获取课程列表
    getCourses(): Promise<Course[]> {
      return request({
        url: '/system/scheduling/courses',
        method: 'get'
      })
    },

    // 获取教室列表
    getClassrooms(): Promise<ClassRoom[]> {
      return request({
        url: '/system/scheduling/classrooms',
        method: 'get'
      })
    },

    // 获取时间槽
    getTimeSlots(): Promise<TimeSlot[]> {
      return request({
        url: '/system/scheduling/timeslots',
        method: 'get'
      })
    },

    // 获取已排课程
    getScheduledCourses(week: number): Promise<ScheduleItem[]> {
      return request({
        url: '/system/scheduling/scheduled',
        method: 'get',
        params: { week }
      })
    },

    // 获取班级列表
    getClasses(): Promise<Class[]> {
      return request({
        url: '/system/scheduling/classes',
        method: 'get'
      })
    },

    // 保存排课
    saveSchedule(data: ScheduleItem): Promise<any> {
      return request({
        url: '/system/scheduling/save',
        method: 'post',
        data
      })
    },

    // 批量保存排课
    saveScheduleBatch(data: ScheduleItem[]): Promise<any> {
      return request({
        url: '/system/scheduling/saveBatch',
        method: 'post',
        data
      })
    },

    // 删除排课
    deleteSchedule(data: {
      classroomId: number,
      timeSlotId: number,
      week: number,
      weekday: number
    }): Promise<any> {
      return request({
        url: '/system/scheduling/delete',
        method: 'post',
        data
      })
    },
    
    // 获取智能建议
    getSmartSuggestions(week: number): Promise<ScheduleItem[]> {
      return request({
        url: '/system/scheduling/suggestions',
        method: 'get',
        params: { week }
      })
    },
    
    // 导出排课表
    exportSchedule(week: number, format: ExportFormat = 'xlsx'): Promise<Blob> {
      return request({
        url: '/system/scheduling/export',
        method: 'get',
        params: { week, format },
        responseType: 'blob'
      })
    },
    
    // 获取周次列表
    getWeekList(): Promise<WeekInfo[]> {
      return request({
        url: '/system/scheduling/weeks',
        method: 'get'
      })
    },
    
    // 复制排课表
    copySchedule(fromWeek: number, toWeek: number): Promise<any> {
      return request({
        url: '/system/scheduling/copy',
        method: 'post',
        data: { fromWeek, toWeek }
      })
    },
    
    // 获取冲突检测结果
    getConflicts(week: number): Promise<any[]> {
      return request({
        url: '/system/scheduling/conflicts',
        method: 'get',
        params: { week }
      })
    }
  }
} 