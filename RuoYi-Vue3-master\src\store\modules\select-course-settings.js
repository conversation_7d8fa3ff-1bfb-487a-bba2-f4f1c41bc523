import { defineStore } from 'pinia'

// 排课设置store
export const useSelectCourseSettingsStore = defineStore('selectCourseSettings', {
  // 状态数据
  state: () => ({
    // 基本设置
    basicSettings: {
      minUnit: 'teachingClass', // 排课最小单位
      clearBeforeSchedule: false, // 自动排课前清除已排课程
      autoAssignClassroom: true, // 自动排课时安排教室
      useFixedClassroom: false, // 启用班级固定教室
      allowEveningClasses: true, // 允许晚上排课
      enableSelectiveCourses: false, // 勾选课程优先排课
      centralizeClassRooms: true, // 班级教室尽量集中
      centralizeTeacherRooms: true, // 教师教室尽量集中
      sameCourseUsesSameRoom: true, // 同一课程使用相同教室
      enforceRoomCapacity: true // 班级人数小于教室容量
    },

    // 时间规则设置
    timeSettings: {
      continuousRule: '2', // 连排规则
      oddHoursHandle: 'split', // 奇数学时处理方式
      teacherMaxDailyHours: 6, // 教师每日最大课时
      teacherMaxWeeklyHours: 20, // 教师每周最大课时
      teacherMaxMorningHours: 3, // 教师上午最大课时
      teacherMaxAfternoonHours: 3 // 教师下午最大课时
    },

    // 特殊课程规则
    specialCourseSettings: {
      peOnlyAfternoon: true, // 体育课仅安排在下午
      noClassAfterPE: true, // 体育课后不安排课程
      labOnlyEvening: false, // 实验课仅安排在晚上
      multiHoursContinuous: true // 多学时课程连续排课
    },

    // 优先级设置
    prioritySettings: {
      priorities: [
        { id: 1, label: '学生所在院系优先' },
        { id: 2, label: '开课院系优先' },
        { id: 3, label: '课程性质优先' },
        { id: 4, label: '学时类型优先' },
        { id: 5, label: '课程类型优先' }
      ]
    },

    // 禁排设置
    restrictionSettings: {
      // 学校禁排
      schoolRestrictedTime: [],
      // 教师禁排
      selectedTeachers: [], 
      teacherRestrictedTime: [],
      // 学生禁排
      selectedClasses: [], 
      studentRestrictedTime: [],
      // 课程禁排
      selectedCourses: [], 
      courseRestrictedTime: [],
      // 教室禁排
      selectedClassrooms: [], 
      classroomRestrictedTime: [],
      // 角色禁排
      selectedRoles: [], 
      roleRestrictedTime: []
    },

    // 模拟数据 - 在实际应用中可能会从API获取
    teachers: [
      { id: 1, name: '张老师' },
      { id: 2, name: '李老师' },
      { id: 3, name: '王老师' }
    ],

    classrooms: [
      { id: 1, name: 'A101' },
      { id: 2, name: 'B203' },
      { id: 3, name: 'C305' }
    ],

    classes: [
      { id: 1, name: '计算机2301班' },
      { id: 2, name: '软件工程2302班' },
      { id: 3, name: '数据科学2301班' }
    ],

    courses: [
      { id: 1, name: '高等数学' },
      { id: 2, name: '大学物理' },
      { id: 3, name: '程序设计基础' }
    ],

    roles: [
      { id: 1, name: '教学秘书' },
      { id: 2, name: '教研室主任' },
      { id: 3, name: '系主任' }
    ],

    // 选择性排课数据
    selectiveCourses: [
      { id: 1, courseCode: 'CS101', courseName: '计算机导论', teacher: '张老师', classInfo: '计算机2301班', hours: 32 },
      { id: 2, courseCode: 'MA101', courseName: '高等数学', teacher: '李老师', classInfo: '软件工程2302班', hours: 48 },
      { id: 3, courseCode: 'PH101', courseName: '大学物理', teacher: '王老师', classInfo: '数据科学2301班', hours: 40 }
    ],

    // 标记设置是否被修改
    isSettingsModified: false
  }),

  // getter方法
  getters: {
    // 获取所有设置作为一个对象
    allSettings: (state) => {
      return {
        basicSettings: state.basicSettings,
        timeSettings: state.timeSettings,
        specialCourseSettings: state.specialCourseSettings,
        prioritySettings: state.prioritySettings,
        restrictionSettings: state.restrictionSettings
      }
    },
    
    // 获取选中的选择性排课课程
    selectedCourses: (state) => {
      return state.selectiveCourses.filter(course => course.selected)
    }
  },

  // actions方法
  actions: {
    // 保存所有设置
    saveSettings() {
      // 这里可以添加API调用来保存设置到后端
      console.log('保存设置：', this.allSettings)
      
      // 重置修改标记
      this.isSettingsModified = false
      
      // 返回Promise以便组件知道保存完成
      return Promise.resolve('设置保存成功')
    },

    // 重置所有设置为默认值
    resetSettings() {
      // 重置为初始状态
      this.$reset()
      
      // 返回Promise以便组件知道重置完成
      return Promise.resolve('已重置所有设置')
    },

    // 更新基本设置
    updateBasicSettings(newSettings) {
      this.basicSettings = { ...this.basicSettings, ...newSettings }
      this.isSettingsModified = true
    },

    // 更新时间规则设置
    updateTimeSettings(newSettings) {
      this.timeSettings = { ...this.timeSettings, ...newSettings }
      this.isSettingsModified = true
    },

    // 更新特殊课程规则
    updateSpecialCourseSettings(newSettings) {
      this.specialCourseSettings = { ...this.specialCourseSettings, ...newSettings }
      this.isSettingsModified = true
    },

    // 更新优先级设置
    updatePrioritySettings(newSettings) {
      this.prioritySettings = { ...this.prioritySettings, ...newSettings }
      this.isSettingsModified = true
    },

    // 更新禁排设置
    updateRestrictionSettings(newSettings) {
      this.restrictionSettings = { ...this.restrictionSettings, ...newSettings }
      this.isSettingsModified = true
    },

    // 优先级上移
    movePriorityUp(index) {
      if (index > 0) {
        const temp = this.prioritySettings.priorities[index]
        this.prioritySettings.priorities[index] = this.prioritySettings.priorities[index - 1]
        this.prioritySettings.priorities[index - 1] = temp
        this.isSettingsModified = true
      }
    },

    // 优先级下移
    movePriorityDown(index) {
      if (index < this.prioritySettings.priorities.length - 1) {
        const temp = this.prioritySettings.priorities[index]
        this.prioritySettings.priorities[index] = this.prioritySettings.priorities[index + 1]
        this.prioritySettings.priorities[index + 1] = temp
        this.isSettingsModified = true
      }
    },

    // 勾选课程优先排课
    scheduleSelectedCourses() {
      // 这里可以添加API调用来执行优先排课
      console.log('执行优先排课', this.selectedCourses)
      
      // 返回Promise以便组件知道操作完成
      return Promise.resolve('已开始优先排课')
    },

    // 根据ID查找教师
    findTeacher(id) {
      return this.teachers.find(t => t.id === id)
    },

    // 根据ID查找班级
    findClass(id) {
      return this.classes.find(c => c.id === id)
    },

    // 根据ID查找课程
    findCourse(id) {
      return this.courses.find(c => c.id === id)
    },

    // 根据ID查找教室
    findClassroom(id) {
      return this.classrooms.find(c => c.id === id)
    },

    // 根据ID查找角色
    findRole(id) {
      return this.roles.find(r => r.id === id)
    },

    // 添加教师禁排时间
    addTeacherRestriction(teacherId, timeSlots) {
      const teacherIndex = this.restrictionSettings.selectedTeachers.indexOf(teacherId)
      if (teacherIndex === -1) {
        this.restrictionSettings.selectedTeachers.push(teacherId)
      }
      
      // 合并时间段，避免重复
      this.restrictionSettings.teacherRestrictedTime = [
        ...new Set([...this.restrictionSettings.teacherRestrictedTime, ...timeSlots])
      ]
      
      this.isSettingsModified = true
    },

    // 添加班级禁排时间
    addClassRestriction(classId, timeSlots) {
      const classIndex = this.restrictionSettings.selectedClasses.indexOf(classId)
      if (classIndex === -1) {
        this.restrictionSettings.selectedClasses.push(classId)
      }
      
      // 合并时间段，避免重复
      this.restrictionSettings.studentRestrictedTime = [
        ...new Set([...this.restrictionSettings.studentRestrictedTime, ...timeSlots])
      ]
      
      this.isSettingsModified = true
    }
  }
})
